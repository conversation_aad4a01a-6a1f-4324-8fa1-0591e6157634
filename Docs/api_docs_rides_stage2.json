{"openapi": "3.1.0", "info": {"title": "<PERSON>s", "description": "Backend API for St. Lucia Ride-Share MVP - Tourist-focused ride-sharing platform with fixed pricing", "version": "1.0.0"}, "paths": {"/api/v1/rides/request": {"post": {"tags": ["rides", "rides"], "summary": "Request Ride", "description": "Create a new ride request.\n\nThis endpoint allows riders to request a new ride by providing pickup and\ndropoff locations. The system will automatically find and notify nearby drivers.\n\n**Required permissions**: Rider\n\n**Process**:\n1. Validates rider and route\n2. Calculates pricing\n3. Creates ride request\n4. Starts driver matching process", "operationId": "request_ride_api_v1_rides_request_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RideRequestCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RideResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/rides/history": {"get": {"tags": ["rides", "rides"], "summary": "Get Ride History", "description": "Get ride history for the current user.\n\nReturns completed rides with details about the other party (driver/rider),\nratings, and trip information.\n\n**Required permissions**: Rider or Driver", "operationId": "get_ride_history_api_v1_rides_history_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "Number of results to return", "default": 20, "title": "Limit"}, "description": "Number of results to return"}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "description": "Number of results to skip", "default": 0, "title": "Offset"}, "description": "Number of results to skip"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RideHistoryResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/rides/{ride_id}/cancel": {"post": {"tags": ["rides", "rides"], "summary": "Cancel Ride", "description": "Cancel a ride request.\n\nAllows riders and drivers to cancel rides that are in requested or accepted status.\nProper notifications are sent to the other party.\n\n**Required permissions**: Rider or Driver (must be participant in the ride)", "operationId": "cancel_ride_api_v1_rides__ride_id__cancel_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "ride_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Ride Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RideCancellationRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__SuccessResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/rides/{ride_id}/status": {"get": {"tags": ["rides", "rides"], "summary": "Get Ride Status", "description": "Get current ride status and details.\n\nReturns complete ride information including location, status, and timing.\n\n**Required permissions**: Rider or Driver (must be participant in the ride)", "operationId": "get_ride_status_api_v1_rides__ride_id__status_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "ride_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Ride Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RideResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["rides", "rides"], "summary": "Update Ride Status", "description": "Update ride status.\n\nAllows drivers to update ride status through the ride lifecycle\n(accepted -> in_progress -> completed).\n\n**Required permissions**: Driver (must be assigned to the ride)", "operationId": "update_ride_status_api_v1_rides__ride_id__status_put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "ride_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Ride Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RideStatusUpdateRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RideResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/rides/{ride_id}/receipt": {"get": {"tags": ["rides", "rides"], "summary": "Get Ride Receipt", "description": "Generate and retrieve ride receipt.\n\nOnly available for completed rides. Returns detailed receipt with\nfare breakdown and trip information.\n\n**Required permissions**: Rider or Driver (must be participant in the ride)", "operationId": "get_ride_receipt_api_v1_rides__ride_id__receipt_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "ride_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Ride Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RideReceiptResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/rides/available": {"get": {"tags": ["rides", "rides"], "summary": "Get Available Rides", "description": "Get available ride requests for drivers.\n\nReturns ride requests within the specified radius that are looking for drivers.\n\n**Required permissions**: Driver", "operationId": "get_available_rides_api_v1_rides_available_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "radius_km", "in": "query", "required": false, "schema": {"type": "number", "maximum": 50, "minimum": 1, "description": "Search radius in kilometers", "default": 5, "title": "<PERSON><PERSON>"}, "description": "Search radius in kilometers"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverAvailableRidesResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/rides/{ride_id}/accept": {"post": {"tags": ["rides", "rides"], "summary": "Accept Ride", "description": "Accept a ride request.\n\nAllows drivers to accept ride requests. This assigns the driver to the ride\nand notifies the rider.\n\n**Required permissions**: Driver", "operationId": "accept_ride_api_v1_rides__ride_id__accept_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "ride_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Ride Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RideAcceptanceRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RideResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/rides/active": {"get": {"tags": ["rides", "rides"], "summary": "Get Active Ride", "description": "Get current active ride for the user.\n\nReturns the user's current active ride (requested, accepted, or in_progress).\n\n**Required permissions**: Rider or Driver", "operationId": "get_active_ride_api_v1_rides_active_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RideResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/rides/stats": {"get": {"tags": ["rides", "rides"], "summary": "Get Ride Stats", "description": "Get ride statistics for the current user.\n\nReturns comprehensive statistics including total rides, completion rate,\naverage rating, and earnings (for drivers).\n\n**Required permissions**: Rider or Driver", "operationId": "get_ride_stats_api_v1_rides_stats_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RideStatsResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/rides/search": {"post": {"tags": ["rides", "rides"], "summary": "Search Rides", "description": "Search rides with filters.\n\nAdvanced search functionality with multiple filters including date range,\nstatus, location, and fare range.\n\n**Required permissions**: Rider or Driver", "operationId": "search_rides_api_v1_rides_search_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RideSearchRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RideSearchResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/rides/{ride_id}": {"get": {"tags": ["rides", "rides"], "summary": "Get Ride Details", "description": "Get complete ride details.\n\nReturns comprehensive ride information including all locations, timing,\nand status information.\n\n**Required permissions**: Rider or Driver (must be participant in the ride)", "operationId": "get_ride_details_api_v1_rides__ride_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "ride_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Ride Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RideResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/rides/pricing/routes": {"get": {"tags": ["rides", "rides"], "summary": "Get Available Routes", "description": "Get all available routes with pricing.\n\nReturns all predefined routes with their fixed pricing information.\n\n**Required permissions**: Rider or Driver", "operationId": "get_available_routes_api_v1_rides_pricing_routes_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Get Available Routes Api V1 Rides Pricing Routes Get"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/rides/pricing/calculate": {"get": {"tags": ["rides", "rides"], "summary": "Calculate Ride Price", "description": "Calculate price for a specific route.\n\nReturns pricing quote for the specified pickup and dropoff locations.\n\n**Required permissions**: Rider or Driver", "operationId": "calculate_ride_price_api_v1_rides_pricing_calculate_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "pickup", "in": "query", "required": true, "schema": {"type": "string", "description": "Pickup location name", "title": "Pickup"}, "description": "Pickup location name"}, {"name": "dropoff", "in": "query", "required": true, "schema": {"type": "string", "description": "Dropoff location name", "title": "Dropoff"}, "description": "Dropoff location name"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Calculate Ride Price Api V1 Rides Pricing Calculate Get"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/rides/{ride_id}/confirm-pickup": {"post": {"tags": ["rides", "rides"], "summary": "Confirm Pickup", "description": "Confirm passenger pickup.\n\nOnly drivers can confirm pickup.\nThis updates the ride status to 'started' and sends notifications.\n\nArgs:\n    ride_id: The ride ID\n    current_user: Current authenticated user (must be driver)\n    \nReturns:\n    Updated ride information\n    \nRaises:\n    HTTPException: If user is not authorized, ride not found, or confirmation fails", "operationId": "confirm_pickup_api_v1_rides__ride_id__confirm_pickup_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "ride_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Ride Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RideResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/rides/{ride_id}/confirm-dropoff": {"post": {"tags": ["rides", "rides"], "summary": "Confirm Dropoff", "description": "Confirm passenger dropoff.\n\nOnly drivers can confirm dropoff.\nThis updates the ride status to 'completed' and sends notifications.\n\nArgs:\n    ride_id: The ride ID\n    current_user: Current authenticated user (must be driver)\n    \nReturns:\n    Updated ride information\n    \nRaises:\n    HTTPException: If user is not authorized, ride not found, or confirmation fails", "operationId": "confirm_dropoff_api_v1_rides__ride_id__confirm_dropoff_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "ride_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Ride Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RideResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/rides/{ride_id}/generate-receipt": {"post": {"tags": ["rides", "rides"], "summary": "Generate Receipt", "description": "Generate a receipt for a completed ride.\n\nArgs:\n    ride_id: The ride ID\n    current_user: Current authenticated user\n    \nReturns:\n    Dictionary containing receipt data\n    \nRaises:\n    HTTPException: If receipt generation fails", "operationId": "generate_receipt_api_v1_rides__ride_id__generate_receipt_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "ride_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Ride Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Generate Receipt Api V1 Rides  Ride Id  Generate Receipt Post"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/rides/receipts": {"get": {"tags": ["rides", "rides"], "summary": "Get User Receipts", "operationId": "get_user_receipts_api_v1_rides_receipts_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "Number of results to return", "default": 20, "title": "Limit"}, "description": "Number of results to return"}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "description": "Number of results to skip", "default": 0, "title": "Offset"}, "description": "Number of results to skip"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Get User Receipts Api V1 Rides Receipts Get"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__ride__ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}}