import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:lucian_rides_app/presentation/widgets/interactive_map_widget.dart';
import 'package:lucian_rides_app/domain/entities/location_models.dart';
import 'package:lucian_rides_app/domain/entities/ride.dart';

/// Example demonstrating how to use the InteractiveMapWidget
class InteractiveMapExample extends ConsumerStatefulWidget {
  const InteractiveMapExample({super.key});

  @override
  ConsumerState<InteractiveMapExample> createState() =>
      _InteractiveMapExampleState();
}

class _InteractiveMapExampleState extends ConsumerState<InteractiveMapExample> {
  // Current user location (St. Lucia center)
  final LocationCoordinates currentLocation = const LocationCoordinates(
    latitude: 13.9094,
    longitude: -60.9789,
  );

  // Example pickup location (Rodney Bay)
  RideLocation? pickupLocation = const RideLocation(
    name: 'Rodney Bay Marina',
    address: 'Rodney Bay, Gros Islet, St. Lucia',
    latitude: 14.0728,
    longitude: -60.9560,
  );

  // Example dropoff location (Castries)
  RideLocation? dropoffLocation = const RideLocation(
    name: 'Castries Market',
    address: 'Jeremie Street, Castries, St. Lucia',
    latitude: 14.0101,
    longitude: -60.9875,
  );

  bool showRoute = true;
  bool enableMarkerDragging = true;
  bool showCurrentLocation = true;
  bool showControls = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Interactive Map Example'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Column(
        children: [
          // Controls panel
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: SwitchListTile(
                        title: const Text('Show Route'),
                        value: showRoute,
                        onChanged: (value) {
                          setState(() {
                            showRoute = value;
                          });
                        },
                      ),
                    ),
                    Expanded(
                      child: SwitchListTile(
                        title: const Text('Enable Dragging'),
                        value: enableMarkerDragging,
                        onChanged: (value) {
                          setState(() {
                            enableMarkerDragging = value;
                          });
                        },
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    Expanded(
                      child: SwitchListTile(
                        title: const Text('Show Current Location'),
                        value: showCurrentLocation,
                        onChanged: (value) {
                          setState(() {
                            showCurrentLocation = value;
                          });
                        },
                      ),
                    ),
                    Expanded(
                      child: SwitchListTile(
                        title: const Text('Show Controls'),
                        value: showControls,
                        onChanged: (value) {
                          setState(() {
                            showControls = value;
                          });
                        },
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          setState(() {
                            pickupLocation = null;
                          });
                        },
                        child: const Text('Clear Pickup'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          setState(() {
                            dropoffLocation = null;
                          });
                        },
                        child: const Text('Clear Dropoff'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Map widget
          Expanded(
            child: InteractiveMapWidget(
              currentLocation: currentLocation,
              pickupLocation: pickupLocation,
              dropoffLocation: dropoffLocation,
              showRoute: showRoute,
              enableMarkerDragging: enableMarkerDragging,
              showCurrentLocation: showCurrentLocation,
              showControls: showControls,
              expandToFill: true,
              onPickupLocationChanged: (location) {
                setState(() {
                  pickupLocation = RideLocation(
                    name: 'Pickup Location',
                    latitude: location.latitude,
                    longitude: location.longitude,
                  );
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'Pickup location updated: ${location.latitude.toStringAsFixed(4)}, ${location.longitude.toStringAsFixed(4)}',
                    ),
                  ),
                );
              },
              onDropoffLocationChanged: (location) {
                setState(() {
                  dropoffLocation = RideLocation(
                    name: 'Dropoff Location',
                    latitude: location.latitude,
                    longitude: location.longitude,
                  );
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'Dropoff location updated: ${location.latitude.toStringAsFixed(4)}, ${location.longitude.toStringAsFixed(4)}',
                    ),
                  ),
                );
              },
              onMapTap: (position) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'Map tapped at: ${position.latitude.toStringAsFixed(4)}, ${position.longitude.toStringAsFixed(4)}',
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

/// Example app to run the interactive map example
class InteractiveMapExampleApp extends StatelessWidget {
  const InteractiveMapExampleApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ProviderScope(
      child: MaterialApp(
        title: 'Interactive Map Example',
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(seedColor: Colors.teal),
          useMaterial3: true,
        ),
        home: const InteractiveMapExample(),
      ),
    );
  }
}

void main() {
  runApp(const InteractiveMapExampleApp());
}
