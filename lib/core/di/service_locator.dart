import 'package:get_it/get_it.dart';
import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter/foundation.dart'; // Added for debugPrint

import '../constants/app_constants.dart';
import '../../data/datasources/api_client.dart';
import '../../data/datasources/ride_api_client.dart';
import '../../data/datasources/storage_service.dart';
import '../../data/repositories/auth_repository_impl.dart';
import '../../data/repositories/user_repository_impl.dart';
import '../../data/repositories/profile_repository_impl.dart';
import '../../data/repositories/ride_repository_impl.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../domain/repositories/user_repository.dart';
import '../../domain/repositories/profile_repository.dart';
import '../../domain/repositories/ride_repository.dart';
import '../../services/auth/auth_service.dart';
import '../../services/auth/auth_service_impl.dart';
import '../../services/location/location_service.dart';
import '../../services/location/location_service_impl.dart';
import '../../services/sync/data_sync_service.dart';
import '../../services/sync/offline_data_service.dart';
import '../../services/network/network_service.dart';
import '../../services/location/location_fallback_service.dart';
import '../../core/errors/global_error_handler.dart';
import '../../data/repositories/offline_aware_ride_repository.dart';

final GetIt getIt = GetIt.instance;

Future<void> setupServiceLocator() async {
  // External dependencies
  getIt.registerLazySingleton<Dio>(() {
    final dio = Dio();
    dio.options.baseUrl = AppConstants.baseUrl;
    return dio;
  });

  getIt.registerLazySingleton<FlutterSecureStorage>(
    () => const FlutterSecureStorage(
      aOptions: AndroidOptions(encryptedSharedPreferences: true),
      iOptions: IOSOptions(
        accessibility: KeychainAccessibility.first_unlock_this_device,
      ),
    ),
  );

  // Core services
  getIt.registerLazySingleton<StorageService>(
    () => SecureStorageService(getIt<FlutterSecureStorage>()),
  );

  getIt.registerLazySingleton<NetworkService>(() => NetworkServiceImpl());

  // Register LocationService with proper initialization
  getIt.registerLazySingleton<LocationService>(() {
    final locationService = LocationServiceImpl(
      storageService: getIt<StorageService>(),
    );
    // Initialize the location service asynchronously
    locationService.initialize().then((result) {
      result.fold(
        (error) => debugPrint(
          'Failed to initialize location service: ${error.message}',
        ),
        (success) => debugPrint('Location service initialized successfully'),
      );
    });
    return locationService;
  });

  getIt.registerLazySingleton<ApiClient>(
    () => DioApiClient(
      getIt<Dio>(),
      getIt<StorageService>(),
      networkService: getIt<NetworkService>(),
    ),
  );

  getIt.registerLazySingleton<RideApiClient>(
    () => RideApiClientImpl(getIt<ApiClient>()),
  );

  // Repositories
  getIt.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(
      apiClient: getIt<ApiClient>(),
      storageService: getIt<StorageService>(),
    ),
  );

  getIt.registerLazySingleton<UserRepository>(
    () => UserRepositoryImpl(
      apiClient: getIt<ApiClient>(),
      storageService: getIt<StorageService>(),
    ),
  );

  getIt.registerLazySingleton<ProfileRepository>(
    () => ProfileRepositoryImpl(
      apiClient: getIt<ApiClient>(),
      storageService: getIt<StorageService>(),
    ),
  );

  // Register the base ride repository implementation first
  getIt.registerLazySingleton<RideRepositoryImpl>(
    () => RideRepositoryImpl(
      rideApiClient: getIt<RideApiClient>(),
      storageService: getIt<StorageService>(),
      networkService: getIt<NetworkService>(),
    ),
  );

  // Register offline data service
  getIt.registerLazySingleton<OfflineDataService>(
    () => OfflineDataServiceImpl(
      storageService: getIt<StorageService>(),
      networkService: getIt<NetworkService>(),
    ),
  );

  // Register offline-aware ride repository as the main RideRepository
  getIt.registerLazySingleton<RideRepository>(
    () => OfflineAwareRideRepository(
      onlineRepository: getIt<RideRepositoryImpl>(),
      networkService: getIt<NetworkService>(),
      offlineDataService: getIt<OfflineDataService>(),
    ),
  );

  // Register location fallback service
  getIt.registerLazySingleton<LocationFallbackService>(
    () =>
        LocationFallbackServiceImpl(locationService: getIt<LocationService>()),
  );

  // Register global error handler
  getIt.registerLazySingleton<GlobalErrorHandler>(
    () => GlobalErrorHandlerImpl.instance,
  );

  // Services
  getIt.registerLazySingleton<AuthService>(
    () => AuthServiceImpl(
      authRepository: getIt<AuthRepository>(),
      storageService: getIt<StorageService>(),
    ),
  );

  getIt.registerLazySingleton<DataSyncService>(
    () => DataSyncServiceImpl(
      storageService: getIt<StorageService>(),
      apiClient: getIt<ApiClient>(),
      networkService: getIt<NetworkService>(),
      rideRepository: getIt<RideRepository>(),
      offlineDataService: getIt<OfflineDataService>(),
    ),
  );

  // Initialize global error handler
  getIt<GlobalErrorHandler>().initialize();
}
