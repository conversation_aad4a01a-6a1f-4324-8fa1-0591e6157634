import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'app_error.dart';
import 'error_handler.dart';
import 'retry_handler.dart';

/// Global error handler service for managing application-wide error handling
abstract class GlobalErrorHandler {
  /// Initialize the global error handler
  void initialize();

  /// Handle an error globally
  Future<void> handleError(
    AppError error, {
    String? context,
    bool showToUser = true,
    bool logError = true,
  });

  /// Handle an error with retry capability
  Future<T?> handleErrorWithRetry<T>(
    Future<T> Function() operation, {
    String? operationName,
    RetryConfig? retryConfig,
    bool showErrorToUser = true,
  });

  /// Show error to user via UI
  void showErrorToUser(AppError error, {String? context});

  /// Log error for debugging/monitoring
  void logError(AppError error, {String? context, StackTrace? stackTrace});

  /// Register error callback for custom handling
  void registerErrorCallback(void Function(AppError error) callback);

  /// Unregister error callback
  void unregisterErrorCallback(void Function(AppError error) callback);

  /// Get error statistics
  ErrorStatistics getErrorStatistics();

  /// Clear error statistics
  void clearErrorStatistics();

  /// Dispose resources
  void dispose();
}

/// Implementation of global error handler
class GlobalErrorHandlerImpl implements GlobalErrorHandler {
  static GlobalErrorHandlerImpl? _instance;
  static GlobalErrorHandlerImpl get instance {
    _instance ??= GlobalErrorHandlerImpl._();
    return _instance!;
  }

  GlobalErrorHandlerImpl._();

  final List<void Function(AppError error)> _errorCallbacks = [];
  final ErrorStatistics _statistics = ErrorStatistics();
  bool _isInitialized = false;

  @override
  void initialize() {
    if (_isInitialized) return;

    // Set up Flutter error handling
    FlutterError.onError = (FlutterErrorDetails details) {
      final error = AppError.unknown(
        message: 'Flutter error: ${details.exception}',
        exception: details.exception,
      );
      handleError(error, context: 'Flutter Framework', logError: true);
    };

    // Set up platform dispatcher error handling
    PlatformDispatcher.instance.onError = (error, stack) {
      final appError = AppError.unknown(
        message: 'Platform error: $error',
        exception: error,
      );
      handleError(appError, context: 'Platform', logError: true);
      return true;
    };

    _isInitialized = true;
    debugPrint('GlobalErrorHandler initialized');
  }

  @override
  Future<void> handleError(
    AppError error, {
    String? context,
    bool showToUser = true,
    bool logError = true,
  }) async {
    // Update statistics
    _statistics.recordError(error);

    // Log error if requested
    if (logError) {
      this.logError(error, context: context);
    }

    // Show to user if requested
    if (showToUser) {
      showErrorToUser(error, context: context);
    }

    // Notify registered callbacks
    for (final callback in _errorCallbacks) {
      try {
        callback(error);
      } catch (e) {
        debugPrint('Error in error callback: $e');
      }
    }
  }

  @override
  Future<T?> handleErrorWithRetry<T>(
    Future<T> Function() operation, {
    String? operationName,
    RetryConfig? retryConfig,
    bool showErrorToUser = true,
  }) async {
    try {
      final result = await RetryHandler.execute(
        operation,
        config: retryConfig ?? RetryConfig.network,
        operationName: operationName,
      );

      if (result.isSuccess) {
        return result.data;
      } else {
        await handleError(
          result.error!,
          context: operationName,
          showToUser: showErrorToUser,
        );
        return null;
      }
    } catch (e) {
      final error = ErrorHandler.handleError(e);
      await handleError(
        error,
        context: operationName,
        showToUser: showErrorToUser,
      );
      return null;
    }
  }

  @override
  void showErrorToUser(AppError error, {String? context}) {
    // This will be handled by the UI layer through providers
    // The actual implementation depends on the current navigation context
    debugPrint('Showing error to user: ${error.message}');
  }

  @override
  void logError(AppError error, {String? context, StackTrace? stackTrace}) {
    final contextStr = context != null ? '[$context] ' : '';

    if (kDebugMode) {
      debugPrint('${contextStr}Error: ${error.message}');

      error.when(
        network: (message, details) {
          if (details != null) debugPrint('Details: $details');
        },
        authentication: (message, errorCode) {
          if (errorCode != null) debugPrint('Error Code: $errorCode');
        },
        validation: (message, fieldErrors) {
          if (fieldErrors.isNotEmpty) {
            debugPrint('Field Errors: $fieldErrors');
          }
        },
        server: (message, statusCode) {
          debugPrint('Status Code: $statusCode');
        },
        unknown: (message, exception) {
          if (exception != null) debugPrint('Exception: $exception');
          if (stackTrace != null) debugPrint('Stack Trace: $stackTrace');
        },
      );
    }

    // In production, you would send this to a logging service
    // like Firebase Crashlytics, Sentry, etc.
  }

  @override
  void registerErrorCallback(void Function(AppError error) callback) {
    _errorCallbacks.add(callback);
  }

  @override
  void unregisterErrorCallback(void Function(AppError error) callback) {
    _errorCallbacks.remove(callback);
  }

  @override
  ErrorStatistics getErrorStatistics() {
    return _statistics;
  }

  @override
  void clearErrorStatistics() {
    _statistics.clear();
  }

  @override
  void dispose() {
    _errorCallbacks.clear();
    _statistics.clear();
    _isInitialized = false;
  }
}

/// Statistics about errors that have occurred
class ErrorStatistics {
  final Map<Type, int> _errorCounts = {};
  final List<ErrorRecord> _recentErrors = [];
  static const int _maxRecentErrors = 50;

  /// Record an error occurrence
  void recordError(AppError error) {
    final errorType = error.runtimeType;
    _errorCounts[errorType] = (_errorCounts[errorType] ?? 0) + 1;

    _recentErrors.add(ErrorRecord(error: error, timestamp: DateTime.now()));

    // Keep only recent errors
    if (_recentErrors.length > _maxRecentErrors) {
      _recentErrors.removeAt(0);
    }
  }

  /// Get total error count
  int get totalErrors =>
      _errorCounts.values.fold(0, (sum, count) => sum + count);

  /// Get error count by type
  int getErrorCount(Type errorType) => _errorCounts[errorType] ?? 0;

  /// Get all error counts by type
  Map<Type, int> get errorCountsByType => Map.unmodifiable(_errorCounts);

  /// Get recent errors
  List<ErrorRecord> get recentErrors => List.unmodifiable(_recentErrors);

  /// Get errors from the last hour
  List<ErrorRecord> get errorsLastHour {
    final oneHourAgo = DateTime.now().subtract(const Duration(hours: 1));
    return _recentErrors
        .where((record) => record.timestamp.isAfter(oneHourAgo))
        .toList();
  }

  /// Get most common error type
  Type? get mostCommonErrorType {
    if (_errorCounts.isEmpty) return null;

    return _errorCounts.entries.reduce((a, b) => a.value > b.value ? a : b).key;
  }

  /// Clear all statistics
  void clear() {
    _errorCounts.clear();
    _recentErrors.clear();
  }

  @override
  String toString() {
    return 'ErrorStatistics(totalErrors: $totalErrors, '
        'errorTypes: ${_errorCounts.keys.length}, '
        'recentErrors: ${_recentErrors.length})';
  }
}

/// Record of an error occurrence
class ErrorRecord {
  final AppError error;
  final DateTime timestamp;

  const ErrorRecord({required this.error, required this.timestamp});

  @override
  String toString() {
    return 'ErrorRecord(error: ${error.message}, timestamp: $timestamp)';
  }
}

/// Provider for global error handler
final globalErrorHandlerProvider = Provider<GlobalErrorHandler>((ref) {
  return GlobalErrorHandlerImpl.instance;
});

/// Provider for error statistics
final errorStatisticsProvider = Provider<ErrorStatistics>((ref) {
  final errorHandler = ref.read(globalErrorHandlerProvider);
  return errorHandler.getErrorStatistics();
});

/// Mixin for widgets that need error handling capabilities
mixin ErrorHandlingMixin<T extends ConsumerStatefulWidget> on ConsumerState<T> {
  /// Handle an error with optional retry
  Future<R?> handleError<R>(
    Future<R> Function() operation, {
    String? operationName,
    bool showError = true,
    RetryConfig? retryConfig,
  }) async {
    final errorHandler = ref.read(globalErrorHandlerProvider);
    return await errorHandler.handleErrorWithRetry(
      operation,
      operationName: operationName ?? T.toString(),
      retryConfig: retryConfig,
      showErrorToUser: showError,
    );
  }

  /// Show an error to the user
  void showError(AppError error, {String? context}) {
    final errorHandler = ref.read(globalErrorHandlerProvider);
    errorHandler.showErrorToUser(error, context: context);
  }

  /// Log an error
  void logError(AppError error, {String? context, StackTrace? stackTrace}) {
    final errorHandler = ref.read(globalErrorHandlerProvider);
    errorHandler.logError(error, context: context, stackTrace: stackTrace);
  }
}

/// Extension for easy error handling in providers
extension ProviderErrorHandling on Ref {
  /// Handle an error with the global error handler
  Future<void> handleError(
    AppError error, {
    String? context,
    bool showToUser = true,
  }) async {
    final errorHandler = read(globalErrorHandlerProvider);
    await errorHandler.handleError(
      error,
      context: context,
      showToUser: showToUser,
    );
  }

  /// Handle an operation with retry capability
  Future<T?> handleWithRetry<T>(
    Future<T> Function() operation, {
    String? operationName,
    RetryConfig? retryConfig,
  }) async {
    final errorHandler = read(globalErrorHandlerProvider);
    return await errorHandler.handleErrorWithRetry(
      operation,
      operationName: operationName,
      retryConfig: retryConfig,
    );
  }
}
