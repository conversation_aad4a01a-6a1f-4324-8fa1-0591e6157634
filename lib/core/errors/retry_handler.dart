import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';

import 'app_error.dart';
import 'error_extensions.dart';
import 'error_handler.dart';

/// Configuration for retry behavior
class RetryConfig {
  /// Maximum number of retry attempts
  final int maxAttempts;

  /// Base delay between retries in milliseconds
  final int baseDelayMs;

  /// Maximum delay between retries in milliseconds
  final int maxDelayMs;

  /// Multiplier for exponential backoff
  final double backoffMultiplier;

  /// Whether to add jitter to delay calculations
  final bool useJitter;

  /// Custom function to determine if an error should be retried
  final bool Function(AppError error)? shouldRetry;

  const RetryConfig({
    this.maxAttempts = 3,
    this.baseDelayMs = 1000,
    this.maxDelayMs = 30000,
    this.backoffMultiplier = 2.0,
    this.useJitter = true,
    this.shouldRetry,
  });

  /// Default retry configuration for network operations
  static const network = RetryConfig(
    maxAttempts: 3,
    baseDelayMs: 1000,
    maxDelayMs: 10000,
    backoffMultiplier: 2.0,
    useJitter: true,
  );

  /// Retry configuration for critical operations
  static const critical = RetryConfig(
    maxAttempts: 5,
    baseDelayMs: 500,
    maxDelayMs: 15000,
    backoffMultiplier: 1.5,
    useJitter: true,
  );

  /// Retry configuration for background operations
  static const background = RetryConfig(
    maxAttempts: 10,
    baseDelayMs: 2000,
    maxDelayMs: 60000,
    backoffMultiplier: 2.0,
    useJitter: true,
  );
}

/// Result of a retry operation
class RetryResult<T> {
  final T? data;
  final AppError? error;
  final int attemptCount;
  final bool isSuccess;

  const RetryResult._({
    this.data,
    this.error,
    required this.attemptCount,
    required this.isSuccess,
  });

  factory RetryResult.success(T data, int attemptCount) {
    return RetryResult._(
      data: data,
      attemptCount: attemptCount,
      isSuccess: true,
    );
  }

  factory RetryResult.failure(AppError error, int attemptCount) {
    return RetryResult._(
      error: error,
      attemptCount: attemptCount,
      isSuccess: false,
    );
  }
}

/// Service for handling retry logic with exponential backoff
class RetryHandler {
  static final Random _random = Random();

  /// Executes a function with retry logic
  static Future<RetryResult<T>> execute<T>(
    Future<T> Function() operation, {
    RetryConfig config = const RetryConfig(),
    String? operationName,
  }) async {
    int attemptCount = 0;
    AppError? lastError;

    while (attemptCount < config.maxAttempts) {
      attemptCount++;

      try {
        debugPrint(
          '${operationName ?? 'Operation'} attempt $attemptCount/${config.maxAttempts}',
        );

        final result = await operation();

        debugPrint(
          '${operationName ?? 'Operation'} succeeded on attempt $attemptCount',
        );
        return RetryResult.success(result, attemptCount);
      } catch (error) {
        final appError = ErrorHandler.handleError(error);
        lastError = appError;

        debugPrint(
          '${operationName ?? 'Operation'} failed on attempt $attemptCount: ${appError.message}',
        );

        // Check if we should retry this error
        final shouldRetry =
            config.shouldRetry?.call(appError) ??
            ErrorHandler.isRetryableError(appError);

        if (!shouldRetry || attemptCount >= config.maxAttempts) {
          debugPrint('${operationName ?? 'Operation'} will not be retried');
          break;
        }

        // Calculate delay for next attempt
        if (attemptCount < config.maxAttempts) {
          final delay = _calculateDelay(attemptCount, config);
          debugPrint(
            '${operationName ?? 'Operation'} will retry in ${delay}ms',
          );
          await Future.delayed(Duration(milliseconds: delay));
        }
      }
    }

    return RetryResult.failure(
      lastError ??
          AppError.unknown(
            message: 'Operation failed after $attemptCount attempts',
          ),
      attemptCount,
    );
  }

  /// Executes a function with retry logic and returns the result directly
  /// Throws the last error if all attempts fail
  static Future<T> executeOrThrow<T>(
    Future<T> Function() operation, {
    RetryConfig config = const RetryConfig(),
    String? operationName,
  }) async {
    final result = await execute(
      operation,
      config: config,
      operationName: operationName,
    );

    if (result.isSuccess) {
      return result.data!;
    } else {
      throw result.error!;
    }
  }

  /// Executes a function with retry logic for void operations
  static Future<RetryResult<void>> executeVoid(
    Future<void> Function() operation, {
    RetryConfig config = const RetryConfig(),
    String? operationName,
  }) async {
    return execute<void>(
      operation,
      config: config,
      operationName: operationName,
    );
  }

  /// Calculates the delay for the next retry attempt using exponential backoff
  static int _calculateDelay(int attemptNumber, RetryConfig config) {
    // Calculate exponential backoff delay
    final exponentialDelay =
        (config.baseDelayMs * pow(config.backoffMultiplier, attemptNumber - 1))
            .round();

    // Apply maximum delay limit
    var delay = min(exponentialDelay, config.maxDelayMs);

    // Add jitter if enabled
    if (config.useJitter) {
      // Add random jitter of ±25%
      final jitterRange = (delay * 0.25).round();
      final jitter = _random.nextInt(jitterRange * 2) - jitterRange;
      delay = max(0, delay + jitter);
    }

    return delay;
  }

  /// Creates a retry configuration for specific error types
  static RetryConfig configForErrorTypes({
    required List<Type> retryableErrorTypes,
    int maxAttempts = 3,
    int baseDelayMs = 1000,
    int maxDelayMs = 30000,
    double backoffMultiplier = 2.0,
    bool useJitter = true,
  }) {
    return RetryConfig(
      maxAttempts: maxAttempts,
      baseDelayMs: baseDelayMs,
      maxDelayMs: maxDelayMs,
      backoffMultiplier: backoffMultiplier,
      useJitter: useJitter,
      shouldRetry: (error) {
        return retryableErrorTypes.any((type) => error.runtimeType == type);
      },
    );
  }

  /// Creates a retry configuration that only retries network errors
  static RetryConfig networkOnly({
    int maxAttempts = 3,
    int baseDelayMs = 1000,
    int maxDelayMs = 10000,
  }) {
    return RetryConfig(
      maxAttempts: maxAttempts,
      baseDelayMs: baseDelayMs,
      maxDelayMs: maxDelayMs,
      shouldRetry: (error) => error.isNetworkError,
    );
  }

  /// Creates a retry configuration that retries network and server errors
  static RetryConfig networkAndServer({
    int maxAttempts = 3,
    int baseDelayMs = 1000,
    int maxDelayMs = 15000,
  }) {
    return RetryConfig(
      maxAttempts: maxAttempts,
      baseDelayMs: baseDelayMs,
      maxDelayMs: maxDelayMs,
      shouldRetry: (error) =>
          error.isNetworkError ||
          (error.isServerError && (error.statusCode ?? 0) >= 500),
    );
  }
}

/// Extension methods for adding retry functionality to Future operations
extension RetryExtensions<T> on Future<T> {
  /// Adds retry functionality to any Future
  Future<T> withRetry({
    RetryConfig config = const RetryConfig(),
    String? operationName,
  }) {
    return RetryHandler.executeOrThrow(
      () => this,
      config: config,
      operationName: operationName,
    );
  }

  /// Adds network-specific retry functionality
  Future<T> withNetworkRetry({int maxAttempts = 3, String? operationName}) {
    return withRetry(
      config: RetryHandler.networkOnly(maxAttempts: maxAttempts),
      operationName: operationName,
    );
  }

  /// Adds retry functionality for critical operations
  Future<T> withCriticalRetry({String? operationName}) {
    return withRetry(
      config: RetryConfig.critical,
      operationName: operationName,
    );
  }
}
