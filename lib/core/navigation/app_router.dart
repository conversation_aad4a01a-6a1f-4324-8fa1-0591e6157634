import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../presentation/screens/welcome_screen.dart';
import '../../presentation/screens/login_screen.dart';
import '../../presentation/screens/register_screen.dart';
import '../../presentation/screens/splash_screen.dart';
import '../../presentation/screens/dashboard_screen.dart';
import '../../presentation/screens/profile_screen.dart';
import '../../presentation/screens/profile_setup_screen.dart';
import '../../presentation/screens/edit_profile_screen.dart';
import '../../presentation/screens/ride_booking_screen.dart';
import '../../presentation/screens/ride_tracking_screen.dart';
import '../../presentation/screens/ride_history_screen.dart';
import '../../presentation/screens/ride_details_screen.dart';
import '../../presentation/screens/ride_receipt_screen.dart';
import '../../domain/entities/user.dart';
import '../../presentation/providers/auth_notifier.dart';

part 'app_router.gr.dart';

/// AutoRoute configuration for the Lucian Rides app
/// Defines all routes and navigation structure
@AutoRouterConfig()
class AppRouter extends _$AppRouter {
  @override
  List<AutoRoute> get routes => [
    // Splash screen - initial route
    AutoRoute(page: SplashRoute.page, path: '/', initial: true),

    // Authentication routes
    AutoRoute(page: WelcomeRoute.page, path: '/welcome'),
    AutoRoute(page: LoginRoute.page, path: '/login'),
    AutoRoute(page: RegisterRoute.page, path: '/register'),

    // Main app routes - protected by authentication guard
    AutoRoute(
      page: DashboardRoute.page,
      path: '/dashboard',
      guards: [AuthGuard()],
      children: [
        // Nested routes for dashboard tabs will be added in subtask 5.2
      ],
    ),

    // Ride booking routes - protected by authentication guard
    AutoRoute(
      page: RideBookingRoute.page,
      path: '/ride/book',
      guards: [AuthGuard()],
    ),

    // Ride tracking routes - protected by authentication guard
    AutoRoute(
      page: RideTrackingRoute.page,
      path: '/ride/track/:rideId',
      guards: [AuthGuard()],
    ),

    // Ride history routes - protected by authentication guard
    AutoRoute(
      page: RideHistoryRoute.page,
      path: '/ride/history',
      guards: [AuthGuard()],
    ),
    AutoRoute(
      page: RideDetailsRoute.page,
      path: '/ride/details/:rideId',
      guards: [AuthGuard()],
    ),
    AutoRoute(
      page: RideReceiptRoute.page,
      path: '/ride/receipt/:rideId',
      guards: [AuthGuard()],
    ),

    // Profile routes - protected by authentication guard
    AutoRoute(page: ProfileRoute.page, path: '/profile', guards: [AuthGuard()]),
    AutoRoute(
      page: ProfileSetupRoute.page,
      path: '/profile/setup',
      guards: [AuthGuard()],
    ),
    AutoRoute(
      page: EditProfileRoute.page,
      path: '/profile/edit',
      guards: [AuthGuard()],
    ),
  ];
}

/// Authentication guard to protect routes that require authentication
class AuthGuard extends AutoRouteGuard {
  @override
  void onNavigation(NavigationResolver resolver, StackRouter router) {
    try {
      // Get the current context from the router
      final context = router.navigatorKey.currentContext;
      if (context == null) {
        // If no context available, redirect to splash for proper initialization
        router.replaceAll([const SplashRoute()]);
        return;
      }

      // Get the ProviderContainer from the context
      final container = ProviderScope.containerOf(context);
      final authState = container.read(authStateProvider);

      // Check authentication state and handle navigation accordingly
      authState.when(
        authenticated: (_) {
          // User is authenticated, allow navigation
          resolver.next();
        },
        unauthenticated: () {
          // User is not authenticated, redirect to welcome screen
          router.replaceAll([const WelcomeRoute()]);
        },
        initial: () {
          // Authentication state is still initializing, redirect to splash
          router.replaceAll([const SplashRoute()]);
        },
        loading: () {
          // Authentication is in progress, redirect to splash
          router.replaceAll([const SplashRoute()]);
        },
        error: (_, __) {
          // Authentication error occurred, redirect to welcome screen
          router.replaceAll([const WelcomeRoute()]);
        },
      );
    } catch (e) {
      // If any error occurs during authentication check, redirect to splash
      // This ensures the app doesn't crash and can recover properly
      router.replaceAll([const SplashRoute()]);
    }
  }
}
