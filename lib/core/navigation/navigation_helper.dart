import 'package:auto_route/auto_route.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../domain/entities/ride.dart';
import '../../presentation/providers/ride_tracking_notifier.dart';
import 'app_router.dart';

/// Navigation helper for ride-related navigation logic
class NavigationHelper {
  /// Navigate to appropriate ride screen based on current ride status
  static void navigateToActiveRide(StackRouter router, WidgetRef ref) {
    final activeRide = ref.read(activeRideProvider);

    if (activeRide == null) {
      // No active ride, navigate to ride booking
      router.push(const RideBookingRoute());
      return;
    }

    // Navigate based on ride status
    switch (activeRide.status) {
      case RideStatus.requested:
      case RideStatus.accepted:
      case RideStatus.inProgress:
        // Active ride in progress, navigate to tracking
        router.push(RideTrackingRoute(rideId: activeRide.id));
        break;
      case RideStatus.completed:
        // Ride completed, navigate to receipt
        router.push(RideReceiptRoute(rideId: activeRide.id));
        break;
      case RideStatus.cancelled:
        // Ride cancelled, navigate to booking for new ride
        router.push(const RideBookingRoute());
        break;
    }
  }

  /// Check if user has an active ride
  static bool hasActiveRide(WidgetRef ref) {
    final activeRide = ref.read(activeRideProvider);
    return activeRide != null &&
        (activeRide.status == RideStatus.requested ||
            activeRide.status == RideStatus.accepted ||
            activeRide.status == RideStatus.inProgress);
  }

  /// Get the appropriate route for the current ride state
  static PageRouteInfo getActiveRideRoute(WidgetRef ref) {
    final activeRide = ref.read(activeRideProvider);

    if (activeRide == null) {
      return const RideBookingRoute();
    }

    switch (activeRide.status) {
      case RideStatus.requested:
      case RideStatus.accepted:
      case RideStatus.inProgress:
        return RideTrackingRoute(rideId: activeRide.id);
      case RideStatus.completed:
        return RideReceiptRoute(rideId: activeRide.id);
      case RideStatus.cancelled:
        return const RideBookingRoute();
    }
  }

  /// Navigate to ride history with optional filter
  static void navigateToRideHistory(StackRouter router, {String? filter}) {
    router.push(const RideHistoryRoute());
  }

  /// Navigate to specific ride details
  static void navigateToRideDetails(StackRouter router, String rideId) {
    router.push(RideDetailsRoute(rideId: rideId));
  }

  /// Navigate to ride receipt
  static void navigateToRideReceipt(StackRouter router, String rideId) {
    router.push(RideReceiptRoute(rideId: rideId));
  }

  /// Handle deep link navigation for ride-related URLs
  static void handleRideDeepLink(
    StackRouter router,
    WidgetRef ref,
    String path,
  ) {
    final uri = Uri.parse(path);
    final segments = uri.pathSegments;

    if (segments.isEmpty) return;

    switch (segments[0]) {
      case 'ride':
        if (segments.length >= 2) {
          switch (segments[1]) {
            case 'book':
              router.push(const RideBookingRoute());
              break;
            case 'track':
              if (segments.length >= 3) {
                final rideId = segments[2];
                router.push(RideTrackingRoute(rideId: rideId));
              } else {
                // No ride ID provided, navigate to active ride or booking
                navigateToActiveRide(router, ref);
              }
              break;
            case 'history':
              router.push(const RideHistoryRoute());
              break;
            case 'details':
              if (segments.length >= 3) {
                final rideId = segments[2];
                router.push(RideDetailsRoute(rideId: rideId));
              }
              break;
            case 'receipt':
              if (segments.length >= 3) {
                final rideId = segments[2];
                router.push(RideReceiptRoute(rideId: rideId));
              }
              break;
          }
        }
        break;
    }
  }
}

/// Provider for checking if user has an active ride
final hasActiveRideProvider = Provider<bool>((ref) {
  final activeRide = ref.watch(activeRideProvider);
  return activeRide != null &&
      (activeRide.status == RideStatus.requested ||
          activeRide.status == RideStatus.accepted ||
          activeRide.status == RideStatus.inProgress);
});

/// Provider for getting the current ride status display
final rideStatusIndicatorProvider = Provider<RideStatusIndicator?>((ref) {
  final activeRide = ref.watch(activeRideProvider);

  if (activeRide == null) return null;

  switch (activeRide.status) {
    case RideStatus.requested:
      return const RideStatusIndicator(
        status: 'Finding Driver',
        color: RideStatusColor.pending,
        icon: RideStatusIcon.search,
      );
    case RideStatus.accepted:
      return const RideStatusIndicator(
        status: 'Driver En Route',
        color: RideStatusColor.active,
        icon: RideStatusIcon.car,
      );
    case RideStatus.inProgress:
      return const RideStatusIndicator(
        status: 'Trip in Progress',
        color: RideStatusColor.active,
        icon: RideStatusIcon.navigation,
      );
    case RideStatus.completed:
      return const RideStatusIndicator(
        status: 'Trip Completed',
        color: RideStatusColor.completed,
        icon: RideStatusIcon.check,
      );
    case RideStatus.cancelled:
      return const RideStatusIndicator(
        status: 'Trip Cancelled',
        color: RideStatusColor.cancelled,
        icon: RideStatusIcon.cancel,
      );
  }
});

/// Data class for ride status indicator
class RideStatusIndicator {
  final String status;
  final RideStatusColor color;
  final RideStatusIcon icon;

  const RideStatusIndicator({
    required this.status,
    required this.color,
    required this.icon,
  });
}

/// Enum for ride status colors
enum RideStatusColor { pending, active, completed, cancelled }

/// Enum for ride status icons
enum RideStatusIcon { search, car, navigation, check, cancel }
