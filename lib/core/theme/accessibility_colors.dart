import 'package:flutter/material.dart';
import 'app_colors.dart';
import '../utils/accessibility_utils.dart';

/// Accessibility-compliant color combinations for WCAG AA compliance
class AccessibilityColors {
  AccessibilityColors._();

  /// Validates all color combinations used in the app
  static void validateColorContrast() {
    // Primary text on white background
    assert(
      AccessibilityUtils.hasValidContrast(
        foreground: AppColors.textPrimary,
        background: AppColors.background,
      ),
      'Primary text on white background does not meet WCAG AA contrast ratio',
    );

    // Secondary text on white background
    assert(
      AccessibilityUtils.hasValidContrast(
        foreground: AppColors.textSecondary,
        background: AppColors.background,
      ),
      'Secondary text on white background does not meet WCAG AA contrast ratio',
    );

    // White text on primary background
    assert(
      AccessibilityUtils.hasValidContrast(
        foreground: AppColors.textOnPrimary,
        background: AppColors.primary,
      ),
      'White text on primary background does not meet WCAG AA contrast ratio',
    );

    // Error text on white background
    assert(
      AccessibilityUtils.hasValidContrast(
        foreground: AppColors.error,
        background: AppColors.background,
      ),
      'Error text on white background does not meet WCAG AA contrast ratio',
    );

    // Success text on white background
    assert(
      AccessibilityUtils.hasValidContrast(
        foreground: AppColors.success,
        background: AppColors.background,
      ),
      'Success text on white background does not meet WCAG AA contrast ratio',
    );

    // Warning text on white background
    assert(
      AccessibilityUtils.hasValidContrast(
        foreground: AppColors.warning,
        background: AppColors.background,
      ),
      'Warning text on white background does not meet WCAG AA contrast ratio',
    );

    // Info text on white background
    assert(
      AccessibilityUtils.hasValidContrast(
        foreground: AppColors.info,
        background: AppColors.background,
      ),
      'Info text on white background does not meet WCAG AA contrast ratio',
    );
  }

  /// High contrast color alternatives for accessibility
  static const Color highContrastTextPrimary = Color(0xFF000000);
  static const Color highContrastTextSecondary = Color(0xFF424242);
  static const Color highContrastBackground = Color(0xFFFFFFFF);
  static const Color highContrastPrimary = Color(
    0xFF006B5D,
  ); // Darker teal for better contrast
  static const Color highContrastError = Color(
    0xFFD32F2F,
  ); // Darker red for better contrast
  static const Color highContrastSuccess = Color(
    0xFF2E7D32,
  ); // Darker green for better contrast
  static const Color highContrastWarning = Color(
    0xFFE65100,
  ); // Darker orange for better contrast
  static const Color highContrastInfo = Color(
    0xFF1565C0,
  ); // Darker blue for better contrast

  /// Gets appropriate colors based on high contrast mode
  static Color getTextPrimary(BuildContext context) {
    return AccessibilityUtils.isHighContrastEnabled(context)
        ? highContrastTextPrimary
        : AppColors.textPrimary;
  }

  static Color getTextSecondary(BuildContext context) {
    return AccessibilityUtils.isHighContrastEnabled(context)
        ? highContrastTextSecondary
        : AppColors.textSecondary;
  }

  static Color getPrimary(BuildContext context) {
    return AccessibilityUtils.isHighContrastEnabled(context)
        ? highContrastPrimary
        : AppColors.primary;
  }

  static Color getError(BuildContext context) {
    return AccessibilityUtils.isHighContrastEnabled(context)
        ? highContrastError
        : AppColors.error;
  }

  static Color getSuccess(BuildContext context) {
    return AccessibilityUtils.isHighContrastEnabled(context)
        ? highContrastSuccess
        : AppColors.success;
  }

  static Color getWarning(BuildContext context) {
    return AccessibilityUtils.isHighContrastEnabled(context)
        ? highContrastWarning
        : AppColors.warning;
  }

  static Color getInfo(BuildContext context) {
    return AccessibilityUtils.isHighContrastEnabled(context)
        ? highContrastInfo
        : AppColors.info;
  }

  /// Focus indicator colors for keyboard navigation
  static const Color focusIndicator = Color(0xFF2196F3);
  static const Color focusIndicatorHighContrast = Color(0xFF000000);

  static Color getFocusIndicator(BuildContext context) {
    return AccessibilityUtils.isHighContrastEnabled(context)
        ? focusIndicatorHighContrast
        : focusIndicator;
  }
}
