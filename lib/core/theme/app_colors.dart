import 'package:flutter/material.dart';

/// App color palette based on design system specifications
class AppColors {
  // Primary Colors
  static const Color primary = Color(0xFF00B5A5);
  static const Color primaryDark = Color(0xFF008B7A);
  static const Color primaryLight = Color(0xFF4DD0C7);

  // Neutral Colors
  static const Color white = Color(0xFFFFFFFF);
  static const Color lightGray = Color(0xFFF5F5F5);
  static const Color mediumGray = Color(0xFFE0E0E0);
  static const Color darkGray = Color(0xFF666666);
  static const Color charcoal = Color(0xFF2C2C2C);
  static const Color black = Color(0xFF000000);

  // Accent Colors
  static const Color blue = Color(0xFF007AFF);
  static const Color yellow = Color(0xFFFFD700);
  static const Color green = Color(0xFF34C759);
  static const Color orange = Color(0xFFFF9500);

  // Status Colors
  static const Color success = Color(0xFF34C759);
  static const Color warning = Color(0xFFFF9500);
  static const Color error = Color(0xFFFF3B30);
  static const Color errorLight = Color(0xFFFFEBEA);
  static const Color info = Color(0xFF007AFF);

  // Text Colors
  static const Color textPrimary = black;
  static const Color textSecondary = darkGray;
  static const Color textTertiary = mediumGray;
  static const Color textOnPrimary = white;
  static const Color textOnDark = white;
  static const Color textPlaceholder = darkGray;

  // Background Colors
  static const Color background = white;
  static const Color backgroundSecondary = lightGray;
  static const Color surface = white;
  static const Color surfaceVariant = lightGray;

  // Border Colors
  static const Color border = mediumGray;
  static const Color borderFocus = primary;
  static const Color borderError = error;

  // Shadow Colors
  static const Color shadow = Color(0x1A000000);
  static const Color shadowLight = Color(0x0D000000);
  static const Color shadowDark = Color(0x26000000);

  // Overlay Colors
  static const Color overlay = Color(0x80000000);
  static const Color overlayLight = Color(0x4D000000);

  // Disabled Colors
  static const Color disabled = mediumGray;
  static const Color disabledText = darkGray;

  // Rating Colors
  static const Color ratingActive = yellow;
  static const Color ratingInactive = mediumGray;

  // Map Colors
  static const Color mapBackground = charcoal;
  static const Color pickupPin = primary;
  static const Color destinationPin = blue;

  // Badge Colors
  static const Color badgeBackground = error;
  static const Color badgeText = white;

  // Material Design 3 Color System
  static const Color onPrimary = white;
  static const Color primaryContainer = Color(0xFFB2DFDB);
  static const Color onPrimaryContainer = Color(0xFF00695C);

  static const Color secondary = Color(0xFF26A69A);
  static const Color onSecondary = white;
  static const Color secondaryContainer = Color(0xFFE0F2F1);
  static const Color onSecondaryContainer = Color(0xFF004D40);

  static const Color tertiary = yellow;
  static const Color onTertiary = black;
  static const Color tertiaryContainer = Color(0xFFFFF9C4);
  static const Color onTertiaryContainer = Color(0xFF827717);

  static const Color onSurface = black;
  static const Color onSurfaceVariant = darkGray;
  static const Color outline = mediumGray;

  static const Color successContainer = Color(0xFFE8F5E8);
  static const Color onSuccessContainer = Color(0xFF1B5E20);
  static const Color onSuccess = white;

  static const Color warningContainer = Color(0xFFFFF3E0);
  static const Color onWarningContainer = Color(0xFFE65100);

  static const Color errorContainer = Color(0xFFFFEBEE);
  static const Color onErrorContainer = Color(0xFFB71C1C);
  static const Color onError = white;
}
