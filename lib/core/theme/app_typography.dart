import 'package:flutter/material.dart';
import 'app_colors.dart';

/// App typography system based on design specifications
class AppTypography {
  // Font Families
  static const String primaryFont = 'Roboto';
  static const String secondaryFont = 'Inter';
  static const String fallbackFont = 'Open Sans';

  // Font Weights
  static const FontWeight light = FontWeight.w300;
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight semibold = FontWeight.w600;
  static const FontWeight bold = FontWeight.w700;

  // Font Sizes
  static const double fontSizeXS = 12.0;
  static const double fontSizeSM = 14.0;
  static const double fontSizeBase = 16.0;
  static const double fontSizeLG = 18.0;
  static const double fontSizeXL = 20.0;
  static const double fontSize2XL = 24.0;
  static const double fontSize3XL = 32.0;

  // Line Heights
  static const double lineHeightTight = 1.2;
  static const double lineHeightNormal = 1.4;
  static const double lineHeightRelaxed = 1.6;

  // Text Styles
  static const TextStyle h1 = TextStyle(
    fontFamily: primaryFont,
    fontSize: fontSize3XL,
    fontWeight: bold,
    height: lineHeightTight,
    color: AppColors.textPrimary,
  );

  static const TextStyle h2 = TextStyle(
    fontFamily: primaryFont,
    fontSize: fontSize2XL,
    fontWeight: bold,
    height: lineHeightTight,
    color: AppColors.textPrimary,
  );

  static const TextStyle h3 = TextStyle(
    fontFamily: primaryFont,
    fontSize: fontSizeXL,
    fontWeight: semibold,
    height: lineHeightNormal,
    color: AppColors.textPrimary,
  );

  static const TextStyle h4 = TextStyle(
    fontFamily: primaryFont,
    fontSize: fontSizeLG,
    fontWeight: semibold,
    height: lineHeightNormal,
    color: AppColors.textPrimary,
  );

  static const TextStyle bodyLarge = TextStyle(
    fontFamily: primaryFont,
    fontSize: fontSizeLG,
    fontWeight: regular,
    height: lineHeightNormal,
    color: AppColors.textPrimary,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontFamily: primaryFont,
    fontSize: fontSizeBase,
    fontWeight: regular,
    height: lineHeightNormal,
    color: AppColors.textPrimary,
  );

  static const TextStyle bodySmall = TextStyle(
    fontFamily: primaryFont,
    fontSize: fontSizeSM,
    fontWeight: regular,
    height: lineHeightNormal,
    color: AppColors.textSecondary,
  );

  static const TextStyle labelLarge = TextStyle(
    fontFamily: primaryFont,
    fontSize: fontSizeBase,
    fontWeight: medium,
    height: lineHeightNormal,
    color: AppColors.textPrimary,
  );

  static const TextStyle labelMedium = TextStyle(
    fontFamily: primaryFont,
    fontSize: fontSizeSM,
    fontWeight: medium,
    height: lineHeightNormal,
    color: AppColors.textPrimary,
  );

  static const TextStyle labelSmall = TextStyle(
    fontFamily: primaryFont,
    fontSize: fontSizeXS,
    fontWeight: medium,
    height: lineHeightNormal,
    color: AppColors.textSecondary,
  );

  // Button Text Styles
  static const TextStyle buttonLarge = TextStyle(
    fontFamily: primaryFont,
    fontSize: fontSizeBase,
    fontWeight: semibold,
    height: lineHeightNormal,
    color: AppColors.textOnPrimary,
  );

  static const TextStyle buttonMedium = TextStyle(
    fontFamily: primaryFont,
    fontSize: fontSizeSM,
    fontWeight: medium,
    height: lineHeightNormal,
    color: AppColors.textOnPrimary,
  );

  // Input Text Styles
  static const TextStyle inputText = TextStyle(
    fontFamily: primaryFont,
    fontSize: fontSizeBase,
    fontWeight: regular,
    height: lineHeightNormal,
    color: AppColors.textPrimary,
  );

  static const TextStyle inputLabel = TextStyle(
    fontFamily: primaryFont,
    fontSize: fontSizeSM,
    fontWeight: medium,
    height: lineHeightNormal,
    color: AppColors.textSecondary,
  );

  static const TextStyle inputHint = TextStyle(
    fontFamily: primaryFont,
    fontSize: fontSizeBase,
    fontWeight: regular,
    height: lineHeightNormal,
    color: AppColors.textPlaceholder,
  );

  static const TextStyle inputError = TextStyle(
    fontFamily: primaryFont,
    fontSize: fontSizeXS,
    fontWeight: regular,
    height: lineHeightNormal,
    color: AppColors.error,
  );

  // Navigation Text Styles
  static const TextStyle navigationTitle = TextStyle(
    fontFamily: primaryFont,
    fontSize: fontSizeLG,
    fontWeight: semibold,
    height: lineHeightNormal,
    color: AppColors.textPrimary,
  );

  static const TextStyle tabLabel = TextStyle(
    fontFamily: primaryFont,
    fontSize: fontSizeXS,
    fontWeight: medium,
    height: lineHeightNormal,
    color: AppColors.textSecondary,
  );

  static const TextStyle tabLabelActive = TextStyle(
    fontFamily: primaryFont,
    fontSize: fontSizeXS,
    fontWeight: medium,
    height: lineHeightNormal,
    color: AppColors.blue,
  );

  // Status Text Styles
  static const TextStyle successText = TextStyle(
    fontFamily: primaryFont,
    fontSize: fontSizeSM,
    fontWeight: medium,
    height: lineHeightNormal,
    color: AppColors.success,
  );

  static const TextStyle errorText = TextStyle(
    fontFamily: primaryFont,
    fontSize: fontSizeSM,
    fontWeight: medium,
    height: lineHeightNormal,
    color: AppColors.error,
  );

  static const TextStyle warningText = TextStyle(
    fontFamily: primaryFont,
    fontSize: fontSizeSM,
    fontWeight: medium,
    height: lineHeightNormal,
    color: AppColors.warning,
  );

  static const TextStyle infoText = TextStyle(
    fontFamily: primaryFont,
    fontSize: fontSizeSM,
    fontWeight: medium,
    height: lineHeightNormal,
    color: AppColors.info,
  );

  // Caption and Overline
  static const TextStyle caption = TextStyle(
    fontFamily: primaryFont,
    fontSize: fontSizeXS,
    fontWeight: regular,
    height: lineHeightNormal,
    color: AppColors.textSecondary,
  );

  static const TextStyle overline = TextStyle(
    fontFamily: primaryFont,
    fontSize: fontSizeXS,
    fontWeight: medium,
    height: lineHeightNormal,
    color: AppColors.textSecondary,
    letterSpacing: 0.5,
  );

  // Material Design 3 Typography Aliases
  static const TextStyle headlineSmall = h3;
  static const TextStyle headlineMedium = h2;
  static const TextStyle headlineLarge = h1;
  static const TextStyle titleSmall = labelLarge;
  static const TextStyle titleMedium = h4;
  static const TextStyle titleLarge = h3;
}
