import 'dart:math' as dart_math;
import 'package:flutter/material.dart';
import 'package:flutter/semantics.dart';
import '../constants/app_constants.dart';

/// Accessibility utilities for ensuring WCAG compliance
class AccessibilityUtils {
  AccessibilityUtils._();

  /// Minimum touch target size according to WCAG guidelines
  static const double minTouchTargetSize = AppConstants.minTouchTarget;

  /// Ensures minimum touch target size for interactive elements
  static Widget ensureMinTouchTarget({
    required Widget child,
    VoidCallback? onTap,
    String? semanticLabel,
    String? semanticHint,
    bool excludeSemantics = false,
  }) {
    Widget wrappedChild = child;

    // Add semantic information if provided
    if (!excludeSemantics && (semanticLabel != null || semanticHint != null)) {
      wrappedChild = Semantics(
        label: semanticLabel,
        hint: semanticHint,
        button: onTap != null,
        child: wrappedChild,
      );
    }

    // Ensure minimum touch target size
    return ConstrainedBox(
      constraints: const BoxConstraints(
        minWidth: minTouchTargetSize,
        minHeight: minTouchTargetSize,
      ),
      child: wrappedChild,
    );
  }

  /// Creates accessible button with proper semantics
  static Widget accessibleButton({
    required Widget child,
    required VoidCallback? onPressed,
    String? semanticLabel,
    String? semanticHint,
    bool enabled = true,
  }) {
    return Semantics(
      label: semanticLabel,
      hint: semanticHint,
      button: true,
      enabled: enabled,
      child: ensureMinTouchTarget(
        onTap: onPressed,
        child: child,
        excludeSemantics: true,
      ),
    );
  }

  /// Creates accessible text field with proper semantics
  static Widget accessibleTextField({
    required Widget child,
    String? label,
    String? hint,
    String? value,
    bool obscureText = false,
    bool required = false,
    String? errorText,
  }) {
    String? semanticLabel = label;
    if (required && semanticLabel != null) {
      semanticLabel += ', required';
    }

    String? semanticHint = hint;
    if (errorText != null) {
      semanticHint = errorText;
    }

    return Semantics(
      label: semanticLabel,
      hint: semanticHint,
      value: obscureText ? null : value,
      textField: true,
      obscured: obscureText,
      child: child,
    );
  }

  /// Creates accessible image with proper alt text
  static Widget accessibleImage({
    required Widget child,
    required String altText,
    bool decorative = false,
  }) {
    if (decorative) {
      return ExcludeSemantics(child: child);
    }

    return Semantics(label: altText, image: true, child: child);
  }

  /// Creates accessible heading with proper semantics
  static Widget accessibleHeading({
    required Widget child,
    required String text,
    int level = 1,
  }) {
    return Semantics(label: text, header: true, child: child);
  }

  /// Creates accessible list with proper semantics
  static Widget accessibleList({
    required Widget child,
    required int itemCount,
    String? label,
  }) {
    return Semantics(
      label: label ?? 'List with $itemCount items',
      child: child,
    );
  }

  /// Creates accessible card with proper semantics
  static Widget accessibleCard({
    required Widget child,
    String? label,
    String? hint,
    VoidCallback? onTap,
  }) {
    return Semantics(
      label: label,
      hint: hint,
      button: onTap != null,
      child: child,
    );
  }

  /// Announces text to screen readers
  static void announceToScreenReader(
    BuildContext context,
    String message, {
    Assertiveness assertiveness = Assertiveness.polite,
  }) {
    SemanticsService.announce(
      message,
      TextDirection.ltr,
      assertiveness: assertiveness,
    );
  }

  /// Announces ride status updates to screen readers
  static void announceRideStatus(BuildContext context, String status) {
    announceToScreenReader(
      context,
      'Ride status updated: $status',
      assertiveness: Assertiveness.assertive,
    );
  }

  /// Announces location updates to screen readers
  static void announceLocationUpdate(BuildContext context, String location) {
    announceToScreenReader(
      context,
      'Location updated to $location',
      assertiveness: Assertiveness.polite,
    );
  }

  /// Announces booking confirmation to screen readers
  static void announceBookingConfirmation(BuildContext context) {
    announceToScreenReader(
      context,
      'Ride booking confirmed. Your driver is on the way.',
      assertiveness: Assertiveness.assertive,
    );
  }

  /// Checks if high contrast mode is enabled
  static bool isHighContrastEnabled(BuildContext context) {
    return MediaQuery.of(context).highContrast;
  }

  /// Checks if reduce motion is enabled
  static bool isReduceMotionEnabled(BuildContext context) {
    return MediaQuery.of(context).disableAnimations;
  }

  /// Gets text scale factor for dynamic type support
  static double getTextScaleFactor(BuildContext context) {
    return MediaQuery.of(context).textScaler.scale(1.0);
  }

  /// Validates color contrast ratio
  static bool hasValidContrast({
    required Color foreground,
    required Color background,
    bool isLargeText = false,
  }) {
    final ratio = _calculateContrastRatio(foreground, background);
    final requiredRatio = isLargeText ? 3.0 : 4.5; // WCAG AA standards
    return ratio >= requiredRatio;
  }

  /// Calculates contrast ratio between two colors
  static double _calculateContrastRatio(Color color1, Color color2) {
    final luminance1 = _calculateLuminance(color1);
    final luminance2 = _calculateLuminance(color2);

    final lighter = luminance1 > luminance2 ? luminance1 : luminance2;
    final darker = luminance1 > luminance2 ? luminance2 : luminance1;

    return (lighter + 0.05) / (darker + 0.05);
  }

  /// Calculates relative luminance of a color
  static double _calculateLuminance(Color color) {
    final r = _linearizeColorComponent((color.r * 255.0).round() / 255.0);
    final g = _linearizeColorComponent((color.g * 255.0).round() / 255.0);
    final b = _linearizeColorComponent((color.b * 255.0).round() / 255.0);

    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  }

  /// Linearizes color component for luminance calculation
  static double _linearizeColorComponent(double component) {
    if (component <= 0.03928) {
      return component / 12.92;
    } else {
      return ((component + 0.055) / 1.055).pow(2.4);
    }
  }
}

/// Extension for adding accessibility features to widgets
extension AccessibilityExtension on Widget {
  /// Adds semantic label to any widget
  Widget withSemanticLabel(String label) {
    return Semantics(label: label, child: this);
  }

  /// Adds semantic hint to any widget
  Widget withSemanticHint(String hint) {
    return Semantics(hint: hint, child: this);
  }

  /// Marks widget as button for screen readers
  Widget asButton({String? label, String? hint}) {
    return Semantics(label: label, hint: hint, button: true, child: this);
  }

  /// Marks widget as heading for screen readers
  Widget asHeading({String? label}) {
    return Semantics(label: label, header: true, child: this);
  }

  /// Excludes widget from semantics tree
  Widget excludeFromSemantics() {
    return ExcludeSemantics(child: this);
  }

  /// Ensures minimum touch target size
  Widget withMinTouchTarget() {
    return AccessibilityUtils.ensureMinTouchTarget(child: this);
  }
}

/// Math extension for power calculation
extension MathExtension on double {
  double pow(double exponent) {
    return dart_math.pow(this, exponent).toDouble();
  }
}
