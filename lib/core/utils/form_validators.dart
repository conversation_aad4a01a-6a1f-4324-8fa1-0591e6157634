import '../constants/app_constants.dart';

/// Form validation utilities and rules
class FormValidators {
  FormValidators._();

  /// Validate email format and requirements
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }

    final trimmedValue = value.trim();

    if (trimmedValue.length > AppConstants.maxEmailLength) {
      return 'Email must be less than ${AppConstants.maxEmailLength} characters';
    }

    if (!RegExp(AppConstants.emailPattern).hasMatch(trimmedValue)) {
      return 'Please enter a valid email address';
    }

    return null;
  }

  /// Validate password format and requirements
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }

    if (value.length < AppConstants.minPasswordLength) {
      return 'Password must be at least ${AppConstants.minPasswordLength} characters';
    }

    if (value.length > AppConstants.maxPasswordLength) {
      return 'Password must be less than ${AppConstants.maxPasswordLength} characters';
    }

    // Check for at least one letter and one number
    if (!RegExp(r'^(?=.*[A-Za-z])(?=.*\d)').hasMatch(value)) {
      return 'Password must contain at least one letter and one number';
    }

    return null;
  }

  /// Validate password confirmation
  static String? validatePasswordConfirmation(String? value, String? password) {
    if (value == null || value.isEmpty) {
      return 'Please confirm your password';
    }

    if (value != password) {
      return 'Passwords do not match';
    }

    return null;
  }

  /// Validate name format and requirements
  static String? validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Name is required';
    }

    final trimmedValue = value.trim();

    if (trimmedValue.length < 2) {
      return 'Name must be at least 2 characters';
    }

    if (trimmedValue.length > AppConstants.maxNameLength) {
      return 'Name must be less than ${AppConstants.maxNameLength} characters';
    }

    if (!RegExp(AppConstants.namePattern).hasMatch(trimmedValue)) {
      return 'Name can only contain letters and spaces';
    }

    return null;
  }

  /// Validate phone number format (optional field)
  static String? validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Phone is optional
    }

    final trimmedValue = value.trim();

    if (trimmedValue.length > AppConstants.maxPhoneLength) {
      return 'Phone number is too long';
    }

    if (!RegExp(AppConstants.phonePattern).hasMatch(trimmedValue)) {
      return 'Please enter a valid phone number';
    }

    return null;
  }

  /// Validate required field
  static String? validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }

  /// Validate minimum length
  static String? validateMinLength(
    String? value,
    int minLength,
    String fieldName,
  ) {
    if (value == null || value.length < minLength) {
      return '$fieldName must be at least $minLength characters';
    }
    return null;
  }

  /// Validate maximum length
  static String? validateMaxLength(
    String? value,
    int maxLength,
    String fieldName,
  ) {
    if (value != null && value.length > maxLength) {
      return '$fieldName must be less than $maxLength characters';
    }
    return null;
  }

  /// Validate multiple fields and return field errors map
  static Map<String, String> validateLoginForm({
    required String email,
    required String password,
  }) {
    final Map<String, String> errors = {};

    final emailError = validateEmail(email);
    if (emailError != null) {
      errors['email'] = emailError;
    }

    final passwordError = validatePassword(password);
    if (passwordError != null) {
      errors['password'] = passwordError;
    }

    return errors;
  }

  /// Validate registration form fields
  static Map<String, String> validateRegisterForm({
    required String name,
    required String email,
    required String password,
    required String confirmPassword,
    String? phone,
  }) {
    final Map<String, String> errors = {};

    final nameError = validateName(name);
    if (nameError != null) {
      errors['name'] = nameError;
    }

    final emailError = validateEmail(email);
    if (emailError != null) {
      errors['email'] = emailError;
    }

    final passwordError = validatePassword(password);
    if (passwordError != null) {
      errors['password'] = passwordError;
    }

    final confirmPasswordError = validatePasswordConfirmation(
      confirmPassword,
      password,
    );
    if (confirmPasswordError != null) {
      errors['confirmPassword'] = confirmPasswordError;
    }

    final phoneError = validatePhone(phone);
    if (phoneError != null) {
      errors['phone'] = phoneError;
    }

    return errors;
  }

  /// Validate profile form fields
  static Map<String, String> validateProfileForm({
    required String name,
    required String email,
    String? phone,
  }) {
    final Map<String, String> errors = {};

    final nameError = validateName(name);
    if (nameError != null) {
      errors['name'] = nameError;
    }

    final emailError = validateEmail(email);
    if (emailError != null) {
      errors['email'] = emailError;
    }

    final phoneError = validatePhone(phone);
    if (phoneError != null) {
      errors['phone'] = phoneError;
    }

    return errors;
  }

  /// Check if form is valid based on field errors
  static bool isFormValid(Map<String, String> fieldErrors) {
    return fieldErrors.isEmpty;
  }

  /// Check if login form is complete and valid
  static bool isLoginFormValid({
    required String email,
    required String password,
  }) {
    if (email.trim().isEmpty || password.isEmpty) {
      return false;
    }

    final errors = validateLoginForm(email: email, password: password);
    return errors.isEmpty;
  }

  /// Check if register form is complete and valid
  static bool isRegisterFormValid({
    required String name,
    required String email,
    required String password,
    required String confirmPassword,
    String? phone,
  }) {
    if (name.trim().isEmpty ||
        email.trim().isEmpty ||
        password.isEmpty ||
        confirmPassword.isEmpty) {
      return false;
    }

    final errors = validateRegisterForm(
      name: name,
      email: email,
      password: password,
      confirmPassword: confirmPassword,
      phone: phone,
    );
    return errors.isEmpty;
  }

  /// Check if profile form is complete and valid
  static bool isProfileFormValid({
    required String name,
    required String email,
    String? phone,
  }) {
    if (name.trim().isEmpty || email.trim().isEmpty) {
      return false;
    }

    final errors = validateProfileForm(name: name, email: email, phone: phone);
    return errors.isEmpty;
  }
}
