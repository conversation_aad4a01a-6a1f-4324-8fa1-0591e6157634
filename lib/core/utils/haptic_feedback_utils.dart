import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// Utility class for managing haptic feedback throughout the app
/// Provides consistent haptic feedback patterns for different user interactions
class HapticFeedbackUtils {
  HapticFeedbackUtils._();

  /// Light haptic feedback for subtle interactions
  /// Used for: button taps, selection changes, minor confirmations
  static Future<void> lightImpact() async {
    if (!kIsWeb) {
      try {
        await HapticFeedback.lightImpact();
      } catch (e) {
        // Haptic feedback not supported on this device
        debugPrint('Haptic feedback not supported: $e');
      }
    }
  }

  /// Medium haptic feedback for standard interactions
  /// Used for: form submissions, navigation actions, moderate confirmations
  static Future<void> mediumImpact() async {
    if (!kIsWeb) {
      try {
        await HapticFeedback.mediumImpact();
      } catch (e) {
        debugPrint('Haptic feedback not supported: $e');
      }
    }
  }

  /// Heavy haptic feedback for important interactions
  /// Used for: critical actions, errors, major confirmations
  static Future<void> heavyImpact() async {
    if (!kIsWeb) {
      try {
        await HapticFeedback.heavyImpact();
      } catch (e) {
        debugPrint('Haptic feedback not supported: $e');
      }
    }
  }

  /// Selection click feedback for precise interactions
  /// Used for: picker selections, toggle switches, precise controls
  static Future<void> selectionClick() async {
    if (!kIsWeb) {
      try {
        await HapticFeedback.selectionClick();
      } catch (e) {
        debugPrint('Haptic feedback not supported: $e');
      }
    }
  }

  /// Vibrate pattern for notifications and alerts
  /// Used for: incoming ride requests, important notifications
  static Future<void> vibrate() async {
    if (!kIsWeb) {
      try {
        await HapticFeedback.vibrate();
      } catch (e) {
        debugPrint('Haptic feedback not supported: $e');
      }
    }
  }

  /// Success feedback pattern - light double tap
  /// Used for: successful ride booking, payment completion, profile updates
  static Future<void> success() async {
    await lightImpact();
    await Future.delayed(const Duration(milliseconds: 100));
    await lightImpact();
  }

  /// Error feedback pattern - heavy single impact
  /// Used for: booking failures, network errors, validation errors
  static Future<void> error() async {
    await heavyImpact();
  }

  /// Warning feedback pattern - medium impact
  /// Used for: ride cancellation warnings, location permission requests
  static Future<void> warning() async {
    await mediumImpact();
  }

  /// Ride booking confirmation - special pattern
  /// Used specifically for ride booking confirmation
  static Future<void> rideBooked() async {
    await mediumImpact();
    await Future.delayed(const Duration(milliseconds: 150));
    await lightImpact();
    await Future.delayed(const Duration(milliseconds: 100));
    await lightImpact();
  }

  /// Driver arrival notification - attention pattern
  /// Used when driver arrives at pickup location
  static Future<void> driverArrived() async {
    await heavyImpact();
    await Future.delayed(const Duration(milliseconds: 200));
    await mediumImpact();
  }

  /// Trip completion - celebration pattern
  /// Used when ride is successfully completed
  static Future<void> tripCompleted() async {
    await lightImpact();
    await Future.delayed(const Duration(milliseconds: 100));
    await mediumImpact();
    await Future.delayed(const Duration(milliseconds: 100));
    await lightImpact();
  }

  /// Button press feedback - consistent for all buttons
  /// Used for: all button interactions throughout the app
  static Future<void> buttonPress() async {
    await lightImpact();
  }

  /// Toggle feedback - for switches and checkboxes
  /// Used for: settings toggles, checkbox selections
  static Future<void> toggle() async {
    await selectionClick();
  }

  /// Navigation feedback - for screen transitions
  /// Used for: page navigation, tab switches
  static Future<void> navigation() async {
    await lightImpact();
  }

  /// Refresh feedback - for pull-to-refresh actions
  /// Used for: refreshing ride status, updating location
  static Future<void> refresh() async {
    await mediumImpact();
  }

  /// Long press feedback - for context menus and long press actions
  /// Used for: long press menus, drag operations
  static Future<void> longPress() async {
    await heavyImpact();
  }
}

/// Extension to add haptic feedback to common widgets
extension HapticFeedbackExtension on Widget {
  /// Wraps widget with light haptic feedback on tap
  Widget withLightHaptic({VoidCallback? onTap}) {
    return GestureDetector(
      onTap: () {
        HapticFeedbackUtils.lightImpact();
        onTap?.call();
      },
      child: this,
    );
  }

  /// Wraps widget with medium haptic feedback on tap
  Widget withMediumHaptic({VoidCallback? onTap}) {
    return GestureDetector(
      onTap: () {
        HapticFeedbackUtils.mediumImpact();
        onTap?.call();
      },
      child: this,
    );
  }

  /// Wraps widget with heavy haptic feedback on tap
  Widget withHeavyHaptic({VoidCallback? onTap}) {
    return GestureDetector(
      onTap: () {
        HapticFeedbackUtils.heavyImpact();
        onTap?.call();
      },
      child: this,
    );
  }

  /// Wraps widget with button press haptic feedback
  Widget withButtonHaptic({VoidCallback? onTap}) {
    return GestureDetector(
      onTap: () {
        HapticFeedbackUtils.buttonPress();
        onTap?.call();
      },
      child: this,
    );
  }
}
