import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'dart:async';

/// Performance optimization utilities for the app
class PerformanceUtils {
  PerformanceUtils._();

  /// Debounce utility to prevent excessive function calls
  static Timer? _debounceTimer;

  /// Debounces a function call by the specified duration
  static void debounce(Duration duration, VoidCallback callback) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(duration, callback);
  }

  /// Throttle utility to limit function call frequency
  static DateTime? _lastThrottleTime;

  /// Throttles a function call to execute at most once per duration
  static void throttle(Duration duration, VoidCallback callback) {
    final now = DateTime.now();
    if (_lastThrottleTime == null ||
        now.difference(_lastThrottleTime!) >= duration) {
      _lastThrottleTime = now;
      callback();
    }
  }

  /// Optimizes list view performance with lazy loading
  static Widget optimizedListView({
    required int itemCount,
    required IndexedWidgetBuilder itemBuilder,
    ScrollController? controller,
    EdgeInsets? padding,
    bool shrinkWrap = false,
    ScrollPhysics? physics,
    double? itemExtent,
    Widget? separator,
  }) {
    if (separator != null) {
      return ListView.separated(
        controller: controller,
        padding: padding,
        shrinkWrap: shrinkWrap,
        physics: physics,
        itemCount: itemCount,
        itemBuilder: itemBuilder,
        separatorBuilder: (context, index) => separator,
      );
    }

    return ListView.builder(
      controller: controller,
      padding: padding,
      shrinkWrap: shrinkWrap,
      physics: physics,
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      itemExtent: itemExtent,
      // Optimize for performance
      cacheExtent: 250.0,
      addAutomaticKeepAlives: false,
      addRepaintBoundaries: true,
      addSemanticIndexes: true,
    );
  }

  /// Creates a performance-optimized image widget
  static Widget optimizedImage({
    required String imageUrl,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    Widget? placeholder,
    Widget? errorWidget,
    bool enableMemoryCache = true,
    bool enableDiskCache = true,
  }) {
    return Image.network(
      imageUrl,
      width: width,
      height: height,
      fit: fit,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return placeholder ??
            Center(
              child: CircularProgressIndicator(
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded /
                          loadingProgress.expectedTotalBytes!
                    : null,
              ),
            );
      },
      errorBuilder: (context, error, stackTrace) {
        return errorWidget ?? const Icon(Icons.error, color: Colors.grey);
      },
      // Performance optimizations
      frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
        if (wasSynchronouslyLoaded) return child;
        return AnimatedOpacity(
          opacity: frame == null ? 0 : 1,
          duration: const Duration(milliseconds: 200),
          child: child,
        );
      },
      isAntiAlias: true,
      filterQuality: FilterQuality.medium,
    );
  }

  /// Memory-efficient widget builder for large lists
  static Widget memoryEfficientBuilder({
    required int itemCount,
    required IndexedWidgetBuilder itemBuilder,
    int visibleItemCount = 10,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return ListView.builder(
          itemCount: itemCount,
          itemBuilder: (context, index) {
            // Only build widgets that are likely to be visible
            return RepaintBoundary(child: itemBuilder(context, index));
          },
          // Optimize memory usage
          addAutomaticKeepAlives: false,
          addRepaintBoundaries: false, // We're adding them manually
          cacheExtent: constraints.maxHeight * 2, // Cache 2 screen heights
        );
      },
    );
  }

  /// Preloads critical resources
  static Future<void> preloadCriticalResources(BuildContext context) async {
    // Preload commonly used images
    final List<String> criticalImages = [
      // Add paths to critical images here
    ];

    final List<Future> preloadFutures = criticalImages.map((imagePath) {
      return precacheImage(AssetImage(imagePath), context);
    }).toList();

    await Future.wait(preloadFutures);
  }

  /// Optimizes animations for low-end devices
  static Duration getOptimizedAnimationDuration(BuildContext context) {
    // Check if reduce motion is enabled
    final bool reduceMotion = MediaQuery.of(context).disableAnimations;
    if (reduceMotion) {
      return Duration.zero;
    }

    // Adjust duration based on device performance
    // This is a simplified approach - in a real app, you might want to
    // benchmark device performance and adjust accordingly
    return const Duration(milliseconds: 300);
  }

  /// Battery-optimized location updates
  static LocationSettings getBatteryOptimizedLocationSettings() {
    return const LocationSettings(
      accuracy: LocationAccuracy.balanced,
      distanceFilter: 10, // Only update when moved 10 meters
      timeLimit: Duration(minutes: 5), // Limit continuous tracking
    );
  }

  /// Reduces widget rebuilds with const constructors
  static Widget constOptimizedWidget(Widget child) {
    return RepaintBoundary(child: child);
  }

  /// Lazy loading for expensive computations
  static Widget lazyBuilder({
    required Widget Function() builder,
    Widget? placeholder,
  }) {
    return FutureBuilder<Widget>(
      future: Future.microtask(builder),
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          return snapshot.data!;
        }
        return placeholder ?? const SizedBox.shrink();
      },
    );
  }

  /// Optimizes text rendering for performance
  static Text optimizedText(
    String text, {
    TextStyle? style,
    int? maxLines,
    TextOverflow? overflow,
    TextAlign? textAlign,
  }) {
    return Text(
      text,
      style: style,
      maxLines: maxLines,
      overflow: overflow ?? TextOverflow.ellipsis,
      textAlign: textAlign,
      // Performance optimizations
      softWrap: maxLines != 1,
      textScaler: TextScaler.noScaling, // Disable text scaling for performance
    );
  }

  /// Batch multiple setState calls
  static void batchStateUpdates(List<VoidCallback> updates) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      for (final update in updates) {
        update();
      }
    });
  }

  /// Dispose resources properly
  static void disposeResources(List<dynamic> resources) {
    for (final resource in resources) {
      if (resource is AnimationController) {
        resource.dispose();
      } else if (resource is StreamSubscription) {
        resource.cancel();
      } else if (resource is Timer) {
        resource.cancel();
      } else if (resource is ScrollController) {
        resource.dispose();
      } else if (resource is TextEditingController) {
        resource.dispose();
      } else if (resource is FocusNode) {
        resource.dispose();
      }
    }
  }

  /// Check if device is low-end based on available memory
  static bool isLowEndDevice() {
    // This is a simplified check - in a real app, you might want to
    // use platform channels to get actual device specifications
    return false; // Placeholder implementation
  }

  /// Optimize for low-end devices
  static Widget lowEndOptimizedWidget({
    required Widget child,
    Widget? lowEndChild,
  }) {
    if (isLowEndDevice() && lowEndChild != null) {
      return lowEndChild;
    }
    return child;
  }

  /// Precompute expensive operations
  static final Map<String, dynamic> _computationCache = {};

  static T? getCachedComputation<T>(String key) {
    return _computationCache[key] as T?;
  }

  static void setCachedComputation<T>(String key, T value) {
    _computationCache[key] = value;
  }

  static void clearComputationCache() {
    _computationCache.clear();
  }

  /// Optimize network requests
  static Duration getOptimizedTimeout() {
    return const Duration(seconds: 30);
  }

  /// Reduce memory footprint for images
  static ImageProvider optimizedImageProvider(String imageUrl) {
    return NetworkImage(
      imageUrl,
      // Add headers for caching
      headers: {'Cache-Control': 'max-age=3600'},
    );
  }
}

/// Performance monitoring widget
class PerformanceMonitor extends StatefulWidget {
  const PerformanceMonitor({
    super.key,
    required this.child,
    this.enableInDebugMode = true,
  });

  final Widget child;
  final bool enableInDebugMode;

  @override
  State<PerformanceMonitor> createState() => _PerformanceMonitorState();
}

class _PerformanceMonitorState extends State<PerformanceMonitor> {
  int _frameCount = 0;
  DateTime _lastFrameTime = DateTime.now();
  double _fps = 0.0;

  @override
  void initState() {
    super.initState();
    if (kDebugMode && widget.enableInDebugMode) {
      _startMonitoring();
    }
  }

  void _startMonitoring() {
    WidgetsBinding.instance.addPostFrameCallback(_onFrame);
  }

  void _onFrame(Duration timestamp) {
    _frameCount++;
    final now = DateTime.now();
    final elapsed = now.difference(_lastFrameTime);

    if (elapsed.inMilliseconds >= 1000) {
      setState(() {
        _fps = _frameCount / elapsed.inSeconds;
        _frameCount = 0;
        _lastFrameTime = now;
      });
    }

    WidgetsBinding.instance.addPostFrameCallback(_onFrame);
  }

  @override
  Widget build(BuildContext context) {
    if (!kDebugMode || !widget.enableInDebugMode) {
      return widget.child;
    }

    return Stack(
      children: [
        widget.child,
        Positioned(
          top: MediaQuery.of(context).padding.top + 10,
          right: 10,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          ),
        ),
      ],
    );
  }
}

/// Location settings for battery optimization
class LocationSettings {
  const LocationSettings({
    required this.accuracy,
    required this.distanceFilter,
    required this.timeLimit,
  });

  final LocationAccuracy accuracy;
  final double distanceFilter;
  final Duration timeLimit;
}

/// Location accuracy levels
enum LocationAccuracy { lowest, low, medium, high, best, balanced }
