import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../../core/constants/app_constants.dart';
import '../../core/errors/error_handler.dart';
import '../../core/errors/app_error.dart';
import 'auth_interceptor.dart';
import 'storage_service.dart';
import '../../services/network/network_service.dart';

abstract class ApiClient {
  Future<Response<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParams,
    bool allowCached = true,
  });
  Future<Response<T>> post<T>(String path, {dynamic data});
  Future<Response<T>> put<T>(String path, {dynamic data});
  Future<Response<T>> delete<T>(String path);
  Future<Response<T>> patch<T>(String path, {dynamic data});
  void updateAuthToken(String? token);
  Future<bool> isOnline();
}

class DioApiClient implements ApiClient {
  final Dio _dio;
  final AuthInterceptor _authInterceptor;
  final StorageService _storageService;
  final NetworkService? _networkService;

  DioApiClient(
    this._dio,
    this._storageService, {
    NetworkService? networkService,
  }) : _authInterceptor = AuthInterceptor(_storageService),
       _networkService = networkService {
    _setupInterceptors();
  }

  void _setupInterceptors() {
    // Clear existing interceptors
    _dio.interceptors.clear();

    // Add authentication interceptor
    _dio.interceptors.add(_authInterceptor);

    // Add logging interceptor in debug mode
    if (AppConstants.enableLogging && kDebugMode) {
      _dio.interceptors.add(
        LogInterceptor(
          requestHeader: true,
          requestBody: true,
          responseHeader: false,
          responseBody: true,
          error: true,
          logPrint: (object) {
            debugPrint('[API] $object');
          },
        ),
      );
    }

    // Configure timeouts
    _dio.options.connectTimeout = AppConstants.connectTimeout;
    _dio.options.receiveTimeout = AppConstants.requestTimeout;
    _dio.options.sendTimeout = AppConstants.requestTimeout;
  }

  @override
  void updateAuthToken(String? token) {
    _authInterceptor.updateToken(token);
  }

  @override
  Future<bool> isOnline() async {
    if (_networkService == null) {
      return true; // Assume online if no network service
    }
    return await _networkService.isOnline();
  }

  @override
  Future<Response<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParams,
    bool allowCached = true,
  }) async {
    try {
      // Check if we're online
      final online = await isOnline();

      if (!online && allowCached) {
        // Try to get cached data when offline using storage service directly
        final cachedData = await _storageService.getCachedData('api_$path');
        if (cachedData != null) {
          // Return cached data as a mock response
          return Response<T>(
            data: cachedData as T,
            statusCode: 200,
            requestOptions: RequestOptions(path: path),
          );
        }

        // No cached data available, throw network error
        throw const AppError.network(
          message: 'No internet connection and no cached data available',
          details: 'Please check your connection and try again',
        );
      }

      // Make the actual API call
      final response = await _dio.get<T>(path, queryParameters: queryParams);

      // Cache successful responses for offline access
      if (response.statusCode == 200 && response.data != null && allowCached) {
        await _storageService.storeCachedData(
          'api_$path',
          response.data as Map<String, dynamic>,
        );
      }

      return response;
    } catch (error) {
      // If online request fails and we allow cached data, try to return cached data
      if (allowCached) {
        final cachedData = await _storageService.getCachedData('api_$path');
        if (cachedData != null) {
          return Response<T>(
            data: cachedData as T,
            statusCode: 200,
            requestOptions: RequestOptions(path: path),
          );
        }
      }

      throw ErrorHandler.handleError(error);
    }
  }

  @override
  Future<Response<T>> post<T>(String path, {dynamic data}) async {
    try {
      return await _dio.post<T>(path, data: data);
    } catch (error) {
      throw ErrorHandler.handleError(error);
    }
  }

  @override
  Future<Response<T>> put<T>(String path, {dynamic data}) async {
    try {
      return await _dio.put<T>(path, data: data);
    } catch (error) {
      throw ErrorHandler.handleError(error);
    }
  }

  @override
  Future<Response<T>> patch<T>(String path, {dynamic data}) async {
    try {
      return await _dio.patch<T>(path, data: data);
    } catch (error) {
      throw ErrorHandler.handleError(error);
    }
  }

  @override
  Future<Response<T>> delete<T>(String path) async {
    try {
      return await _dio.delete<T>(path);
    } catch (error) {
      throw ErrorHandler.handleError(error);
    }
  }
}
