import 'package:dio/dio.dart';
import '../../domain/entities/ride.dart';
import '../models/api_response.dart';
import 'api_client.dart';

/// Interface for ride-specific API client methods
abstract class RideApiClient {
  /// Request a new ride
  Future<ApiResponse<Ride>> requestRide(RideRequestCreate request);

  /// Get the status of a specific ride
  Future<ApiResponse<Ride>> getRideStatus(String rideId);

  /// Cancel a ride
  Future<ApiResponse<void>> cancelRide(
    String rideId,
    CancellationReason reason,
  );

  /// Get ride history with pagination
  Future<ApiResponse<List<RideHistory>>> getRideHistory({
    int limit = 20,
    int offset = 0,
  });

  /// Get the active ride for the current user (if any)
  Future<ApiResponse<Ride?>> getActiveRide();

  /// Calculate pricing for a ride between two locations
  Future<ApiResponse<PricingInfo>> calculatePrice(
    RideLocation pickup,
    RideLocation dropoff,
  );
}

/// Implementation of RideApiClient using the base ApiClient
class RideApiClientImpl implements RideApiClient {
  final ApiClient _apiClient;

  /// Base path for ride-related endpoints
  static const String _basePath = '/rides';

  /// Creates a new RideApiClientImpl with the given ApiClient
  RideApiClientImpl(this._apiClient);

  @override
  Future<ApiResponse<Ride>> requestRide(RideRequestCreate request) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        '$_basePath/request',
        data: request.toJson(),
      );

      return ApiResponse<Ride>.fromJson(
        response.data!,
        (json) => Ride.fromJson(json as Map<String, dynamic>),
      );
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<ApiResponse<Ride>> getRideStatus(String rideId) async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>(
        '$_basePath/$rideId/status',
      );

      return ApiResponse<Ride>.fromJson(
        response.data!,
        (json) => Ride.fromJson(json as Map<String, dynamic>),
      );
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<ApiResponse<void>> cancelRide(
    String rideId,
    CancellationReason reason,
  ) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        '$_basePath/$rideId/cancel',
        data: {'reason': reason.toString().split('.').last},
      );

      return ApiResponse<void>.fromJson(response.data!, (_) {});
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<ApiResponse<List<RideHistory>>> getRideHistory({
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>(
        '$_basePath/history',
        queryParams: {'limit': limit, 'offset': offset},
      );

      return ApiResponse<List<RideHistory>>.fromJson(response.data!, (json) {
        final List<dynamic> items =
            (json as Map<String, dynamic>)['items'] as List<dynamic>;
        return items
            .map((item) => RideHistory.fromJson(item as Map<String, dynamic>))
            .toList();
      });
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<ApiResponse<Ride?>> getActiveRide() async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>(
        '$_basePath/active',
      );

      return ApiResponse<Ride?>.fromJson(response.data!, (json) {
        if (json == null) return null;
        return Ride.fromJson(json as Map<String, dynamic>);
      });
    } catch (e) {
      // If there's a 404 error, it means there's no active ride
      if (e is DioException && e.response?.statusCode == 404) {
        return const ApiResponse.success(data: null);
      }
      rethrow;
    }
  }

  @override
  Future<ApiResponse<PricingInfo>> calculatePrice(
    RideLocation pickup,
    RideLocation dropoff,
  ) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        '$_basePath/pricing/calculate',
        data: {'pickup': pickup.toJson(), 'dropoff': dropoff.toJson()},
      );

      return ApiResponse<PricingInfo>.fromJson(
        response.data!,
        (json) => PricingInfo.fromJson(json as Map<String, dynamic>),
      );
    } catch (e) {
      rethrow;
    }
  }
}
