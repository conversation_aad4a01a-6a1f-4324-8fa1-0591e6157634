import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../../core/constants/app_constants.dart';
import '../../domain/entities/user.dart';

/// Abstract interface for secure storage operations
abstract class StorageService {
  /// Store JWT authentication token securely with expiration time
  Future<void> storeToken(String token, {DateTime? expiresAt});

  /// Store refresh token securely
  Future<void> storeRefreshToken(String refreshToken);

  /// Retrieve stored JWT token
  Future<String?> getToken();

  /// Retrieve stored refresh token
  Future<String?> getRefreshToken();

  /// Get token expiration time
  Future<DateTime?> getTokenExpiry();

  /// Check if token is expired or will expire soon
  Future<bool> isTokenExpired();

  /// Check if token needs refresh (within buffer time)
  Future<bool> shouldRefreshToken();

  /// Clear stored JWT token and related data
  Future<void> clearToken();

  /// Clear refresh token
  Future<void> clearRefreshToken();

  /// Store user data object securely
  Future<void> storeUserData(User user);

  /// Retrieve stored user data
  Future<User?> getUserData();

  /// Clear stored user data
  Future<void> clearUserData();

  /// Store cached data with timestamp for offline access
  Future<void> storeCachedData(String key, Map<String, dynamic> data);

  /// Retrieve cached data if not expired
  Future<Map<String, dynamic>?> getCachedData(String key);

  /// Check if cached data exists and is valid
  Future<bool> isCachedDataValid(String key);

  /// Clear specific cached data
  Future<void> clearCachedData(String key);

  /// Clear all cached data
  Future<void> clearAllCachedData();

  /// Store last sync timestamp
  Future<void> storeLastSync(DateTime timestamp);

  /// Get last sync timestamp
  Future<DateTime?> getLastSync();

  /// Check if data needs synchronization
  Future<bool> needsSync();

  /// Store app language preference
  Future<void> storeLanguage(String language);

  /// Retrieve stored language preference
  Future<String?> getLanguage();

  /// Store theme mode preference
  Future<void> storeThemeMode(String themeMode);

  /// Retrieve stored theme mode
  Future<String?> getThemeMode();

  /// Store onboarding completion status
  Future<void> storeOnboardingCompleted(bool completed);

  /// Check if onboarding is completed
  Future<bool> isOnboardingCompleted();

  /// Clear all stored data
  Future<void> clearAll();

  /// Check if token exists
  Future<bool> hasToken();

  /// Check if user data exists
  Future<bool> hasUserData();
}

/// Secure storage service implementation using Flutter Secure Storage
class SecureStorageService implements StorageService {
  final FlutterSecureStorage _storage;

  SecureStorageService(this._storage);

  @override
  Future<void> storeToken(String token, {DateTime? expiresAt}) async {
    await _storage.write(key: AppConstants.tokenKey, value: token);

    // Store token expiration time if provided
    if (expiresAt != null) {
      await _storage.write(
        key: AppConstants.tokenExpiryKey,
        value: expiresAt.millisecondsSinceEpoch.toString(),
      );
    }
  }

  @override
  Future<void> storeRefreshToken(String refreshToken) async {
    await _storage.write(
      key: AppConstants.refreshTokenKey,
      value: refreshToken,
    );
  }

  @override
  Future<String?> getToken() async {
    return await _storage.read(key: AppConstants.tokenKey);
  }

  @override
  Future<String?> getRefreshToken() async {
    return await _storage.read(key: AppConstants.refreshTokenKey);
  }

  @override
  Future<DateTime?> getTokenExpiry() async {
    final expiryString = await _storage.read(key: AppConstants.tokenExpiryKey);
    if (expiryString == null) return null;

    try {
      final timestamp = int.parse(expiryString);
      return DateTime.fromMillisecondsSinceEpoch(timestamp);
    } catch (e) {
      // If parsing fails, clear corrupted expiry data
      await _storage.delete(key: AppConstants.tokenExpiryKey);
      return null;
    }
  }

  @override
  Future<bool> isTokenExpired() async {
    final expiry = await getTokenExpiry();
    if (expiry == null) {
      // If no expiry is set, assume token is valid
      // In production, you might want to be more strict
      return false;
    }

    return DateTime.now().isAfter(expiry);
  }

  @override
  Future<bool> shouldRefreshToken() async {
    final expiry = await getTokenExpiry();
    if (expiry == null) return false;

    // Check if token will expire within the buffer time
    final bufferTime = expiry.subtract(AppConstants.tokenExpiryBuffer);
    return DateTime.now().isAfter(bufferTime);
  }

  @override
  Future<void> clearToken() async {
    await _storage.delete(key: AppConstants.tokenKey);
    await _storage.delete(key: AppConstants.tokenExpiryKey);
  }

  @override
  Future<void> clearRefreshToken() async {
    await _storage.delete(key: AppConstants.refreshTokenKey);
  }

  @override
  Future<void> storeUserData(User user) async {
    final userJson = jsonEncode(user.toJson());
    await _storage.write(key: AppConstants.userDataKey, value: userJson);
  }

  @override
  Future<User?> getUserData() async {
    final userJson = await _storage.read(key: AppConstants.userDataKey);
    if (userJson == null) return null;

    try {
      final userMap = jsonDecode(userJson) as Map<String, dynamic>;
      return User.fromJson(userMap);
    } catch (e) {
      // If parsing fails, clear corrupted data
      await clearUserData();
      return null;
    }
  }

  @override
  Future<void> clearUserData() async {
    await _storage.delete(key: AppConstants.userDataKey);
  }

  @override
  Future<void> storeLanguage(String language) async {
    await _storage.write(key: AppConstants.languageKey, value: language);
  }

  @override
  Future<String?> getLanguage() async {
    return await _storage.read(key: AppConstants.languageKey);
  }

  @override
  Future<void> storeThemeMode(String themeMode) async {
    await _storage.write(key: AppConstants.themeKey, value: themeMode);
  }

  @override
  Future<String?> getThemeMode() async {
    return await _storage.read(key: AppConstants.themeKey);
  }

  @override
  Future<void> storeOnboardingCompleted(bool completed) async {
    await _storage.write(
      key: AppConstants.onboardingKey,
      value: completed.toString(),
    );
  }

  @override
  Future<bool> isOnboardingCompleted() async {
    final value = await _storage.read(key: AppConstants.onboardingKey);
    return value == 'true';
  }

  @override
  Future<void> clearAll() async {
    await _storage.deleteAll();
  }

  @override
  Future<bool> hasToken() async {
    final token = await getToken();
    return token != null && token.isNotEmpty;
  }

  @override
  Future<bool> hasUserData() async {
    final userData = await getUserData();
    return userData != null;
  }

  @override
  Future<void> storeCachedData(String key, Map<String, dynamic> data) async {
    // Create cache entry with timestamp
    final cacheEntry = {
      'data': data,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };

    final cacheJson = jsonEncode(cacheEntry);
    final cacheKey = '${AppConstants.cachedDataKey}_$key';
    await _storage.write(key: cacheKey, value: cacheJson);
  }

  @override
  Future<Map<String, dynamic>?> getCachedData(String key) async {
    final cacheKey = '${AppConstants.cachedDataKey}_$key';
    final cacheJson = await _storage.read(key: cacheKey);

    if (cacheJson == null) return null;

    try {
      final cacheEntry = jsonDecode(cacheJson) as Map<String, dynamic>;
      final timestamp = cacheEntry['timestamp'] as int;
      final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);

      // Check if cache is still valid
      final expiryTime = cacheTime.add(AppConstants.cacheExpiry);
      if (DateTime.now().isAfter(expiryTime)) {
        // Cache expired, remove it
        await clearCachedData(key);
        return null;
      }

      return cacheEntry['data'] as Map<String, dynamic>;
    } catch (e) {
      // If parsing fails, clear corrupted cache
      await clearCachedData(key);
      return null;
    }
  }

  @override
  Future<bool> isCachedDataValid(String key) async {
    final cachedData = await getCachedData(key);
    return cachedData != null;
  }

  @override
  Future<void> clearCachedData(String key) async {
    final cacheKey = '${AppConstants.cachedDataKey}_$key';
    await _storage.delete(key: cacheKey);
  }

  @override
  Future<void> clearAllCachedData() async {
    // Get all keys and filter cache keys
    final allKeys = await _storage.readAll();
    final cacheKeyPrefix = '${AppConstants.cachedDataKey}_';

    for (final key in allKeys.keys) {
      if (key.startsWith(cacheKeyPrefix)) {
        await _storage.delete(key: key);
      }
    }
  }

  @override
  Future<void> storeLastSync(DateTime timestamp) async {
    await _storage.write(
      key: AppConstants.lastSyncKey,
      value: timestamp.millisecondsSinceEpoch.toString(),
    );
  }

  @override
  Future<DateTime?> getLastSync() async {
    final syncString = await _storage.read(key: AppConstants.lastSyncKey);
    if (syncString == null) return null;

    try {
      final timestamp = int.parse(syncString);
      return DateTime.fromMillisecondsSinceEpoch(timestamp);
    } catch (e) {
      // If parsing fails, clear corrupted sync data
      await _storage.delete(key: AppConstants.lastSyncKey);
      return null;
    }
  }

  @override
  Future<bool> needsSync() async {
    final lastSync = await getLastSync();
    if (lastSync == null) return true; // Never synced before

    // Check if sync interval has passed
    final nextSyncTime = lastSync.add(AppConstants.syncInterval);
    return DateTime.now().isAfter(nextSyncTime);
  }
}
