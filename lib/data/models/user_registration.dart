import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/user.dart';

part 'user_registration.freezed.dart';
part 'user_registration.g.dart';

/// User registration data model for creating new accounts
@freezed
class UserRegistration with _$UserRegistration {
  const factory UserRegistration({
    required String email,
    required String password,
    @Json<PERSON>ey(name: 'confirm_password') required String confirmPassword,
    required String name,
    @Json<PERSON>ey(name: 'user_type') required UserType userType,
    String? phone,
  }) = _UserRegistration;

  factory UserRegistration.fromJson(Map<String, dynamic> json) =>
      _$UserRegistrationFromJson(json);
}
