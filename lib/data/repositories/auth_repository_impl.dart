import '../../domain/entities/user.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../core/constants/app_constants.dart';
import '../../core/errors/app_error.dart';
import '../datasources/api_client.dart';
import '../datasources/storage_service.dart';
import '../models/login_request.dart';
import '../models/user_registration.dart';

/// Implementation of AuthRepository with enhanced error handling and data transformation
class AuthRepositoryImpl implements AuthRepository {
  final ApiClient _apiClient;
  final StorageService _storageService;

  AuthRepositoryImpl({
    required ApiClient apiClient,
    required StorageService storageService,
  }) : _apiClient = apiClient,
       _storageService = storageService;

  @override
  Future<String> login(String email, String password) async {
    try {
      // Create login request model with user_type set to "rider" for rider app
      final loginRequest = LoginRequest(
        email: email,
        password: password,
        userType: 'rider',
      );

      // Make API call with proper endpoint
      final response = await _apiClient.post(
        AppConstants.authLogin,
        data: loginRequest.toJson(),
      );

      // Extract and validate response data
      final responseData = response.data as Map<String, dynamic>;
      final token = responseData['access_token'] as String?;

      if (token == null || token.isEmpty) {
        throw const AppError.authentication(
          message: 'Invalid response: missing access token',
        );
      }

      // Store token securely with expiration
      // In a real implementation, you would decode the JWT to get the actual expiry
      final expiresAt = DateTime.now().add(const Duration(hours: 24));
      await _storageService.storeToken(token, expiresAt: expiresAt);

      // Store refresh token if provided
      if (responseData.containsKey('refresh_token')) {
        final refreshToken = responseData['refresh_token'] as String;
        await _storageService.storeRefreshToken(refreshToken);
      }

      // Store user data if provided in response
      if (responseData.containsKey('user')) {
        final userData = responseData['user'] as Map<String, dynamic>;
        final user = User.fromJson(userData);
        await _storageService.storeUserData(user);
      }

      return token;
    } catch (e) {
      if (e is AppError) rethrow;
      throw AppError.authentication(message: 'Login failed: ${e.toString()}');
    }
  }

  @override
  Future<String> register({
    required String email,
    required String password,
    required String name,
    required UserType userType,
    String? phone,
  }) async {
    try {
      // Create registration request model
      final registrationRequest = UserRegistration(
        email: email,
        password: password,
        confirmPassword: password, // Use same password for confirm_password
        name: name,
        userType: userType,
        phone: phone,
      );

      // Make API call with proper endpoint
      final response = await _apiClient.post(
        AppConstants.authRegister,
        data: registrationRequest.toJson(),
      );

      // Extract and validate response data
      final responseData = response.data as Map<String, dynamic>;
      final token = responseData['access_token'] as String?;

      if (token == null || token.isEmpty) {
        throw const AppError.authentication(
          message: 'Invalid response: missing access token',
        );
      }

      // Store token securely with expiration
      // In a real implementation, you would decode the JWT to get the actual expiry
      final expiresAt = DateTime.now().add(const Duration(hours: 24));
      await _storageService.storeToken(token, expiresAt: expiresAt);

      // Store refresh token if provided
      if (responseData.containsKey('refresh_token')) {
        final refreshToken = responseData['refresh_token'] as String;
        await _storageService.storeRefreshToken(refreshToken);
      }

      // Store user data if provided in response
      if (responseData.containsKey('user')) {
        final userData = responseData['user'] as Map<String, dynamic>;
        final user = User.fromJson(userData);
        await _storageService.storeUserData(user);
      }

      return token;
    } catch (e) {
      if (e is AppError) rethrow;
      throw AppError.authentication(
        message: 'Registration failed: ${e.toString()}',
      );
    }
  }

  @override
  Future<void> logout() async {
    try {
      // Attempt to notify server of logout
      await _apiClient.post(AppConstants.authLogout);
    } catch (e) {
      // Log error but don't fail logout process
      // Server logout failure shouldn't prevent local cleanup
    } finally {
      // Always clear authentication and cached data regardless of server response
      await _storageService.clearToken();
      await _storageService.clearRefreshToken();
      await _storageService.clearUserData();
      await _storageService.clearAllCachedData();
    }
  }

  @override
  Future<User> getCurrentUser() async {
    try {
      // First try to get user from local storage
      final cachedUser = await _storageService.getUserData();
      if (cachedUser != null) {
        return cachedUser;
      }

      // If not cached, fetch from server
      final response = await _apiClient.get(AppConstants.authProfile);
      final responseData = response.data as Map<String, dynamic>;
      final user = User.fromJson(responseData);

      // Cache the user data for future use
      await _storageService.storeUserData(user);

      return user;
    } catch (e) {
      if (e is AppError) rethrow;
      throw AppError.authentication(
        message: 'Failed to get current user: ${e.toString()}',
      );
    }
  }

  @override
  Future<bool> verifyToken() async {
    try {
      // Check if token exists locally
      final hasToken = await _storageService.hasToken();
      if (!hasToken) return false;

      // Verify token with server
      await _apiClient.get(AppConstants.authVerifyToken);
      return true;
    } catch (e) {
      // If verification fails, clear invalid token
      await _storageService.clearToken();
      await _storageService.clearUserData();
      return false;
    }
  }
}
