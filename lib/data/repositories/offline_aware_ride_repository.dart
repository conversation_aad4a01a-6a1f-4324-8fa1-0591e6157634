import 'dart:math' as math;
import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';

import '../../core/errors/app_error.dart';
import '../../domain/entities/ride.dart';
import '../../domain/repositories/ride_repository.dart';
import '../../services/network/network_service.dart';
import '../../services/sync/offline_data_service.dart';
import 'ride_repository_impl.dart';

/// Offline-aware wrapper for RideRepository that provides graceful offline handling
class OfflineAwareRideRepository implements RideRepository {
  final RideRepositoryImpl _onlineRepository;
  final NetworkService _networkService;
  final OfflineDataService _offlineDataService;

  OfflineAwareRideRepository({
    required RideRepositoryImpl onlineRepository,
    required NetworkService networkService,
    required OfflineDataService offlineDataService,
  }) : _onlineRepository = onlineRepository,
       _networkService = networkService,
       _offlineDataService = offlineDataService;

  @override
  Future<Either<AppError, Ride>> requestRide(RideRequestCreate request) async {
    try {
      final isOnline = await _networkService.isOnline();

      if (!isOnline) {
        // Store ride request for later sync when online
        await _offlineDataService.markForSync(
          'ride_request_${DateTime.now().millisecondsSinceEpoch}',
          {
            'type': 'ride_request',
            'data': request.toJson(),
            'timestamp': DateTime.now().toIso8601String(),
          },
        );

        return const Left(
          AppError.network(
            message: 'No internet connection',
            details:
                'Your ride request has been saved and will be processed when you\'re back online.',
          ),
        );
      }

      // Online - use normal repository
      final result = await _onlineRepository.requestRide(request);

      // Cache successful result
      result.fold(
        (error) =>
            debugPrint('Failed to cache ride request result: ${error.message}'),
        (ride) async {
          await _offlineDataService.cacheActiveRide(ride);
        },
      );

      return result;
    } catch (e) {
      debugPrint('Error in offline-aware ride request: $e');
      return Left(
        AppError.unknown(message: 'Failed to process ride request: $e'),
      );
    }
  }

  @override
  Future<Either<AppError, Ride>> getRideStatus(String rideId) async {
    try {
      // Try to get from cache first
      final cachedRide = await _offlineDataService.getCachedActiveRide();
      if (cachedRide != null && cachedRide.id == rideId) {
        // Check if we need to refresh from server
        final needsRefresh = await _offlineDataService.needsRefresh(
          'active_ride',
        );
        if (!needsRefresh) {
          return Right(cachedRide);
        }
      }

      final isOnline = await _networkService.isOnline();

      if (!isOnline) {
        // Return cached data if available
        if (cachedRide != null && cachedRide.id == rideId) {
          return Right(cachedRide);
        }

        return const Left(
          AppError.network(
            message: 'No internet connection',
            details:
                'Unable to get current ride status. Please check your connection.',
          ),
        );
      }

      // Online - get fresh data
      final result = await _onlineRepository.getRideStatus(rideId);

      // Cache successful result
      result.fold(
        (error) => debugPrint('Failed to cache ride status: ${error.message}'),
        (ride) async {
          await _offlineDataService.cacheActiveRide(ride);
        },
      );

      return result;
    } catch (e) {
      debugPrint('Error in offline-aware ride status: $e');
      return Left(AppError.unknown(message: 'Failed to get ride status: $e'));
    }
  }

  @override
  Future<Either<AppError, void>> cancelRide(
    String rideId,
    CancellationReason reason,
  ) async {
    try {
      final isOnline = await _networkService.isOnline();

      if (!isOnline) {
        // Store cancellation request for later sync
        await _offlineDataService.markForSync('ride_cancel_$rideId', {
          'type': 'ride_cancel',
          'rideId': rideId,
          'reason': reason.name,
          'timestamp': DateTime.now().toIso8601String(),
        });

        // Update local cache to show cancelled status
        final cachedRide = await _offlineDataService.getCachedActiveRide();
        if (cachedRide != null && cachedRide.id == rideId) {
          final cancelledRide = cachedRide.copyWith(
            status: RideStatus.cancelled,
            cancellationReason: reason,
            cancelledAt: DateTime.now(),
          );
          await _offlineDataService.cacheActiveRide(cancelledRide);
        }

        return const Right(null);
      }

      // Online - cancel immediately
      final result = await _onlineRepository.cancelRide(rideId, reason);

      // Update cache on successful cancellation
      result.fold(
        (error) => debugPrint(
          'Failed to update cache after cancellation: ${error.message}',
        ),
        (_) async {
          await _offlineDataService.cacheActiveRide(null);
        },
      );

      return result;
    } catch (e) {
      debugPrint('Error in offline-aware ride cancellation: $e');
      return Left(AppError.unknown(message: 'Failed to cancel ride: $e'));
    }
  }

  @override
  Future<Either<AppError, List<RideHistory>>> getRideHistory({
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      // Try to get from cache first
      final cachedHistory = await _offlineDataService.getCachedRideHistory();
      final needsRefresh = await _offlineDataService.needsRefresh(
        'ride_history',
      );

      if (cachedHistory != null && !needsRefresh) {
        // Apply pagination to cached data
        final paginatedHistory = cachedHistory
            .skip(offset)
            .take(limit)
            .toList();
        return Right(paginatedHistory);
      }

      final isOnline = await _networkService.isOnline();

      if (!isOnline) {
        // Return cached data if available
        if (cachedHistory != null) {
          final paginatedHistory = cachedHistory
              .skip(offset)
              .take(limit)
              .toList();
          return Right(paginatedHistory);
        }

        return const Left(
          AppError.network(
            message: 'No internet connection',
            details:
                'Unable to load ride history. Please check your connection.',
          ),
        );
      }

      // Online - get fresh data
      final result = await _onlineRepository.getRideHistory(
        limit: limit,
        offset: offset,
      );

      // Cache successful result (only for first page to avoid cache bloat)
      if (offset == 0) {
        result.fold(
          (error) =>
              debugPrint('Failed to cache ride history: ${error.message}'),
          (history) async {
            await _offlineDataService.cacheRideHistory(history);
          },
        );
      }

      return result;
    } catch (e) {
      debugPrint('Error in offline-aware ride history: $e');
      return Left(AppError.unknown(message: 'Failed to get ride history: $e'));
    }
  }

  @override
  Future<Either<AppError, Ride?>> getActiveRide() async {
    try {
      // Try to get from cache first
      final cachedRide = await _offlineDataService.getCachedActiveRide();
      final needsRefresh = await _offlineDataService.needsRefresh(
        'active_ride',
      );

      if (cachedRide != null && !needsRefresh) {
        return Right(cachedRide);
      }

      final isOnline = await _networkService.isOnline();

      if (!isOnline) {
        // Return cached data if available
        return Right(cachedRide);
      }

      // Online - get fresh data
      final result = await _onlineRepository.getActiveRide();

      // Cache successful result
      result.fold(
        (error) => debugPrint('Failed to cache active ride: ${error.message}'),
        (ride) async {
          await _offlineDataService.cacheActiveRide(ride);
        },
      );

      return result;
    } catch (e) {
      debugPrint('Error in offline-aware active ride: $e');
      return Left(AppError.unknown(message: 'Failed to get active ride: $e'));
    }
  }

  @override
  Future<Either<AppError, PricingInfo>> calculatePrice(
    RideLocation pickup,
    RideLocation dropoff,
  ) async {
    try {
      // Generate a cache key for this route
      final routeKey =
          '${pickup.latitude},${pickup.longitude}_${dropoff.latitude},${dropoff.longitude}';

      // Try to get from cache first
      final cachedPricing = await _offlineDataService.getCachedPricingInfo(
        routeKey,
      );
      final needsRefresh = await _offlineDataService.needsRefresh(
        'pricing_$routeKey',
      );

      if (cachedPricing != null && !needsRefresh) {
        return Right(cachedPricing);
      }

      final isOnline = await _networkService.isOnline();

      if (!isOnline) {
        // Return cached pricing if available
        if (cachedPricing != null) {
          return Right(cachedPricing);
        }

        // Provide estimated pricing based on distance if no cache available
        final estimatedPricing = _calculateEstimatedPricing(pickup, dropoff);
        return Right(estimatedPricing);
      }

      // Online - get fresh pricing
      final result = await _onlineRepository.calculatePrice(pickup, dropoff);

      // Cache successful result
      result.fold(
        (error) => debugPrint('Failed to cache pricing: ${error.message}'),
        (pricing) async {
          await _offlineDataService.cachePricingInfo(routeKey, pricing);
        },
      );

      return result;
    } catch (e) {
      debugPrint('Error in offline-aware price calculation: $e');
      return Left(AppError.unknown(message: 'Failed to calculate price: $e'));
    }
  }

  @override
  Future<Either<AppError, RideReceipt>> getRideReceipt(String rideId) async {
    try {
      final isOnline = await _networkService.isOnline();

      if (!isOnline) {
        return const Left(
          AppError.network(
            message: 'No internet connection',
            details: 'Receipt generation requires an internet connection.',
          ),
        );
      }

      // Receipts are not cached as they contain sensitive financial information
      return await _onlineRepository.getRideReceipt(rideId);
    } catch (e) {
      debugPrint('Error in offline-aware receipt generation: $e');
      return Left(AppError.unknown(message: 'Failed to generate receipt: $e'));
    }
  }

  /// Calculate estimated pricing based on distance when offline
  PricingInfo _calculateEstimatedPricing(
    RideLocation pickup,
    RideLocation dropoff,
  ) {
    // Simple distance calculation using Haversine formula
    const double earthRadius = 6371; // km

    final lat1Rad = pickup.latitude * (3.14159 / 180);
    final lat2Rad = dropoff.latitude * (3.14159 / 180);
    final deltaLatRad = (dropoff.latitude - pickup.latitude) * (3.14159 / 180);
    final deltaLngRad =
        (dropoff.longitude - pickup.longitude) * (3.14159 / 180);

    final a =
        math.sin(deltaLatRad / 2) * math.sin(deltaLatRad / 2) +
        math.cos(lat1Rad) *
            math.cos(lat2Rad) *
            math.sin(deltaLngRad / 2) *
            math.sin(deltaLngRad / 2);
    final c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
    final distance = earthRadius * c;

    // Estimated pricing based on distance (simplified)
    const double baseFare = 15.0;
    const double perKmRate = 2.5;
    const double taxRate = 0.15; // 15% tax

    final distanceFare = distance * perKmRate;
    final subtotal = baseFare + distanceFare;
    final tax = subtotal * taxRate;
    final totalFare = subtotal + tax;

    return PricingInfo(
      baseFare: baseFare,
      distanceFare: distanceFare,
      additionalFees: 0.0,
      tax: tax,
      totalFare: totalFare,
      estimatedDistanceKm: distance,
      estimatedDurationMinutes: (distance * 2)
          .round(), // Rough estimate: 2 minutes per km
    );
  }
}
