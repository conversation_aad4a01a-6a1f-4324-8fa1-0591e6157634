import 'package:dartz/dartz.dart';
import '../../core/errors/app_error.dart';
import '../../core/errors/error_handler.dart';
import '../../domain/entities/ride.dart';
import '../../domain/repositories/ride_repository.dart';
import '../datasources/ride_api_client.dart';
import '../datasources/storage_service.dart';
import '../../services/network/network_service.dart';

/// Implementation of the RideRepository interface
class RideRepositoryImpl implements RideRepository {
  final RideApiClient _rideApiClient;
  final StorageService _storageService;
  final NetworkService _networkService;

  /// Cache keys for frequently accessed data
  static const String _activeRideCacheKey = 'active_ride';
  static const String _rideHistoryCacheKey = 'ride_history';

  /// Creates a new RideRepositoryImpl
  RideRepositoryImpl({
    required RideApiClient rideApiClient,
    required StorageService storageService,
    required NetworkService networkService,
  }) : _rideApiClient = rideApiClient,
       _storageService = storageService,
       _networkService = networkService;

  @override
  Future<Either<AppError, Ride>> requestRide(RideRequestCreate request) async {
    try {
      // Validate the request
      if (!request.isValid()) {
        return const Left(
          AppError.validation(
            message: 'Invalid ride request',
            fieldErrors: {
              'request':
                  'Both pickup and dropoff locations must be within St. Lucia',
            },
          ),
        );
      }

      // Check network connectivity
      final isOnline = await _networkService.isOnline();
      if (!isOnline) {
        return const Left(
          AppError.network(
            message: 'No internet connection',
            details: 'Please check your connection and try again',
          ),
        );
      }

      // Make API request
      final response = await _rideApiClient.requestRide(request);

      // Handle API response
      return response.when(
        success: (data, _) {
          // Cache the active ride
          _cacheActiveRide(data);
          return Right(data);
        },
        error: (message, errorCode, statusCode) {
          return Left(
            AppError.server(message: message, statusCode: statusCode ?? 500),
          );
        },
      );
    } catch (e) {
      return Left(ErrorHandler.handleError(e));
    }
  }

  @override
  Future<Either<AppError, Ride>> getRideStatus(String rideId) async {
    try {
      // Try to get from cache first
      final cachedRide = await _getCachedActiveRide();
      if (cachedRide != null && cachedRide.id == rideId) {
        return Right(cachedRide);
      }

      // Check network connectivity
      final isOnline = await _networkService.isOnline();
      if (!isOnline) {
        return const Left(
          AppError.network(
            message: 'No internet connection',
            details: 'Please check your connection and try again',
          ),
        );
      }

      // Make API request
      final response = await _rideApiClient.getRideStatus(rideId);

      // Handle API response
      return response.when(
        success: (data, _) {
          // Update cache if this is the active ride
          if (data.isActive()) {
            _cacheActiveRide(data);
          }
          return Right(data);
        },
        error: (message, errorCode, statusCode) {
          return Left(
            AppError.server(message: message, statusCode: statusCode ?? 500),
          );
        },
      );
    } catch (e) {
      return Left(ErrorHandler.handleError(e));
    }
  }

  @override
  Future<Either<AppError, void>> cancelRide(
    String rideId,
    CancellationReason reason,
  ) async {
    try {
      // Check network connectivity
      final isOnline = await _networkService.isOnline();
      if (!isOnline) {
        return const Left(
          AppError.network(
            message: 'No internet connection',
            details: 'Please check your connection and try again',
          ),
        );
      }

      // Make API request
      final response = await _rideApiClient.cancelRide(rideId, reason);

      // Handle API response
      return response.when(
        success: (_, __) {
          // Clear active ride cache if this was the active ride
          _clearActiveRideIfMatches(rideId);
          return const Right(null);
        },
        error: (message, errorCode, statusCode) {
          return Left(
            AppError.server(message: message, statusCode: statusCode ?? 500),
          );
        },
      );
    } catch (e) {
      return Left(ErrorHandler.handleError(e));
    }
  }

  @override
  Future<Either<AppError, List<RideHistory>>> getRideHistory({
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      // Check network connectivity
      final isOnline = await _networkService.isOnline();

      if (!isOnline) {
        // Try to get from cache when offline
        final cachedHistory = await _getCachedRideHistory();
        if (cachedHistory != null) {
          // Apply pagination to cached data
          final paginatedHistory = cachedHistory
              .skip(offset)
              .take(limit)
              .toList();
          return Right(paginatedHistory);
        }

        return const Left(
          AppError.network(
            message: 'No internet connection',
            details: 'Please check your connection and try again',
          ),
        );
      }

      // Make API request
      final response = await _rideApiClient.getRideHistory(
        limit: limit,
        offset: offset,
      );

      // Handle API response
      return response.when(
        success: (data, _) {
          // Cache the ride history
          _cacheRideHistory(data);
          return Right(data);
        },
        error: (message, errorCode, statusCode) {
          return Left(
            AppError.server(message: message, statusCode: statusCode ?? 500),
          );
        },
      );
    } catch (e) {
      return Left(ErrorHandler.handleError(e));
    }
  }

  @override
  Future<Either<AppError, Ride?>> getActiveRide() async {
    try {
      // Try to get from cache first
      final cachedRide = await _getCachedActiveRide();
      if (cachedRide != null && cachedRide.isActive()) {
        return Right(cachedRide);
      }

      // Check network connectivity
      final isOnline = await _networkService.isOnline();
      if (!isOnline) {
        if (cachedRide != null) {
          return Right(cachedRide);
        }

        return const Left(
          AppError.network(
            message: 'No internet connection',
            details: 'Please check your connection and try again',
          ),
        );
      }

      // Make API request
      final response = await _rideApiClient.getActiveRide();

      // Handle API response
      return response.when(
        success: (data, _) {
          if (data != null) {
            _cacheActiveRide(data);
          } else {
            _clearActiveRideCache();
          }
          return Right(data);
        },
        error: (message, errorCode, statusCode) {
          return Left(
            AppError.server(message: message, statusCode: statusCode ?? 500),
          );
        },
      );
    } catch (e) {
      return Left(ErrorHandler.handleError(e));
    }
  }

  @override
  Future<Either<AppError, PricingInfo>> calculatePrice(
    RideLocation pickup,
    RideLocation dropoff,
  ) async {
    try {
      // Validate locations
      if (!pickup.isValidStLuciaLocation() ||
          !dropoff.isValidStLuciaLocation()) {
        return const Left(
          AppError.validation(
            message: 'Invalid locations',
            fieldErrors: {
              'locations':
                  'Both pickup and dropoff locations must be within St. Lucia',
            },
          ),
        );
      }

      // Check network connectivity
      final isOnline = await _networkService.isOnline();
      if (!isOnline) {
        return const Left(
          AppError.network(
            message: 'No internet connection',
            details: 'Please check your connection and try again',
          ),
        );
      }

      // Make API request
      final response = await _rideApiClient.calculatePrice(pickup, dropoff);

      // Handle API response
      return response.when(
        success: (data, _) => Right(data),
        error: (message, errorCode, statusCode) {
          return Left(
            AppError.server(message: message, statusCode: statusCode ?? 500),
          );
        },
      );
    } catch (e) {
      return Left(ErrorHandler.handleError(e));
    }
  }

  @override
  Future<Either<AppError, RideReceipt>> getRideReceipt(String rideId) async {
    try {
      // This endpoint is not implemented in the API client yet
      // For now, we'll return a mock receipt

      // Check network connectivity
      final isOnline = await _networkService.isOnline();
      if (!isOnline) {
        return const Left(
          AppError.network(
            message: 'No internet connection',
            details: 'Please check your connection and try again',
          ),
        );
      }

      // In a real implementation, we would make an API call here
      // For now, we'll simulate a successful response with mock data
      final mockReceipt = RideReceipt(
        rideId: rideId,
        receiptNumber: 'RCP-${DateTime.now().millisecondsSinceEpoch}',
        baseFare: 25.0,
        additionalFees: 2.5,
        tax: 3.75,
        totalAmount: 31.25,
        paymentMethod: 'Credit Card',
        paymentDate: DateTime.now(),
        pickupLocation: 'Rodney Bay',
        dropoffLocation: 'Castries',
        distanceKm: 12.5,
        durationMinutes: 25,
        driverName: 'John Doe',
        vehicleInfo: 'Toyota Camry, White, SLU-123',
      );

      return Right(mockReceipt);
    } catch (e) {
      return Left(ErrorHandler.handleError(e));
    }
  }

  // Private helper methods for caching

  /// Cache the active ride
  Future<void> _cacheActiveRide(Ride ride) async {
    await _storageService.storeCachedData(_activeRideCacheKey, ride.toJson());
  }

  /// Get the cached active ride
  Future<Ride?> _getCachedActiveRide() async {
    final cachedData = await _storageService.getCachedData(_activeRideCacheKey);
    if (cachedData != null) {
      try {
        return Ride.fromJson(cachedData);
      } catch (_) {
        // If parsing fails, clear the cache
        await _clearActiveRideCache();
      }
    }
    return null;
  }

  /// Clear the active ride cache
  Future<void> _clearActiveRideCache() async {
    await _storageService.clearCachedData(_activeRideCacheKey);
  }

  /// Clear the active ride cache if it matches the given ID
  Future<void> _clearActiveRideIfMatches(String rideId) async {
    final cachedRide = await _getCachedActiveRide();
    if (cachedRide != null && cachedRide.id == rideId) {
      await _clearActiveRideCache();
    }
  }

  /// Cache the ride history
  Future<void> _cacheRideHistory(List<RideHistory> history) async {
    final historyJson = history.map((ride) => ride.toJson()).toList();
    await _storageService.storeCachedData(_rideHistoryCacheKey, {
      'history': historyJson,
    });
  }

  /// Get the cached ride history
  Future<List<RideHistory>?> _getCachedRideHistory() async {
    final cachedData = await _storageService.getCachedData(
      _rideHistoryCacheKey,
    );
    if (cachedData != null) {
      try {
        final historyList = cachedData['history'] as List<dynamic>;
        return historyList
            .map((item) => RideHistory.fromJson(item as Map<String, dynamic>))
            .toList();
      } catch (_) {
        // If parsing fails, clear the cache
        await _storageService.clearCachedData(_rideHistoryCacheKey);
      }
    }
    return null;
  }
}
