import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:math' as math;

part 'location_models.freezed.dart';
part 'location_models.g.dart';

/// Represents geographic coordinates with latitude and longitude
@freezed
class LocationCoordinates with _$LocationCoordinates {
  const LocationCoordinates._();

  /// Creates a new location coordinates object
  const factory LocationCoordinates({
    /// Latitude coordinate
    required double latitude,

    /// Longitude coordinate
    required double longitude,

    /// Accuracy of the location in meters (optional)
    double? accuracy,

    /// Timestamp when the location was captured (optional)
    DateTime? timestamp,
  }) = _LocationCoordinates;

  /// Creates LocationCoordinates from JSON
  factory LocationCoordinates.fromJson(Map<String, dynamic> json) =>
      _$LocationCoordinatesFromJson(json);

  /// Validates if the coordinates are within St. Lucia boundaries
  bool isValidStLuciaLocation() {
    // St. Lucia approximate boundaries
    const double minLat = 13.7;
    const double maxLat = 14.1;
    const double minLng = -61.1;
    const double maxLng = -60.8;

    return latitude >= minLat &&
        latitude <= maxLat &&
        longitude >= minLng &&
        longitude <= maxLng;
  }

  /// Calculate distance to another location in kilometers
  double distanceTo(LocationCoordinates other) {
    // Implementation using Haversine formula
    const double earthRadius = 6371; // Earth radius in kilometers

    final double latDiff = _toRadians(other.latitude - latitude);
    final double lngDiff = _toRadians(other.longitude - longitude);

    final double a =
        math.sin(latDiff / 2) * math.sin(latDiff / 2) +
        math.cos(_toRadians(latitude)) *
            math.cos(_toRadians(other.latitude)) *
            math.sin(lngDiff / 2) *
            math.sin(lngDiff / 2);

    final double c = 2 * math.asin(math.sqrt(a));

    return earthRadius * c;
  }

  /// Convert degrees to radians
  double _toRadians(double degrees) {
    return degrees * (math.pi / 180);
  }
}

/// Represents a location suggestion for autocomplete
@freezed
class LocationSuggestion with _$LocationSuggestion {
  const LocationSuggestion._();

  /// Creates a new location suggestion
  const factory LocationSuggestion({
    /// Unique identifier for the suggestion
    required String id,

    /// Main text to display (e.g., "Rodney Bay")
    required String mainText,

    /// Secondary text to display (e.g., "Gros Islet, St. Lucia")
    required String secondaryText,

    /// Full address
    required String fullAddress,

    /// Location coordinates (may be null until selected)
    LocationCoordinates? coordinates,
  }) = _LocationSuggestion;

  /// Creates LocationSuggestion from JSON
  factory LocationSuggestion.fromJson(Map<String, dynamic> json) =>
      _$LocationSuggestionFromJson(json);
}

/// Represents a recent location used by the user
@freezed
class RecentLocation with _$RecentLocation {
  const RecentLocation._();

  /// Creates a new recent location
  const factory RecentLocation({
    /// Unique identifier for the location
    required String id,

    /// Name or label of the location
    required String name,

    /// Full address of the location
    required String address,

    /// Location coordinates
    required LocationCoordinates coordinates,

    /// Timestamp when this location was last used
    required DateTime lastUsed,

    /// Type of location (pickup or dropoff)
    required RecentLocationType type,
  }) = _RecentLocation;

  /// Creates RecentLocation from JSON
  factory RecentLocation.fromJson(Map<String, dynamic> json) =>
      _$RecentLocationFromJson(json);
}

/// Type of recent location
enum RecentLocationType {
  /// Pickup location
  pickup,

  /// Dropoff location
  dropoff,

  /// Both pickup and dropoff
  both,
}

/// Represents the accuracy level of a location
enum LocationAccuracy {
  /// Lowest accuracy, suitable for city-level positioning
  lowest,

  /// Low accuracy, suitable for neighborhood-level positioning
  low,

  /// Medium accuracy, suitable for block-level positioning
  medium,

  /// High accuracy, suitable for building-level positioning
  high,

  /// Highest accuracy, suitable for precise positioning
  highest,
}
