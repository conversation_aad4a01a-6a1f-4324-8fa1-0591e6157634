// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'location_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

LocationCoordinates _$LocationCoordinatesFromJson(Map<String, dynamic> json) {
  return _LocationCoordinates.fromJson(json);
}

/// @nodoc
mixin _$LocationCoordinates {
  /// Latitude coordinate
  double get latitude => throw _privateConstructorUsedError;

  /// Longitude coordinate
  double get longitude => throw _privateConstructorUsedError;

  /// Accuracy of the location in meters (optional)
  double? get accuracy => throw _privateConstructorUsedError;

  /// Timestamp when the location was captured (optional)
  DateTime? get timestamp => throw _privateConstructorUsedError;

  /// Serializes this LocationCoordinates to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LocationCoordinates
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LocationCoordinatesCopyWith<LocationCoordinates> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocationCoordinatesCopyWith<$Res> {
  factory $LocationCoordinatesCopyWith(
          LocationCoordinates value, $Res Function(LocationCoordinates) then) =
      _$LocationCoordinatesCopyWithImpl<$Res, LocationCoordinates>;
  @useResult
  $Res call(
      {double latitude,
      double longitude,
      double? accuracy,
      DateTime? timestamp});
}

/// @nodoc
class _$LocationCoordinatesCopyWithImpl<$Res, $Val extends LocationCoordinates>
    implements $LocationCoordinatesCopyWith<$Res> {
  _$LocationCoordinatesCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LocationCoordinates
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? latitude = null,
    Object? longitude = null,
    Object? accuracy = freezed,
    Object? timestamp = freezed,
  }) {
    return _then(_value.copyWith(
      latitude: null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
      accuracy: freezed == accuracy
          ? _value.accuracy
          : accuracy // ignore: cast_nullable_to_non_nullable
              as double?,
      timestamp: freezed == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LocationCoordinatesImplCopyWith<$Res>
    implements $LocationCoordinatesCopyWith<$Res> {
  factory _$$LocationCoordinatesImplCopyWith(_$LocationCoordinatesImpl value,
          $Res Function(_$LocationCoordinatesImpl) then) =
      __$$LocationCoordinatesImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double latitude,
      double longitude,
      double? accuracy,
      DateTime? timestamp});
}

/// @nodoc
class __$$LocationCoordinatesImplCopyWithImpl<$Res>
    extends _$LocationCoordinatesCopyWithImpl<$Res, _$LocationCoordinatesImpl>
    implements _$$LocationCoordinatesImplCopyWith<$Res> {
  __$$LocationCoordinatesImplCopyWithImpl(_$LocationCoordinatesImpl _value,
      $Res Function(_$LocationCoordinatesImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationCoordinates
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? latitude = null,
    Object? longitude = null,
    Object? accuracy = freezed,
    Object? timestamp = freezed,
  }) {
    return _then(_$LocationCoordinatesImpl(
      latitude: null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
      accuracy: freezed == accuracy
          ? _value.accuracy
          : accuracy // ignore: cast_nullable_to_non_nullable
              as double?,
      timestamp: freezed == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LocationCoordinatesImpl extends _LocationCoordinates {
  const _$LocationCoordinatesImpl(
      {required this.latitude,
      required this.longitude,
      this.accuracy,
      this.timestamp})
      : super._();

  factory _$LocationCoordinatesImpl.fromJson(Map<String, dynamic> json) =>
      _$$LocationCoordinatesImplFromJson(json);

  /// Latitude coordinate
  @override
  final double latitude;

  /// Longitude coordinate
  @override
  final double longitude;

  /// Accuracy of the location in meters (optional)
  @override
  final double? accuracy;

  /// Timestamp when the location was captured (optional)
  @override
  final DateTime? timestamp;

  @override
  String toString() {
    return 'LocationCoordinates(latitude: $latitude, longitude: $longitude, accuracy: $accuracy, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocationCoordinatesImpl &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude) &&
            (identical(other.accuracy, accuracy) ||
                other.accuracy == accuracy) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, latitude, longitude, accuracy, timestamp);

  /// Create a copy of LocationCoordinates
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LocationCoordinatesImplCopyWith<_$LocationCoordinatesImpl> get copyWith =>
      __$$LocationCoordinatesImplCopyWithImpl<_$LocationCoordinatesImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LocationCoordinatesImplToJson(
      this,
    );
  }
}

abstract class _LocationCoordinates extends LocationCoordinates {
  const factory _LocationCoordinates(
      {required final double latitude,
      required final double longitude,
      final double? accuracy,
      final DateTime? timestamp}) = _$LocationCoordinatesImpl;
  const _LocationCoordinates._() : super._();

  factory _LocationCoordinates.fromJson(Map<String, dynamic> json) =
      _$LocationCoordinatesImpl.fromJson;

  /// Latitude coordinate
  @override
  double get latitude;

  /// Longitude coordinate
  @override
  double get longitude;

  /// Accuracy of the location in meters (optional)
  @override
  double? get accuracy;

  /// Timestamp when the location was captured (optional)
  @override
  DateTime? get timestamp;

  /// Create a copy of LocationCoordinates
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LocationCoordinatesImplCopyWith<_$LocationCoordinatesImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

LocationSuggestion _$LocationSuggestionFromJson(Map<String, dynamic> json) {
  return _LocationSuggestion.fromJson(json);
}

/// @nodoc
mixin _$LocationSuggestion {
  /// Unique identifier for the suggestion
  String get id => throw _privateConstructorUsedError;

  /// Main text to display (e.g., "Rodney Bay")
  String get mainText => throw _privateConstructorUsedError;

  /// Secondary text to display (e.g., "Gros Islet, St. Lucia")
  String get secondaryText => throw _privateConstructorUsedError;

  /// Full address
  String get fullAddress => throw _privateConstructorUsedError;

  /// Location coordinates (may be null until selected)
  LocationCoordinates? get coordinates => throw _privateConstructorUsedError;

  /// Serializes this LocationSuggestion to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LocationSuggestion
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LocationSuggestionCopyWith<LocationSuggestion> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocationSuggestionCopyWith<$Res> {
  factory $LocationSuggestionCopyWith(
          LocationSuggestion value, $Res Function(LocationSuggestion) then) =
      _$LocationSuggestionCopyWithImpl<$Res, LocationSuggestion>;
  @useResult
  $Res call(
      {String id,
      String mainText,
      String secondaryText,
      String fullAddress,
      LocationCoordinates? coordinates});

  $LocationCoordinatesCopyWith<$Res>? get coordinates;
}

/// @nodoc
class _$LocationSuggestionCopyWithImpl<$Res, $Val extends LocationSuggestion>
    implements $LocationSuggestionCopyWith<$Res> {
  _$LocationSuggestionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LocationSuggestion
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? mainText = null,
    Object? secondaryText = null,
    Object? fullAddress = null,
    Object? coordinates = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      mainText: null == mainText
          ? _value.mainText
          : mainText // ignore: cast_nullable_to_non_nullable
              as String,
      secondaryText: null == secondaryText
          ? _value.secondaryText
          : secondaryText // ignore: cast_nullable_to_non_nullable
              as String,
      fullAddress: null == fullAddress
          ? _value.fullAddress
          : fullAddress // ignore: cast_nullable_to_non_nullable
              as String,
      coordinates: freezed == coordinates
          ? _value.coordinates
          : coordinates // ignore: cast_nullable_to_non_nullable
              as LocationCoordinates?,
    ) as $Val);
  }

  /// Create a copy of LocationSuggestion
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LocationCoordinatesCopyWith<$Res>? get coordinates {
    if (_value.coordinates == null) {
      return null;
    }

    return $LocationCoordinatesCopyWith<$Res>(_value.coordinates!, (value) {
      return _then(_value.copyWith(coordinates: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$LocationSuggestionImplCopyWith<$Res>
    implements $LocationSuggestionCopyWith<$Res> {
  factory _$$LocationSuggestionImplCopyWith(_$LocationSuggestionImpl value,
          $Res Function(_$LocationSuggestionImpl) then) =
      __$$LocationSuggestionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String mainText,
      String secondaryText,
      String fullAddress,
      LocationCoordinates? coordinates});

  @override
  $LocationCoordinatesCopyWith<$Res>? get coordinates;
}

/// @nodoc
class __$$LocationSuggestionImplCopyWithImpl<$Res>
    extends _$LocationSuggestionCopyWithImpl<$Res, _$LocationSuggestionImpl>
    implements _$$LocationSuggestionImplCopyWith<$Res> {
  __$$LocationSuggestionImplCopyWithImpl(_$LocationSuggestionImpl _value,
      $Res Function(_$LocationSuggestionImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationSuggestion
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? mainText = null,
    Object? secondaryText = null,
    Object? fullAddress = null,
    Object? coordinates = freezed,
  }) {
    return _then(_$LocationSuggestionImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      mainText: null == mainText
          ? _value.mainText
          : mainText // ignore: cast_nullable_to_non_nullable
              as String,
      secondaryText: null == secondaryText
          ? _value.secondaryText
          : secondaryText // ignore: cast_nullable_to_non_nullable
              as String,
      fullAddress: null == fullAddress
          ? _value.fullAddress
          : fullAddress // ignore: cast_nullable_to_non_nullable
              as String,
      coordinates: freezed == coordinates
          ? _value.coordinates
          : coordinates // ignore: cast_nullable_to_non_nullable
              as LocationCoordinates?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LocationSuggestionImpl extends _LocationSuggestion {
  const _$LocationSuggestionImpl(
      {required this.id,
      required this.mainText,
      required this.secondaryText,
      required this.fullAddress,
      this.coordinates})
      : super._();

  factory _$LocationSuggestionImpl.fromJson(Map<String, dynamic> json) =>
      _$$LocationSuggestionImplFromJson(json);

  /// Unique identifier for the suggestion
  @override
  final String id;

  /// Main text to display (e.g., "Rodney Bay")
  @override
  final String mainText;

  /// Secondary text to display (e.g., "Gros Islet, St. Lucia")
  @override
  final String secondaryText;

  /// Full address
  @override
  final String fullAddress;

  /// Location coordinates (may be null until selected)
  @override
  final LocationCoordinates? coordinates;

  @override
  String toString() {
    return 'LocationSuggestion(id: $id, mainText: $mainText, secondaryText: $secondaryText, fullAddress: $fullAddress, coordinates: $coordinates)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocationSuggestionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.mainText, mainText) ||
                other.mainText == mainText) &&
            (identical(other.secondaryText, secondaryText) ||
                other.secondaryText == secondaryText) &&
            (identical(other.fullAddress, fullAddress) ||
                other.fullAddress == fullAddress) &&
            (identical(other.coordinates, coordinates) ||
                other.coordinates == coordinates));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, mainText, secondaryText, fullAddress, coordinates);

  /// Create a copy of LocationSuggestion
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LocationSuggestionImplCopyWith<_$LocationSuggestionImpl> get copyWith =>
      __$$LocationSuggestionImplCopyWithImpl<_$LocationSuggestionImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LocationSuggestionImplToJson(
      this,
    );
  }
}

abstract class _LocationSuggestion extends LocationSuggestion {
  const factory _LocationSuggestion(
      {required final String id,
      required final String mainText,
      required final String secondaryText,
      required final String fullAddress,
      final LocationCoordinates? coordinates}) = _$LocationSuggestionImpl;
  const _LocationSuggestion._() : super._();

  factory _LocationSuggestion.fromJson(Map<String, dynamic> json) =
      _$LocationSuggestionImpl.fromJson;

  /// Unique identifier for the suggestion
  @override
  String get id;

  /// Main text to display (e.g., "Rodney Bay")
  @override
  String get mainText;

  /// Secondary text to display (e.g., "Gros Islet, St. Lucia")
  @override
  String get secondaryText;

  /// Full address
  @override
  String get fullAddress;

  /// Location coordinates (may be null until selected)
  @override
  LocationCoordinates? get coordinates;

  /// Create a copy of LocationSuggestion
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LocationSuggestionImplCopyWith<_$LocationSuggestionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

RecentLocation _$RecentLocationFromJson(Map<String, dynamic> json) {
  return _RecentLocation.fromJson(json);
}

/// @nodoc
mixin _$RecentLocation {
  /// Unique identifier for the location
  String get id => throw _privateConstructorUsedError;

  /// Name or label of the location
  String get name => throw _privateConstructorUsedError;

  /// Full address of the location
  String get address => throw _privateConstructorUsedError;

  /// Location coordinates
  LocationCoordinates get coordinates => throw _privateConstructorUsedError;

  /// Timestamp when this location was last used
  DateTime get lastUsed => throw _privateConstructorUsedError;

  /// Type of location (pickup or dropoff)
  RecentLocationType get type => throw _privateConstructorUsedError;

  /// Serializes this RecentLocation to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RecentLocation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RecentLocationCopyWith<RecentLocation> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RecentLocationCopyWith<$Res> {
  factory $RecentLocationCopyWith(
          RecentLocation value, $Res Function(RecentLocation) then) =
      _$RecentLocationCopyWithImpl<$Res, RecentLocation>;
  @useResult
  $Res call(
      {String id,
      String name,
      String address,
      LocationCoordinates coordinates,
      DateTime lastUsed,
      RecentLocationType type});

  $LocationCoordinatesCopyWith<$Res> get coordinates;
}

/// @nodoc
class _$RecentLocationCopyWithImpl<$Res, $Val extends RecentLocation>
    implements $RecentLocationCopyWith<$Res> {
  _$RecentLocationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RecentLocation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? address = null,
    Object? coordinates = null,
    Object? lastUsed = null,
    Object? type = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      address: null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String,
      coordinates: null == coordinates
          ? _value.coordinates
          : coordinates // ignore: cast_nullable_to_non_nullable
              as LocationCoordinates,
      lastUsed: null == lastUsed
          ? _value.lastUsed
          : lastUsed // ignore: cast_nullable_to_non_nullable
              as DateTime,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as RecentLocationType,
    ) as $Val);
  }

  /// Create a copy of RecentLocation
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LocationCoordinatesCopyWith<$Res> get coordinates {
    return $LocationCoordinatesCopyWith<$Res>(_value.coordinates, (value) {
      return _then(_value.copyWith(coordinates: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$RecentLocationImplCopyWith<$Res>
    implements $RecentLocationCopyWith<$Res> {
  factory _$$RecentLocationImplCopyWith(_$RecentLocationImpl value,
          $Res Function(_$RecentLocationImpl) then) =
      __$$RecentLocationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      String address,
      LocationCoordinates coordinates,
      DateTime lastUsed,
      RecentLocationType type});

  @override
  $LocationCoordinatesCopyWith<$Res> get coordinates;
}

/// @nodoc
class __$$RecentLocationImplCopyWithImpl<$Res>
    extends _$RecentLocationCopyWithImpl<$Res, _$RecentLocationImpl>
    implements _$$RecentLocationImplCopyWith<$Res> {
  __$$RecentLocationImplCopyWithImpl(
      _$RecentLocationImpl _value, $Res Function(_$RecentLocationImpl) _then)
      : super(_value, _then);

  /// Create a copy of RecentLocation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? address = null,
    Object? coordinates = null,
    Object? lastUsed = null,
    Object? type = null,
  }) {
    return _then(_$RecentLocationImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      address: null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String,
      coordinates: null == coordinates
          ? _value.coordinates
          : coordinates // ignore: cast_nullable_to_non_nullable
              as LocationCoordinates,
      lastUsed: null == lastUsed
          ? _value.lastUsed
          : lastUsed // ignore: cast_nullable_to_non_nullable
              as DateTime,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as RecentLocationType,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RecentLocationImpl extends _RecentLocation {
  const _$RecentLocationImpl(
      {required this.id,
      required this.name,
      required this.address,
      required this.coordinates,
      required this.lastUsed,
      required this.type})
      : super._();

  factory _$RecentLocationImpl.fromJson(Map<String, dynamic> json) =>
      _$$RecentLocationImplFromJson(json);

  /// Unique identifier for the location
  @override
  final String id;

  /// Name or label of the location
  @override
  final String name;

  /// Full address of the location
  @override
  final String address;

  /// Location coordinates
  @override
  final LocationCoordinates coordinates;

  /// Timestamp when this location was last used
  @override
  final DateTime lastUsed;

  /// Type of location (pickup or dropoff)
  @override
  final RecentLocationType type;

  @override
  String toString() {
    return 'RecentLocation(id: $id, name: $name, address: $address, coordinates: $coordinates, lastUsed: $lastUsed, type: $type)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RecentLocationImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.coordinates, coordinates) ||
                other.coordinates == coordinates) &&
            (identical(other.lastUsed, lastUsed) ||
                other.lastUsed == lastUsed) &&
            (identical(other.type, type) || other.type == type));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, name, address, coordinates, lastUsed, type);

  /// Create a copy of RecentLocation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RecentLocationImplCopyWith<_$RecentLocationImpl> get copyWith =>
      __$$RecentLocationImplCopyWithImpl<_$RecentLocationImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RecentLocationImplToJson(
      this,
    );
  }
}

abstract class _RecentLocation extends RecentLocation {
  const factory _RecentLocation(
      {required final String id,
      required final String name,
      required final String address,
      required final LocationCoordinates coordinates,
      required final DateTime lastUsed,
      required final RecentLocationType type}) = _$RecentLocationImpl;
  const _RecentLocation._() : super._();

  factory _RecentLocation.fromJson(Map<String, dynamic> json) =
      _$RecentLocationImpl.fromJson;

  /// Unique identifier for the location
  @override
  String get id;

  /// Name or label of the location
  @override
  String get name;

  /// Full address of the location
  @override
  String get address;

  /// Location coordinates
  @override
  LocationCoordinates get coordinates;

  /// Timestamp when this location was last used
  @override
  DateTime get lastUsed;

  /// Type of location (pickup or dropoff)
  @override
  RecentLocationType get type;

  /// Create a copy of RecentLocation
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RecentLocationImplCopyWith<_$RecentLocationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
