// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'location_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$LocationCoordinatesImpl _$$LocationCoordinatesImplFromJson(
        Map<String, dynamic> json) =>
    _$LocationCoordinatesImpl(
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      accuracy: (json['accuracy'] as num?)?.toDouble(),
      timestamp: json['timestamp'] == null
          ? null
          : DateTime.parse(json['timestamp'] as String),
    );

Map<String, dynamic> _$$LocationCoordinatesImplToJson(
        _$LocationCoordinatesImpl instance) =>
    <String, dynamic>{
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      if (instance.accuracy case final value?) 'accuracy': value,
      if (instance.timestamp?.toIso8601String() case final value?)
        'timestamp': value,
    };

_$LocationSuggestionImpl _$$LocationSuggestionImplFromJson(
        Map<String, dynamic> json) =>
    _$LocationSuggestionImpl(
      id: json['id'] as String,
      mainText: json['mainText'] as String,
      secondaryText: json['secondaryText'] as String,
      fullAddress: json['fullAddress'] as String,
      coordinates: json['coordinates'] == null
          ? null
          : LocationCoordinates.fromJson(
              json['coordinates'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$LocationSuggestionImplToJson(
        _$LocationSuggestionImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'mainText': instance.mainText,
      'secondaryText': instance.secondaryText,
      'fullAddress': instance.fullAddress,
      if (instance.coordinates?.toJson() case final value?)
        'coordinates': value,
    };

_$RecentLocationImpl _$$RecentLocationImplFromJson(Map<String, dynamic> json) =>
    _$RecentLocationImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      address: json['address'] as String,
      coordinates: LocationCoordinates.fromJson(
          json['coordinates'] as Map<String, dynamic>),
      lastUsed: DateTime.parse(json['lastUsed'] as String),
      type: $enumDecode(_$RecentLocationTypeEnumMap, json['type']),
    );

Map<String, dynamic> _$$RecentLocationImplToJson(
        _$RecentLocationImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'address': instance.address,
      'coordinates': instance.coordinates.toJson(),
      'lastUsed': instance.lastUsed.toIso8601String(),
      'type': _$RecentLocationTypeEnumMap[instance.type]!,
    };

const _$RecentLocationTypeEnumMap = {
  RecentLocationType.pickup: 'pickup',
  RecentLocationType.dropoff: 'dropoff',
  RecentLocationType.both: 'both',
};
