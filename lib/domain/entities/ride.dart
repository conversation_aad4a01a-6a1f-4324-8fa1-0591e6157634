import 'package:freezed_annotation/freezed_annotation.dart';

part 'ride.freezed.dart';
part 'ride.g.dart';

/// Represents the status of a ride
enum RideStatus {
  /// Ride has been requested but not yet accepted by a driver
  requested,

  /// Ride has been accepted by a driver who is en route to pickup
  accepted,

  /// Ride is in progress (passenger picked up, heading to destination)
  inProgress,

  /// Ride has been completed successfully
  completed,

  /// Ride was cancelled by either rider or driver
  cancelled,
}

/// Represents the status of payment for a ride
enum PaymentStatus {
  /// Payment is pending
  pending,

  /// Payment has been processed successfully
  completed,

  /// Payment failed
  failed,

  /// Payment was refunded
  refunded,
}

/// Represents the reason for cancellation of a ride
enum CancellationReason {
  /// Rider changed their mind
  riderChangedMind,

  /// Rider found alternative transportation
  riderFoundAlternative,

  /// Driver is taking too long
  driverTakingTooLong,

  /// Driver cancelled the ride
  driverCancelled,

  /// Emergency situation
  emergency,

  /// Other reason not listed
  other,
}

/// Represents a location for pickup or dropoff
@freezed
class RideLocation with _$RideLocation {
  const RideLocation._();

  /// Creates a new ride location
  const factory RideLocation({
    /// Name or label of the location
    required String name,

    /// Full address of the location (optional)
    String? address,

    /// Latitude coordinate
    required double latitude,

    /// Longitude coordinate
    required double longitude,

    /// Additional instructions for the driver (optional)
    String? instructions,
  }) = _RideLocation;

  /// Creates a RideLocation from JSON
  factory RideLocation.fromJson(Map<String, dynamic> json) =>
      _$RideLocationFromJson(json);

  /// Validates if the location coordinates are within St. Lucia boundaries
  bool isValidStLuciaLocation() {
    // St. Lucia approximate boundaries
    const double minLat = 13.7;
    const double maxLat = 14.1;
    const double minLng = -61.1;
    const double maxLng = -60.8;

    return latitude >= minLat &&
        latitude <= maxLat &&
        longitude >= minLng &&
        longitude <= maxLng;
  }
}

/// Represents a ride in the system
@freezed
class Ride with _$Ride {
  const Ride._();

  /// Creates a new ride
  const factory Ride({
    /// Unique identifier for the ride
    required String id,

    /// ID of the rider who requested the ride
    required String riderId,

    /// ID of the driver assigned to the ride (null if not yet assigned)
    String? driverId,

    /// Current status of the ride
    required RideStatus status,

    /// Pickup location details
    required RideLocation pickupLocation,

    /// Dropoff location details
    required RideLocation dropoffLocation,

    /// Fixed fare price for the ride
    required double fixedFare,

    /// Current payment status
    required PaymentStatus paymentStatus,

    /// Timestamp when the ride was accepted by a driver
    DateTime? acceptedAt,

    /// Timestamp when the ride was started (passenger picked up)
    DateTime? startedAt,

    /// Timestamp when the ride was completed
    DateTime? completedAt,

    /// Timestamp when the ride was cancelled (if applicable)
    DateTime? cancelledAt,

    /// Special instructions from the rider to the driver
    String? specialInstructions,

    /// Reason for cancellation (if applicable)
    CancellationReason? cancellationReason,

    /// Timestamp when the ride was created
    required DateTime createdAt,
  }) = _Ride;

  /// Creates a Ride from JSON
  factory Ride.fromJson(Map<String, dynamic> json) => _$RideFromJson(json);

  /// Checks if the ride can be cancelled by the rider
  bool canBeCancelledByRider() {
    return status == RideStatus.requested || status == RideStatus.accepted;
  }

  /// Checks if the ride is active (either accepted or in progress)
  bool isActive() {
    return status == RideStatus.accepted || status == RideStatus.inProgress;
  }

  /// Calculates the duration of the ride in minutes (if completed)
  int? rideDurationMinutes() {
    if (startedAt != null && completedAt != null) {
      return completedAt!.difference(startedAt!).inMinutes;
    }
    return null;
  }
}

/// Represents a ride history item with summarized information
@freezed
class RideHistory with _$RideHistory {
  const RideHistory._();

  /// Creates a new ride history item
  const factory RideHistory({
    /// Unique identifier for the ride
    required String id,

    /// Name of the pickup location
    required String pickupName,

    /// Name of the dropoff location
    required String dropoffName,

    /// Fixed fare price for the ride
    required double fare,

    /// Current status of the ride
    required RideStatus status,

    /// Timestamp when the ride was created
    required DateTime createdAt,

    /// Timestamp when the ride was completed (if applicable)
    DateTime? completedAt,

    /// Driver name (if assigned)
    String? driverName,

    /// Driver rating (if applicable and ride completed)
    double? driverRating,
  }) = _RideHistory;

  /// Creates a RideHistory from JSON
  factory RideHistory.fromJson(Map<String, dynamic> json) =>
      _$RideHistoryFromJson(json);
}

/// Represents detailed receipt information for a completed ride
@freezed
class RideReceipt with _$RideReceipt {
  const RideReceipt._();

  /// Creates a new ride receipt
  const factory RideReceipt({
    /// Unique identifier for the ride
    required String rideId,

    /// Receipt number for reference
    required String receiptNumber,

    /// Base fare amount
    required double baseFare,

    /// Any additional fees
    @Default(0.0) double additionalFees,

    /// Tax amount
    required double tax,

    /// Total amount charged
    required double totalAmount,

    /// Payment method used
    required String paymentMethod,

    /// Timestamp when payment was processed
    required DateTime paymentDate,

    /// Pickup location name
    required String pickupLocation,

    /// Dropoff location name
    required String dropoffLocation,

    /// Ride distance in kilometers
    required double distanceKm,

    /// Ride duration in minutes
    required int durationMinutes,

    /// Driver name
    required String driverName,

    /// Driver vehicle information
    required String vehicleInfo,
  }) = _RideReceipt;

  /// Creates a RideReceipt from JSON
  factory RideReceipt.fromJson(Map<String, dynamic> json) =>
      _$RideReceiptFromJson(json);
}

/// Request model for creating a new ride
@freezed
class RideRequestCreate with _$RideRequestCreate {
  const RideRequestCreate._();

  /// Creates a new ride request
  const factory RideRequestCreate({
    /// Pickup location details
    required RideLocation pickupLocation,

    /// Dropoff location details
    required RideLocation dropoffLocation,

    /// Special instructions for the driver (optional)
    String? specialInstructions,
  }) = _RideRequestCreate;

  /// Creates a RideRequestCreate from JSON
  factory RideRequestCreate.fromJson(Map<String, dynamic> json) =>
      _$RideRequestCreateFromJson(json);

  /// Validates the ride request
  bool isValid() {
    // Both locations must be valid St. Lucia locations
    return pickupLocation.isValidStLuciaLocation() &&
        dropoffLocation.isValidStLuciaLocation();
  }
}

/// Response model for pricing calculation
@freezed
class PricingInfo with _$PricingInfo {
  const PricingInfo._();

  /// Creates a new pricing info object
  const factory PricingInfo({
    /// Base fare amount
    required double baseFare,

    /// Distance-based fare component
    required double distanceFare,

    /// Any additional fees
    @Default(0.0) double additionalFees,

    /// Tax amount
    required double tax,

    /// Total fare amount
    required double totalFare,

    /// Estimated distance in kilometers
    required double estimatedDistanceKm,

    /// Estimated duration in minutes
    required int estimatedDurationMinutes,
  }) = _PricingInfo;

  /// Creates a PricingInfo from JSON
  factory PricingInfo.fromJson(Map<String, dynamic> json) =>
      _$PricingInfoFromJson(json);
}
