// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ride.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

RideLocation _$RideLocationFromJson(Map<String, dynamic> json) {
  return _RideLocation.fromJson(json);
}

/// @nodoc
mixin _$RideLocation {
  /// Name or label of the location
  String get name => throw _privateConstructorUsedError;

  /// Full address of the location (optional)
  String? get address => throw _privateConstructorUsedError;

  /// Latitude coordinate
  double get latitude => throw _privateConstructorUsedError;

  /// Longitude coordinate
  double get longitude => throw _privateConstructorUsedError;

  /// Additional instructions for the driver (optional)
  String? get instructions => throw _privateConstructorUsedError;

  /// Serializes this RideLocation to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RideLocation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RideLocationCopyWith<RideLocation> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RideLocationCopyWith<$Res> {
  factory $RideLocationCopyWith(
          RideLocation value, $Res Function(RideLocation) then) =
      _$RideLocationCopyWithImpl<$Res, RideLocation>;
  @useResult
  $Res call(
      {String name,
      String? address,
      double latitude,
      double longitude,
      String? instructions});
}

/// @nodoc
class _$RideLocationCopyWithImpl<$Res, $Val extends RideLocation>
    implements $RideLocationCopyWith<$Res> {
  _$RideLocationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RideLocation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? address = freezed,
    Object? latitude = null,
    Object? longitude = null,
    Object? instructions = freezed,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      latitude: null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
      instructions: freezed == instructions
          ? _value.instructions
          : instructions // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RideLocationImplCopyWith<$Res>
    implements $RideLocationCopyWith<$Res> {
  factory _$$RideLocationImplCopyWith(
          _$RideLocationImpl value, $Res Function(_$RideLocationImpl) then) =
      __$$RideLocationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String name,
      String? address,
      double latitude,
      double longitude,
      String? instructions});
}

/// @nodoc
class __$$RideLocationImplCopyWithImpl<$Res>
    extends _$RideLocationCopyWithImpl<$Res, _$RideLocationImpl>
    implements _$$RideLocationImplCopyWith<$Res> {
  __$$RideLocationImplCopyWithImpl(
      _$RideLocationImpl _value, $Res Function(_$RideLocationImpl) _then)
      : super(_value, _then);

  /// Create a copy of RideLocation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? address = freezed,
    Object? latitude = null,
    Object? longitude = null,
    Object? instructions = freezed,
  }) {
    return _then(_$RideLocationImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      latitude: null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
      instructions: freezed == instructions
          ? _value.instructions
          : instructions // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RideLocationImpl extends _RideLocation {
  const _$RideLocationImpl(
      {required this.name,
      this.address,
      required this.latitude,
      required this.longitude,
      this.instructions})
      : super._();

  factory _$RideLocationImpl.fromJson(Map<String, dynamic> json) =>
      _$$RideLocationImplFromJson(json);

  /// Name or label of the location
  @override
  final String name;

  /// Full address of the location (optional)
  @override
  final String? address;

  /// Latitude coordinate
  @override
  final double latitude;

  /// Longitude coordinate
  @override
  final double longitude;

  /// Additional instructions for the driver (optional)
  @override
  final String? instructions;

  @override
  String toString() {
    return 'RideLocation(name: $name, address: $address, latitude: $latitude, longitude: $longitude, instructions: $instructions)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RideLocationImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude) &&
            (identical(other.instructions, instructions) ||
                other.instructions == instructions));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, name, address, latitude, longitude, instructions);

  /// Create a copy of RideLocation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RideLocationImplCopyWith<_$RideLocationImpl> get copyWith =>
      __$$RideLocationImplCopyWithImpl<_$RideLocationImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RideLocationImplToJson(
      this,
    );
  }
}

abstract class _RideLocation extends RideLocation {
  const factory _RideLocation(
      {required final String name,
      final String? address,
      required final double latitude,
      required final double longitude,
      final String? instructions}) = _$RideLocationImpl;
  const _RideLocation._() : super._();

  factory _RideLocation.fromJson(Map<String, dynamic> json) =
      _$RideLocationImpl.fromJson;

  /// Name or label of the location
  @override
  String get name;

  /// Full address of the location (optional)
  @override
  String? get address;

  /// Latitude coordinate
  @override
  double get latitude;

  /// Longitude coordinate
  @override
  double get longitude;

  /// Additional instructions for the driver (optional)
  @override
  String? get instructions;

  /// Create a copy of RideLocation
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RideLocationImplCopyWith<_$RideLocationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Ride _$RideFromJson(Map<String, dynamic> json) {
  return _Ride.fromJson(json);
}

/// @nodoc
mixin _$Ride {
  /// Unique identifier for the ride
  String get id => throw _privateConstructorUsedError;

  /// ID of the rider who requested the ride
  String get riderId => throw _privateConstructorUsedError;

  /// ID of the driver assigned to the ride (null if not yet assigned)
  String? get driverId => throw _privateConstructorUsedError;

  /// Current status of the ride
  RideStatus get status => throw _privateConstructorUsedError;

  /// Pickup location details
  RideLocation get pickupLocation => throw _privateConstructorUsedError;

  /// Dropoff location details
  RideLocation get dropoffLocation => throw _privateConstructorUsedError;

  /// Fixed fare price for the ride
  double get fixedFare => throw _privateConstructorUsedError;

  /// Current payment status
  PaymentStatus get paymentStatus => throw _privateConstructorUsedError;

  /// Timestamp when the ride was accepted by a driver
  DateTime? get acceptedAt => throw _privateConstructorUsedError;

  /// Timestamp when the ride was started (passenger picked up)
  DateTime? get startedAt => throw _privateConstructorUsedError;

  /// Timestamp when the ride was completed
  DateTime? get completedAt => throw _privateConstructorUsedError;

  /// Timestamp when the ride was cancelled (if applicable)
  DateTime? get cancelledAt => throw _privateConstructorUsedError;

  /// Special instructions from the rider to the driver
  String? get specialInstructions => throw _privateConstructorUsedError;

  /// Reason for cancellation (if applicable)
  CancellationReason? get cancellationReason =>
      throw _privateConstructorUsedError;

  /// Timestamp when the ride was created
  DateTime get createdAt => throw _privateConstructorUsedError;

  /// Serializes this Ride to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Ride
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RideCopyWith<Ride> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RideCopyWith<$Res> {
  factory $RideCopyWith(Ride value, $Res Function(Ride) then) =
      _$RideCopyWithImpl<$Res, Ride>;
  @useResult
  $Res call(
      {String id,
      String riderId,
      String? driverId,
      RideStatus status,
      RideLocation pickupLocation,
      RideLocation dropoffLocation,
      double fixedFare,
      PaymentStatus paymentStatus,
      DateTime? acceptedAt,
      DateTime? startedAt,
      DateTime? completedAt,
      DateTime? cancelledAt,
      String? specialInstructions,
      CancellationReason? cancellationReason,
      DateTime createdAt});

  $RideLocationCopyWith<$Res> get pickupLocation;
  $RideLocationCopyWith<$Res> get dropoffLocation;
}

/// @nodoc
class _$RideCopyWithImpl<$Res, $Val extends Ride>
    implements $RideCopyWith<$Res> {
  _$RideCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Ride
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? riderId = null,
    Object? driverId = freezed,
    Object? status = null,
    Object? pickupLocation = null,
    Object? dropoffLocation = null,
    Object? fixedFare = null,
    Object? paymentStatus = null,
    Object? acceptedAt = freezed,
    Object? startedAt = freezed,
    Object? completedAt = freezed,
    Object? cancelledAt = freezed,
    Object? specialInstructions = freezed,
    Object? cancellationReason = freezed,
    Object? createdAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      riderId: null == riderId
          ? _value.riderId
          : riderId // ignore: cast_nullable_to_non_nullable
              as String,
      driverId: freezed == driverId
          ? _value.driverId
          : driverId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as RideStatus,
      pickupLocation: null == pickupLocation
          ? _value.pickupLocation
          : pickupLocation // ignore: cast_nullable_to_non_nullable
              as RideLocation,
      dropoffLocation: null == dropoffLocation
          ? _value.dropoffLocation
          : dropoffLocation // ignore: cast_nullable_to_non_nullable
              as RideLocation,
      fixedFare: null == fixedFare
          ? _value.fixedFare
          : fixedFare // ignore: cast_nullable_to_non_nullable
              as double,
      paymentStatus: null == paymentStatus
          ? _value.paymentStatus
          : paymentStatus // ignore: cast_nullable_to_non_nullable
              as PaymentStatus,
      acceptedAt: freezed == acceptedAt
          ? _value.acceptedAt
          : acceptedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      startedAt: freezed == startedAt
          ? _value.startedAt
          : startedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      completedAt: freezed == completedAt
          ? _value.completedAt
          : completedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      cancelledAt: freezed == cancelledAt
          ? _value.cancelledAt
          : cancelledAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      specialInstructions: freezed == specialInstructions
          ? _value.specialInstructions
          : specialInstructions // ignore: cast_nullable_to_non_nullable
              as String?,
      cancellationReason: freezed == cancellationReason
          ? _value.cancellationReason
          : cancellationReason // ignore: cast_nullable_to_non_nullable
              as CancellationReason?,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }

  /// Create a copy of Ride
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RideLocationCopyWith<$Res> get pickupLocation {
    return $RideLocationCopyWith<$Res>(_value.pickupLocation, (value) {
      return _then(_value.copyWith(pickupLocation: value) as $Val);
    });
  }

  /// Create a copy of Ride
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RideLocationCopyWith<$Res> get dropoffLocation {
    return $RideLocationCopyWith<$Res>(_value.dropoffLocation, (value) {
      return _then(_value.copyWith(dropoffLocation: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$RideImplCopyWith<$Res> implements $RideCopyWith<$Res> {
  factory _$$RideImplCopyWith(
          _$RideImpl value, $Res Function(_$RideImpl) then) =
      __$$RideImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String riderId,
      String? driverId,
      RideStatus status,
      RideLocation pickupLocation,
      RideLocation dropoffLocation,
      double fixedFare,
      PaymentStatus paymentStatus,
      DateTime? acceptedAt,
      DateTime? startedAt,
      DateTime? completedAt,
      DateTime? cancelledAt,
      String? specialInstructions,
      CancellationReason? cancellationReason,
      DateTime createdAt});

  @override
  $RideLocationCopyWith<$Res> get pickupLocation;
  @override
  $RideLocationCopyWith<$Res> get dropoffLocation;
}

/// @nodoc
class __$$RideImplCopyWithImpl<$Res>
    extends _$RideCopyWithImpl<$Res, _$RideImpl>
    implements _$$RideImplCopyWith<$Res> {
  __$$RideImplCopyWithImpl(_$RideImpl _value, $Res Function(_$RideImpl) _then)
      : super(_value, _then);

  /// Create a copy of Ride
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? riderId = null,
    Object? driverId = freezed,
    Object? status = null,
    Object? pickupLocation = null,
    Object? dropoffLocation = null,
    Object? fixedFare = null,
    Object? paymentStatus = null,
    Object? acceptedAt = freezed,
    Object? startedAt = freezed,
    Object? completedAt = freezed,
    Object? cancelledAt = freezed,
    Object? specialInstructions = freezed,
    Object? cancellationReason = freezed,
    Object? createdAt = null,
  }) {
    return _then(_$RideImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      riderId: null == riderId
          ? _value.riderId
          : riderId // ignore: cast_nullable_to_non_nullable
              as String,
      driverId: freezed == driverId
          ? _value.driverId
          : driverId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as RideStatus,
      pickupLocation: null == pickupLocation
          ? _value.pickupLocation
          : pickupLocation // ignore: cast_nullable_to_non_nullable
              as RideLocation,
      dropoffLocation: null == dropoffLocation
          ? _value.dropoffLocation
          : dropoffLocation // ignore: cast_nullable_to_non_nullable
              as RideLocation,
      fixedFare: null == fixedFare
          ? _value.fixedFare
          : fixedFare // ignore: cast_nullable_to_non_nullable
              as double,
      paymentStatus: null == paymentStatus
          ? _value.paymentStatus
          : paymentStatus // ignore: cast_nullable_to_non_nullable
              as PaymentStatus,
      acceptedAt: freezed == acceptedAt
          ? _value.acceptedAt
          : acceptedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      startedAt: freezed == startedAt
          ? _value.startedAt
          : startedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      completedAt: freezed == completedAt
          ? _value.completedAt
          : completedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      cancelledAt: freezed == cancelledAt
          ? _value.cancelledAt
          : cancelledAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      specialInstructions: freezed == specialInstructions
          ? _value.specialInstructions
          : specialInstructions // ignore: cast_nullable_to_non_nullable
              as String?,
      cancellationReason: freezed == cancellationReason
          ? _value.cancellationReason
          : cancellationReason // ignore: cast_nullable_to_non_nullable
              as CancellationReason?,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RideImpl extends _Ride {
  const _$RideImpl(
      {required this.id,
      required this.riderId,
      this.driverId,
      required this.status,
      required this.pickupLocation,
      required this.dropoffLocation,
      required this.fixedFare,
      required this.paymentStatus,
      this.acceptedAt,
      this.startedAt,
      this.completedAt,
      this.cancelledAt,
      this.specialInstructions,
      this.cancellationReason,
      required this.createdAt})
      : super._();

  factory _$RideImpl.fromJson(Map<String, dynamic> json) =>
      _$$RideImplFromJson(json);

  /// Unique identifier for the ride
  @override
  final String id;

  /// ID of the rider who requested the ride
  @override
  final String riderId;

  /// ID of the driver assigned to the ride (null if not yet assigned)
  @override
  final String? driverId;

  /// Current status of the ride
  @override
  final RideStatus status;

  /// Pickup location details
  @override
  final RideLocation pickupLocation;

  /// Dropoff location details
  @override
  final RideLocation dropoffLocation;

  /// Fixed fare price for the ride
  @override
  final double fixedFare;

  /// Current payment status
  @override
  final PaymentStatus paymentStatus;

  /// Timestamp when the ride was accepted by a driver
  @override
  final DateTime? acceptedAt;

  /// Timestamp when the ride was started (passenger picked up)
  @override
  final DateTime? startedAt;

  /// Timestamp when the ride was completed
  @override
  final DateTime? completedAt;

  /// Timestamp when the ride was cancelled (if applicable)
  @override
  final DateTime? cancelledAt;

  /// Special instructions from the rider to the driver
  @override
  final String? specialInstructions;

  /// Reason for cancellation (if applicable)
  @override
  final CancellationReason? cancellationReason;

  /// Timestamp when the ride was created
  @override
  final DateTime createdAt;

  @override
  String toString() {
    return 'Ride(id: $id, riderId: $riderId, driverId: $driverId, status: $status, pickupLocation: $pickupLocation, dropoffLocation: $dropoffLocation, fixedFare: $fixedFare, paymentStatus: $paymentStatus, acceptedAt: $acceptedAt, startedAt: $startedAt, completedAt: $completedAt, cancelledAt: $cancelledAt, specialInstructions: $specialInstructions, cancellationReason: $cancellationReason, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RideImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.riderId, riderId) || other.riderId == riderId) &&
            (identical(other.driverId, driverId) ||
                other.driverId == driverId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.pickupLocation, pickupLocation) ||
                other.pickupLocation == pickupLocation) &&
            (identical(other.dropoffLocation, dropoffLocation) ||
                other.dropoffLocation == dropoffLocation) &&
            (identical(other.fixedFare, fixedFare) ||
                other.fixedFare == fixedFare) &&
            (identical(other.paymentStatus, paymentStatus) ||
                other.paymentStatus == paymentStatus) &&
            (identical(other.acceptedAt, acceptedAt) ||
                other.acceptedAt == acceptedAt) &&
            (identical(other.startedAt, startedAt) ||
                other.startedAt == startedAt) &&
            (identical(other.completedAt, completedAt) ||
                other.completedAt == completedAt) &&
            (identical(other.cancelledAt, cancelledAt) ||
                other.cancelledAt == cancelledAt) &&
            (identical(other.specialInstructions, specialInstructions) ||
                other.specialInstructions == specialInstructions) &&
            (identical(other.cancellationReason, cancellationReason) ||
                other.cancellationReason == cancellationReason) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      riderId,
      driverId,
      status,
      pickupLocation,
      dropoffLocation,
      fixedFare,
      paymentStatus,
      acceptedAt,
      startedAt,
      completedAt,
      cancelledAt,
      specialInstructions,
      cancellationReason,
      createdAt);

  /// Create a copy of Ride
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RideImplCopyWith<_$RideImpl> get copyWith =>
      __$$RideImplCopyWithImpl<_$RideImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RideImplToJson(
      this,
    );
  }
}

abstract class _Ride extends Ride {
  const factory _Ride(
      {required final String id,
      required final String riderId,
      final String? driverId,
      required final RideStatus status,
      required final RideLocation pickupLocation,
      required final RideLocation dropoffLocation,
      required final double fixedFare,
      required final PaymentStatus paymentStatus,
      final DateTime? acceptedAt,
      final DateTime? startedAt,
      final DateTime? completedAt,
      final DateTime? cancelledAt,
      final String? specialInstructions,
      final CancellationReason? cancellationReason,
      required final DateTime createdAt}) = _$RideImpl;
  const _Ride._() : super._();

  factory _Ride.fromJson(Map<String, dynamic> json) = _$RideImpl.fromJson;

  /// Unique identifier for the ride
  @override
  String get id;

  /// ID of the rider who requested the ride
  @override
  String get riderId;

  /// ID of the driver assigned to the ride (null if not yet assigned)
  @override
  String? get driverId;

  /// Current status of the ride
  @override
  RideStatus get status;

  /// Pickup location details
  @override
  RideLocation get pickupLocation;

  /// Dropoff location details
  @override
  RideLocation get dropoffLocation;

  /// Fixed fare price for the ride
  @override
  double get fixedFare;

  /// Current payment status
  @override
  PaymentStatus get paymentStatus;

  /// Timestamp when the ride was accepted by a driver
  @override
  DateTime? get acceptedAt;

  /// Timestamp when the ride was started (passenger picked up)
  @override
  DateTime? get startedAt;

  /// Timestamp when the ride was completed
  @override
  DateTime? get completedAt;

  /// Timestamp when the ride was cancelled (if applicable)
  @override
  DateTime? get cancelledAt;

  /// Special instructions from the rider to the driver
  @override
  String? get specialInstructions;

  /// Reason for cancellation (if applicable)
  @override
  CancellationReason? get cancellationReason;

  /// Timestamp when the ride was created
  @override
  DateTime get createdAt;

  /// Create a copy of Ride
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RideImplCopyWith<_$RideImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

RideHistory _$RideHistoryFromJson(Map<String, dynamic> json) {
  return _RideHistory.fromJson(json);
}

/// @nodoc
mixin _$RideHistory {
  /// Unique identifier for the ride
  String get id => throw _privateConstructorUsedError;

  /// Name of the pickup location
  String get pickupName => throw _privateConstructorUsedError;

  /// Name of the dropoff location
  String get dropoffName => throw _privateConstructorUsedError;

  /// Fixed fare price for the ride
  double get fare => throw _privateConstructorUsedError;

  /// Current status of the ride
  RideStatus get status => throw _privateConstructorUsedError;

  /// Timestamp when the ride was created
  DateTime get createdAt => throw _privateConstructorUsedError;

  /// Timestamp when the ride was completed (if applicable)
  DateTime? get completedAt => throw _privateConstructorUsedError;

  /// Driver name (if assigned)
  String? get driverName => throw _privateConstructorUsedError;

  /// Driver rating (if applicable and ride completed)
  double? get driverRating => throw _privateConstructorUsedError;

  /// Serializes this RideHistory to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RideHistory
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RideHistoryCopyWith<RideHistory> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RideHistoryCopyWith<$Res> {
  factory $RideHistoryCopyWith(
          RideHistory value, $Res Function(RideHistory) then) =
      _$RideHistoryCopyWithImpl<$Res, RideHistory>;
  @useResult
  $Res call(
      {String id,
      String pickupName,
      String dropoffName,
      double fare,
      RideStatus status,
      DateTime createdAt,
      DateTime? completedAt,
      String? driverName,
      double? driverRating});
}

/// @nodoc
class _$RideHistoryCopyWithImpl<$Res, $Val extends RideHistory>
    implements $RideHistoryCopyWith<$Res> {
  _$RideHistoryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RideHistory
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? pickupName = null,
    Object? dropoffName = null,
    Object? fare = null,
    Object? status = null,
    Object? createdAt = null,
    Object? completedAt = freezed,
    Object? driverName = freezed,
    Object? driverRating = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      pickupName: null == pickupName
          ? _value.pickupName
          : pickupName // ignore: cast_nullable_to_non_nullable
              as String,
      dropoffName: null == dropoffName
          ? _value.dropoffName
          : dropoffName // ignore: cast_nullable_to_non_nullable
              as String,
      fare: null == fare
          ? _value.fare
          : fare // ignore: cast_nullable_to_non_nullable
              as double,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as RideStatus,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      completedAt: freezed == completedAt
          ? _value.completedAt
          : completedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      driverName: freezed == driverName
          ? _value.driverName
          : driverName // ignore: cast_nullable_to_non_nullable
              as String?,
      driverRating: freezed == driverRating
          ? _value.driverRating
          : driverRating // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RideHistoryImplCopyWith<$Res>
    implements $RideHistoryCopyWith<$Res> {
  factory _$$RideHistoryImplCopyWith(
          _$RideHistoryImpl value, $Res Function(_$RideHistoryImpl) then) =
      __$$RideHistoryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String pickupName,
      String dropoffName,
      double fare,
      RideStatus status,
      DateTime createdAt,
      DateTime? completedAt,
      String? driverName,
      double? driverRating});
}

/// @nodoc
class __$$RideHistoryImplCopyWithImpl<$Res>
    extends _$RideHistoryCopyWithImpl<$Res, _$RideHistoryImpl>
    implements _$$RideHistoryImplCopyWith<$Res> {
  __$$RideHistoryImplCopyWithImpl(
      _$RideHistoryImpl _value, $Res Function(_$RideHistoryImpl) _then)
      : super(_value, _then);

  /// Create a copy of RideHistory
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? pickupName = null,
    Object? dropoffName = null,
    Object? fare = null,
    Object? status = null,
    Object? createdAt = null,
    Object? completedAt = freezed,
    Object? driverName = freezed,
    Object? driverRating = freezed,
  }) {
    return _then(_$RideHistoryImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      pickupName: null == pickupName
          ? _value.pickupName
          : pickupName // ignore: cast_nullable_to_non_nullable
              as String,
      dropoffName: null == dropoffName
          ? _value.dropoffName
          : dropoffName // ignore: cast_nullable_to_non_nullable
              as String,
      fare: null == fare
          ? _value.fare
          : fare // ignore: cast_nullable_to_non_nullable
              as double,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as RideStatus,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      completedAt: freezed == completedAt
          ? _value.completedAt
          : completedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      driverName: freezed == driverName
          ? _value.driverName
          : driverName // ignore: cast_nullable_to_non_nullable
              as String?,
      driverRating: freezed == driverRating
          ? _value.driverRating
          : driverRating // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RideHistoryImpl extends _RideHistory {
  const _$RideHistoryImpl(
      {required this.id,
      required this.pickupName,
      required this.dropoffName,
      required this.fare,
      required this.status,
      required this.createdAt,
      this.completedAt,
      this.driverName,
      this.driverRating})
      : super._();

  factory _$RideHistoryImpl.fromJson(Map<String, dynamic> json) =>
      _$$RideHistoryImplFromJson(json);

  /// Unique identifier for the ride
  @override
  final String id;

  /// Name of the pickup location
  @override
  final String pickupName;

  /// Name of the dropoff location
  @override
  final String dropoffName;

  /// Fixed fare price for the ride
  @override
  final double fare;

  /// Current status of the ride
  @override
  final RideStatus status;

  /// Timestamp when the ride was created
  @override
  final DateTime createdAt;

  /// Timestamp when the ride was completed (if applicable)
  @override
  final DateTime? completedAt;

  /// Driver name (if assigned)
  @override
  final String? driverName;

  /// Driver rating (if applicable and ride completed)
  @override
  final double? driverRating;

  @override
  String toString() {
    return 'RideHistory(id: $id, pickupName: $pickupName, dropoffName: $dropoffName, fare: $fare, status: $status, createdAt: $createdAt, completedAt: $completedAt, driverName: $driverName, driverRating: $driverRating)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RideHistoryImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.pickupName, pickupName) ||
                other.pickupName == pickupName) &&
            (identical(other.dropoffName, dropoffName) ||
                other.dropoffName == dropoffName) &&
            (identical(other.fare, fare) || other.fare == fare) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.completedAt, completedAt) ||
                other.completedAt == completedAt) &&
            (identical(other.driverName, driverName) ||
                other.driverName == driverName) &&
            (identical(other.driverRating, driverRating) ||
                other.driverRating == driverRating));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, pickupName, dropoffName,
      fare, status, createdAt, completedAt, driverName, driverRating);

  /// Create a copy of RideHistory
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RideHistoryImplCopyWith<_$RideHistoryImpl> get copyWith =>
      __$$RideHistoryImplCopyWithImpl<_$RideHistoryImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RideHistoryImplToJson(
      this,
    );
  }
}

abstract class _RideHistory extends RideHistory {
  const factory _RideHistory(
      {required final String id,
      required final String pickupName,
      required final String dropoffName,
      required final double fare,
      required final RideStatus status,
      required final DateTime createdAt,
      final DateTime? completedAt,
      final String? driverName,
      final double? driverRating}) = _$RideHistoryImpl;
  const _RideHistory._() : super._();

  factory _RideHistory.fromJson(Map<String, dynamic> json) =
      _$RideHistoryImpl.fromJson;

  /// Unique identifier for the ride
  @override
  String get id;

  /// Name of the pickup location
  @override
  String get pickupName;

  /// Name of the dropoff location
  @override
  String get dropoffName;

  /// Fixed fare price for the ride
  @override
  double get fare;

  /// Current status of the ride
  @override
  RideStatus get status;

  /// Timestamp when the ride was created
  @override
  DateTime get createdAt;

  /// Timestamp when the ride was completed (if applicable)
  @override
  DateTime? get completedAt;

  /// Driver name (if assigned)
  @override
  String? get driverName;

  /// Driver rating (if applicable and ride completed)
  @override
  double? get driverRating;

  /// Create a copy of RideHistory
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RideHistoryImplCopyWith<_$RideHistoryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

RideReceipt _$RideReceiptFromJson(Map<String, dynamic> json) {
  return _RideReceipt.fromJson(json);
}

/// @nodoc
mixin _$RideReceipt {
  /// Unique identifier for the ride
  String get rideId => throw _privateConstructorUsedError;

  /// Receipt number for reference
  String get receiptNumber => throw _privateConstructorUsedError;

  /// Base fare amount
  double get baseFare => throw _privateConstructorUsedError;

  /// Any additional fees
  double get additionalFees => throw _privateConstructorUsedError;

  /// Tax amount
  double get tax => throw _privateConstructorUsedError;

  /// Total amount charged
  double get totalAmount => throw _privateConstructorUsedError;

  /// Payment method used
  String get paymentMethod => throw _privateConstructorUsedError;

  /// Timestamp when payment was processed
  DateTime get paymentDate => throw _privateConstructorUsedError;

  /// Pickup location name
  String get pickupLocation => throw _privateConstructorUsedError;

  /// Dropoff location name
  String get dropoffLocation => throw _privateConstructorUsedError;

  /// Ride distance in kilometers
  double get distanceKm => throw _privateConstructorUsedError;

  /// Ride duration in minutes
  int get durationMinutes => throw _privateConstructorUsedError;

  /// Driver name
  String get driverName => throw _privateConstructorUsedError;

  /// Driver vehicle information
  String get vehicleInfo => throw _privateConstructorUsedError;

  /// Serializes this RideReceipt to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RideReceipt
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RideReceiptCopyWith<RideReceipt> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RideReceiptCopyWith<$Res> {
  factory $RideReceiptCopyWith(
          RideReceipt value, $Res Function(RideReceipt) then) =
      _$RideReceiptCopyWithImpl<$Res, RideReceipt>;
  @useResult
  $Res call(
      {String rideId,
      String receiptNumber,
      double baseFare,
      double additionalFees,
      double tax,
      double totalAmount,
      String paymentMethod,
      DateTime paymentDate,
      String pickupLocation,
      String dropoffLocation,
      double distanceKm,
      int durationMinutes,
      String driverName,
      String vehicleInfo});
}

/// @nodoc
class _$RideReceiptCopyWithImpl<$Res, $Val extends RideReceipt>
    implements $RideReceiptCopyWith<$Res> {
  _$RideReceiptCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RideReceipt
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? rideId = null,
    Object? receiptNumber = null,
    Object? baseFare = null,
    Object? additionalFees = null,
    Object? tax = null,
    Object? totalAmount = null,
    Object? paymentMethod = null,
    Object? paymentDate = null,
    Object? pickupLocation = null,
    Object? dropoffLocation = null,
    Object? distanceKm = null,
    Object? durationMinutes = null,
    Object? driverName = null,
    Object? vehicleInfo = null,
  }) {
    return _then(_value.copyWith(
      rideId: null == rideId
          ? _value.rideId
          : rideId // ignore: cast_nullable_to_non_nullable
              as String,
      receiptNumber: null == receiptNumber
          ? _value.receiptNumber
          : receiptNumber // ignore: cast_nullable_to_non_nullable
              as String,
      baseFare: null == baseFare
          ? _value.baseFare
          : baseFare // ignore: cast_nullable_to_non_nullable
              as double,
      additionalFees: null == additionalFees
          ? _value.additionalFees
          : additionalFees // ignore: cast_nullable_to_non_nullable
              as double,
      tax: null == tax
          ? _value.tax
          : tax // ignore: cast_nullable_to_non_nullable
              as double,
      totalAmount: null == totalAmount
          ? _value.totalAmount
          : totalAmount // ignore: cast_nullable_to_non_nullable
              as double,
      paymentMethod: null == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as String,
      paymentDate: null == paymentDate
          ? _value.paymentDate
          : paymentDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      pickupLocation: null == pickupLocation
          ? _value.pickupLocation
          : pickupLocation // ignore: cast_nullable_to_non_nullable
              as String,
      dropoffLocation: null == dropoffLocation
          ? _value.dropoffLocation
          : dropoffLocation // ignore: cast_nullable_to_non_nullable
              as String,
      distanceKm: null == distanceKm
          ? _value.distanceKm
          : distanceKm // ignore: cast_nullable_to_non_nullable
              as double,
      durationMinutes: null == durationMinutes
          ? _value.durationMinutes
          : durationMinutes // ignore: cast_nullable_to_non_nullable
              as int,
      driverName: null == driverName
          ? _value.driverName
          : driverName // ignore: cast_nullable_to_non_nullable
              as String,
      vehicleInfo: null == vehicleInfo
          ? _value.vehicleInfo
          : vehicleInfo // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RideReceiptImplCopyWith<$Res>
    implements $RideReceiptCopyWith<$Res> {
  factory _$$RideReceiptImplCopyWith(
          _$RideReceiptImpl value, $Res Function(_$RideReceiptImpl) then) =
      __$$RideReceiptImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String rideId,
      String receiptNumber,
      double baseFare,
      double additionalFees,
      double tax,
      double totalAmount,
      String paymentMethod,
      DateTime paymentDate,
      String pickupLocation,
      String dropoffLocation,
      double distanceKm,
      int durationMinutes,
      String driverName,
      String vehicleInfo});
}

/// @nodoc
class __$$RideReceiptImplCopyWithImpl<$Res>
    extends _$RideReceiptCopyWithImpl<$Res, _$RideReceiptImpl>
    implements _$$RideReceiptImplCopyWith<$Res> {
  __$$RideReceiptImplCopyWithImpl(
      _$RideReceiptImpl _value, $Res Function(_$RideReceiptImpl) _then)
      : super(_value, _then);

  /// Create a copy of RideReceipt
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? rideId = null,
    Object? receiptNumber = null,
    Object? baseFare = null,
    Object? additionalFees = null,
    Object? tax = null,
    Object? totalAmount = null,
    Object? paymentMethod = null,
    Object? paymentDate = null,
    Object? pickupLocation = null,
    Object? dropoffLocation = null,
    Object? distanceKm = null,
    Object? durationMinutes = null,
    Object? driverName = null,
    Object? vehicleInfo = null,
  }) {
    return _then(_$RideReceiptImpl(
      rideId: null == rideId
          ? _value.rideId
          : rideId // ignore: cast_nullable_to_non_nullable
              as String,
      receiptNumber: null == receiptNumber
          ? _value.receiptNumber
          : receiptNumber // ignore: cast_nullable_to_non_nullable
              as String,
      baseFare: null == baseFare
          ? _value.baseFare
          : baseFare // ignore: cast_nullable_to_non_nullable
              as double,
      additionalFees: null == additionalFees
          ? _value.additionalFees
          : additionalFees // ignore: cast_nullable_to_non_nullable
              as double,
      tax: null == tax
          ? _value.tax
          : tax // ignore: cast_nullable_to_non_nullable
              as double,
      totalAmount: null == totalAmount
          ? _value.totalAmount
          : totalAmount // ignore: cast_nullable_to_non_nullable
              as double,
      paymentMethod: null == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as String,
      paymentDate: null == paymentDate
          ? _value.paymentDate
          : paymentDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      pickupLocation: null == pickupLocation
          ? _value.pickupLocation
          : pickupLocation // ignore: cast_nullable_to_non_nullable
              as String,
      dropoffLocation: null == dropoffLocation
          ? _value.dropoffLocation
          : dropoffLocation // ignore: cast_nullable_to_non_nullable
              as String,
      distanceKm: null == distanceKm
          ? _value.distanceKm
          : distanceKm // ignore: cast_nullable_to_non_nullable
              as double,
      durationMinutes: null == durationMinutes
          ? _value.durationMinutes
          : durationMinutes // ignore: cast_nullable_to_non_nullable
              as int,
      driverName: null == driverName
          ? _value.driverName
          : driverName // ignore: cast_nullable_to_non_nullable
              as String,
      vehicleInfo: null == vehicleInfo
          ? _value.vehicleInfo
          : vehicleInfo // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RideReceiptImpl extends _RideReceipt {
  const _$RideReceiptImpl(
      {required this.rideId,
      required this.receiptNumber,
      required this.baseFare,
      this.additionalFees = 0.0,
      required this.tax,
      required this.totalAmount,
      required this.paymentMethod,
      required this.paymentDate,
      required this.pickupLocation,
      required this.dropoffLocation,
      required this.distanceKm,
      required this.durationMinutes,
      required this.driverName,
      required this.vehicleInfo})
      : super._();

  factory _$RideReceiptImpl.fromJson(Map<String, dynamic> json) =>
      _$$RideReceiptImplFromJson(json);

  /// Unique identifier for the ride
  @override
  final String rideId;

  /// Receipt number for reference
  @override
  final String receiptNumber;

  /// Base fare amount
  @override
  final double baseFare;

  /// Any additional fees
  @override
  @JsonKey()
  final double additionalFees;

  /// Tax amount
  @override
  final double tax;

  /// Total amount charged
  @override
  final double totalAmount;

  /// Payment method used
  @override
  final String paymentMethod;

  /// Timestamp when payment was processed
  @override
  final DateTime paymentDate;

  /// Pickup location name
  @override
  final String pickupLocation;

  /// Dropoff location name
  @override
  final String dropoffLocation;

  /// Ride distance in kilometers
  @override
  final double distanceKm;

  /// Ride duration in minutes
  @override
  final int durationMinutes;

  /// Driver name
  @override
  final String driverName;

  /// Driver vehicle information
  @override
  final String vehicleInfo;

  @override
  String toString() {
    return 'RideReceipt(rideId: $rideId, receiptNumber: $receiptNumber, baseFare: $baseFare, additionalFees: $additionalFees, tax: $tax, totalAmount: $totalAmount, paymentMethod: $paymentMethod, paymentDate: $paymentDate, pickupLocation: $pickupLocation, dropoffLocation: $dropoffLocation, distanceKm: $distanceKm, durationMinutes: $durationMinutes, driverName: $driverName, vehicleInfo: $vehicleInfo)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RideReceiptImpl &&
            (identical(other.rideId, rideId) || other.rideId == rideId) &&
            (identical(other.receiptNumber, receiptNumber) ||
                other.receiptNumber == receiptNumber) &&
            (identical(other.baseFare, baseFare) ||
                other.baseFare == baseFare) &&
            (identical(other.additionalFees, additionalFees) ||
                other.additionalFees == additionalFees) &&
            (identical(other.tax, tax) || other.tax == tax) &&
            (identical(other.totalAmount, totalAmount) ||
                other.totalAmount == totalAmount) &&
            (identical(other.paymentMethod, paymentMethod) ||
                other.paymentMethod == paymentMethod) &&
            (identical(other.paymentDate, paymentDate) ||
                other.paymentDate == paymentDate) &&
            (identical(other.pickupLocation, pickupLocation) ||
                other.pickupLocation == pickupLocation) &&
            (identical(other.dropoffLocation, dropoffLocation) ||
                other.dropoffLocation == dropoffLocation) &&
            (identical(other.distanceKm, distanceKm) ||
                other.distanceKm == distanceKm) &&
            (identical(other.durationMinutes, durationMinutes) ||
                other.durationMinutes == durationMinutes) &&
            (identical(other.driverName, driverName) ||
                other.driverName == driverName) &&
            (identical(other.vehicleInfo, vehicleInfo) ||
                other.vehicleInfo == vehicleInfo));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      rideId,
      receiptNumber,
      baseFare,
      additionalFees,
      tax,
      totalAmount,
      paymentMethod,
      paymentDate,
      pickupLocation,
      dropoffLocation,
      distanceKm,
      durationMinutes,
      driverName,
      vehicleInfo);

  /// Create a copy of RideReceipt
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RideReceiptImplCopyWith<_$RideReceiptImpl> get copyWith =>
      __$$RideReceiptImplCopyWithImpl<_$RideReceiptImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RideReceiptImplToJson(
      this,
    );
  }
}

abstract class _RideReceipt extends RideReceipt {
  const factory _RideReceipt(
      {required final String rideId,
      required final String receiptNumber,
      required final double baseFare,
      final double additionalFees,
      required final double tax,
      required final double totalAmount,
      required final String paymentMethod,
      required final DateTime paymentDate,
      required final String pickupLocation,
      required final String dropoffLocation,
      required final double distanceKm,
      required final int durationMinutes,
      required final String driverName,
      required final String vehicleInfo}) = _$RideReceiptImpl;
  const _RideReceipt._() : super._();

  factory _RideReceipt.fromJson(Map<String, dynamic> json) =
      _$RideReceiptImpl.fromJson;

  /// Unique identifier for the ride
  @override
  String get rideId;

  /// Receipt number for reference
  @override
  String get receiptNumber;

  /// Base fare amount
  @override
  double get baseFare;

  /// Any additional fees
  @override
  double get additionalFees;

  /// Tax amount
  @override
  double get tax;

  /// Total amount charged
  @override
  double get totalAmount;

  /// Payment method used
  @override
  String get paymentMethod;

  /// Timestamp when payment was processed
  @override
  DateTime get paymentDate;

  /// Pickup location name
  @override
  String get pickupLocation;

  /// Dropoff location name
  @override
  String get dropoffLocation;

  /// Ride distance in kilometers
  @override
  double get distanceKm;

  /// Ride duration in minutes
  @override
  int get durationMinutes;

  /// Driver name
  @override
  String get driverName;

  /// Driver vehicle information
  @override
  String get vehicleInfo;

  /// Create a copy of RideReceipt
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RideReceiptImplCopyWith<_$RideReceiptImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

RideRequestCreate _$RideRequestCreateFromJson(Map<String, dynamic> json) {
  return _RideRequestCreate.fromJson(json);
}

/// @nodoc
mixin _$RideRequestCreate {
  /// Pickup location details
  RideLocation get pickupLocation => throw _privateConstructorUsedError;

  /// Dropoff location details
  RideLocation get dropoffLocation => throw _privateConstructorUsedError;

  /// Special instructions for the driver (optional)
  String? get specialInstructions => throw _privateConstructorUsedError;

  /// Serializes this RideRequestCreate to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RideRequestCreate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RideRequestCreateCopyWith<RideRequestCreate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RideRequestCreateCopyWith<$Res> {
  factory $RideRequestCreateCopyWith(
          RideRequestCreate value, $Res Function(RideRequestCreate) then) =
      _$RideRequestCreateCopyWithImpl<$Res, RideRequestCreate>;
  @useResult
  $Res call(
      {RideLocation pickupLocation,
      RideLocation dropoffLocation,
      String? specialInstructions});

  $RideLocationCopyWith<$Res> get pickupLocation;
  $RideLocationCopyWith<$Res> get dropoffLocation;
}

/// @nodoc
class _$RideRequestCreateCopyWithImpl<$Res, $Val extends RideRequestCreate>
    implements $RideRequestCreateCopyWith<$Res> {
  _$RideRequestCreateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RideRequestCreate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pickupLocation = null,
    Object? dropoffLocation = null,
    Object? specialInstructions = freezed,
  }) {
    return _then(_value.copyWith(
      pickupLocation: null == pickupLocation
          ? _value.pickupLocation
          : pickupLocation // ignore: cast_nullable_to_non_nullable
              as RideLocation,
      dropoffLocation: null == dropoffLocation
          ? _value.dropoffLocation
          : dropoffLocation // ignore: cast_nullable_to_non_nullable
              as RideLocation,
      specialInstructions: freezed == specialInstructions
          ? _value.specialInstructions
          : specialInstructions // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of RideRequestCreate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RideLocationCopyWith<$Res> get pickupLocation {
    return $RideLocationCopyWith<$Res>(_value.pickupLocation, (value) {
      return _then(_value.copyWith(pickupLocation: value) as $Val);
    });
  }

  /// Create a copy of RideRequestCreate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RideLocationCopyWith<$Res> get dropoffLocation {
    return $RideLocationCopyWith<$Res>(_value.dropoffLocation, (value) {
      return _then(_value.copyWith(dropoffLocation: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$RideRequestCreateImplCopyWith<$Res>
    implements $RideRequestCreateCopyWith<$Res> {
  factory _$$RideRequestCreateImplCopyWith(_$RideRequestCreateImpl value,
          $Res Function(_$RideRequestCreateImpl) then) =
      __$$RideRequestCreateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {RideLocation pickupLocation,
      RideLocation dropoffLocation,
      String? specialInstructions});

  @override
  $RideLocationCopyWith<$Res> get pickupLocation;
  @override
  $RideLocationCopyWith<$Res> get dropoffLocation;
}

/// @nodoc
class __$$RideRequestCreateImplCopyWithImpl<$Res>
    extends _$RideRequestCreateCopyWithImpl<$Res, _$RideRequestCreateImpl>
    implements _$$RideRequestCreateImplCopyWith<$Res> {
  __$$RideRequestCreateImplCopyWithImpl(_$RideRequestCreateImpl _value,
      $Res Function(_$RideRequestCreateImpl) _then)
      : super(_value, _then);

  /// Create a copy of RideRequestCreate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pickupLocation = null,
    Object? dropoffLocation = null,
    Object? specialInstructions = freezed,
  }) {
    return _then(_$RideRequestCreateImpl(
      pickupLocation: null == pickupLocation
          ? _value.pickupLocation
          : pickupLocation // ignore: cast_nullable_to_non_nullable
              as RideLocation,
      dropoffLocation: null == dropoffLocation
          ? _value.dropoffLocation
          : dropoffLocation // ignore: cast_nullable_to_non_nullable
              as RideLocation,
      specialInstructions: freezed == specialInstructions
          ? _value.specialInstructions
          : specialInstructions // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RideRequestCreateImpl extends _RideRequestCreate {
  const _$RideRequestCreateImpl(
      {required this.pickupLocation,
      required this.dropoffLocation,
      this.specialInstructions})
      : super._();

  factory _$RideRequestCreateImpl.fromJson(Map<String, dynamic> json) =>
      _$$RideRequestCreateImplFromJson(json);

  /// Pickup location details
  @override
  final RideLocation pickupLocation;

  /// Dropoff location details
  @override
  final RideLocation dropoffLocation;

  /// Special instructions for the driver (optional)
  @override
  final String? specialInstructions;

  @override
  String toString() {
    return 'RideRequestCreate(pickupLocation: $pickupLocation, dropoffLocation: $dropoffLocation, specialInstructions: $specialInstructions)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RideRequestCreateImpl &&
            (identical(other.pickupLocation, pickupLocation) ||
                other.pickupLocation == pickupLocation) &&
            (identical(other.dropoffLocation, dropoffLocation) ||
                other.dropoffLocation == dropoffLocation) &&
            (identical(other.specialInstructions, specialInstructions) ||
                other.specialInstructions == specialInstructions));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, pickupLocation, dropoffLocation, specialInstructions);

  /// Create a copy of RideRequestCreate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RideRequestCreateImplCopyWith<_$RideRequestCreateImpl> get copyWith =>
      __$$RideRequestCreateImplCopyWithImpl<_$RideRequestCreateImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RideRequestCreateImplToJson(
      this,
    );
  }
}

abstract class _RideRequestCreate extends RideRequestCreate {
  const factory _RideRequestCreate(
      {required final RideLocation pickupLocation,
      required final RideLocation dropoffLocation,
      final String? specialInstructions}) = _$RideRequestCreateImpl;
  const _RideRequestCreate._() : super._();

  factory _RideRequestCreate.fromJson(Map<String, dynamic> json) =
      _$RideRequestCreateImpl.fromJson;

  /// Pickup location details
  @override
  RideLocation get pickupLocation;

  /// Dropoff location details
  @override
  RideLocation get dropoffLocation;

  /// Special instructions for the driver (optional)
  @override
  String? get specialInstructions;

  /// Create a copy of RideRequestCreate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RideRequestCreateImplCopyWith<_$RideRequestCreateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PricingInfo _$PricingInfoFromJson(Map<String, dynamic> json) {
  return _PricingInfo.fromJson(json);
}

/// @nodoc
mixin _$PricingInfo {
  /// Base fare amount
  double get baseFare => throw _privateConstructorUsedError;

  /// Distance-based fare component
  double get distanceFare => throw _privateConstructorUsedError;

  /// Any additional fees
  double get additionalFees => throw _privateConstructorUsedError;

  /// Tax amount
  double get tax => throw _privateConstructorUsedError;

  /// Total fare amount
  double get totalFare => throw _privateConstructorUsedError;

  /// Estimated distance in kilometers
  double get estimatedDistanceKm => throw _privateConstructorUsedError;

  /// Estimated duration in minutes
  int get estimatedDurationMinutes => throw _privateConstructorUsedError;

  /// Serializes this PricingInfo to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PricingInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PricingInfoCopyWith<PricingInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PricingInfoCopyWith<$Res> {
  factory $PricingInfoCopyWith(
          PricingInfo value, $Res Function(PricingInfo) then) =
      _$PricingInfoCopyWithImpl<$Res, PricingInfo>;
  @useResult
  $Res call(
      {double baseFare,
      double distanceFare,
      double additionalFees,
      double tax,
      double totalFare,
      double estimatedDistanceKm,
      int estimatedDurationMinutes});
}

/// @nodoc
class _$PricingInfoCopyWithImpl<$Res, $Val extends PricingInfo>
    implements $PricingInfoCopyWith<$Res> {
  _$PricingInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PricingInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? baseFare = null,
    Object? distanceFare = null,
    Object? additionalFees = null,
    Object? tax = null,
    Object? totalFare = null,
    Object? estimatedDistanceKm = null,
    Object? estimatedDurationMinutes = null,
  }) {
    return _then(_value.copyWith(
      baseFare: null == baseFare
          ? _value.baseFare
          : baseFare // ignore: cast_nullable_to_non_nullable
              as double,
      distanceFare: null == distanceFare
          ? _value.distanceFare
          : distanceFare // ignore: cast_nullable_to_non_nullable
              as double,
      additionalFees: null == additionalFees
          ? _value.additionalFees
          : additionalFees // ignore: cast_nullable_to_non_nullable
              as double,
      tax: null == tax
          ? _value.tax
          : tax // ignore: cast_nullable_to_non_nullable
              as double,
      totalFare: null == totalFare
          ? _value.totalFare
          : totalFare // ignore: cast_nullable_to_non_nullable
              as double,
      estimatedDistanceKm: null == estimatedDistanceKm
          ? _value.estimatedDistanceKm
          : estimatedDistanceKm // ignore: cast_nullable_to_non_nullable
              as double,
      estimatedDurationMinutes: null == estimatedDurationMinutes
          ? _value.estimatedDurationMinutes
          : estimatedDurationMinutes // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PricingInfoImplCopyWith<$Res>
    implements $PricingInfoCopyWith<$Res> {
  factory _$$PricingInfoImplCopyWith(
          _$PricingInfoImpl value, $Res Function(_$PricingInfoImpl) then) =
      __$$PricingInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double baseFare,
      double distanceFare,
      double additionalFees,
      double tax,
      double totalFare,
      double estimatedDistanceKm,
      int estimatedDurationMinutes});
}

/// @nodoc
class __$$PricingInfoImplCopyWithImpl<$Res>
    extends _$PricingInfoCopyWithImpl<$Res, _$PricingInfoImpl>
    implements _$$PricingInfoImplCopyWith<$Res> {
  __$$PricingInfoImplCopyWithImpl(
      _$PricingInfoImpl _value, $Res Function(_$PricingInfoImpl) _then)
      : super(_value, _then);

  /// Create a copy of PricingInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? baseFare = null,
    Object? distanceFare = null,
    Object? additionalFees = null,
    Object? tax = null,
    Object? totalFare = null,
    Object? estimatedDistanceKm = null,
    Object? estimatedDurationMinutes = null,
  }) {
    return _then(_$PricingInfoImpl(
      baseFare: null == baseFare
          ? _value.baseFare
          : baseFare // ignore: cast_nullable_to_non_nullable
              as double,
      distanceFare: null == distanceFare
          ? _value.distanceFare
          : distanceFare // ignore: cast_nullable_to_non_nullable
              as double,
      additionalFees: null == additionalFees
          ? _value.additionalFees
          : additionalFees // ignore: cast_nullable_to_non_nullable
              as double,
      tax: null == tax
          ? _value.tax
          : tax // ignore: cast_nullable_to_non_nullable
              as double,
      totalFare: null == totalFare
          ? _value.totalFare
          : totalFare // ignore: cast_nullable_to_non_nullable
              as double,
      estimatedDistanceKm: null == estimatedDistanceKm
          ? _value.estimatedDistanceKm
          : estimatedDistanceKm // ignore: cast_nullable_to_non_nullable
              as double,
      estimatedDurationMinutes: null == estimatedDurationMinutes
          ? _value.estimatedDurationMinutes
          : estimatedDurationMinutes // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PricingInfoImpl extends _PricingInfo {
  const _$PricingInfoImpl(
      {required this.baseFare,
      required this.distanceFare,
      this.additionalFees = 0.0,
      required this.tax,
      required this.totalFare,
      required this.estimatedDistanceKm,
      required this.estimatedDurationMinutes})
      : super._();

  factory _$PricingInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$PricingInfoImplFromJson(json);

  /// Base fare amount
  @override
  final double baseFare;

  /// Distance-based fare component
  @override
  final double distanceFare;

  /// Any additional fees
  @override
  @JsonKey()
  final double additionalFees;

  /// Tax amount
  @override
  final double tax;

  /// Total fare amount
  @override
  final double totalFare;

  /// Estimated distance in kilometers
  @override
  final double estimatedDistanceKm;

  /// Estimated duration in minutes
  @override
  final int estimatedDurationMinutes;

  @override
  String toString() {
    return 'PricingInfo(baseFare: $baseFare, distanceFare: $distanceFare, additionalFees: $additionalFees, tax: $tax, totalFare: $totalFare, estimatedDistanceKm: $estimatedDistanceKm, estimatedDurationMinutes: $estimatedDurationMinutes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PricingInfoImpl &&
            (identical(other.baseFare, baseFare) ||
                other.baseFare == baseFare) &&
            (identical(other.distanceFare, distanceFare) ||
                other.distanceFare == distanceFare) &&
            (identical(other.additionalFees, additionalFees) ||
                other.additionalFees == additionalFees) &&
            (identical(other.tax, tax) || other.tax == tax) &&
            (identical(other.totalFare, totalFare) ||
                other.totalFare == totalFare) &&
            (identical(other.estimatedDistanceKm, estimatedDistanceKm) ||
                other.estimatedDistanceKm == estimatedDistanceKm) &&
            (identical(
                    other.estimatedDurationMinutes, estimatedDurationMinutes) ||
                other.estimatedDurationMinutes == estimatedDurationMinutes));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      baseFare,
      distanceFare,
      additionalFees,
      tax,
      totalFare,
      estimatedDistanceKm,
      estimatedDurationMinutes);

  /// Create a copy of PricingInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PricingInfoImplCopyWith<_$PricingInfoImpl> get copyWith =>
      __$$PricingInfoImplCopyWithImpl<_$PricingInfoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PricingInfoImplToJson(
      this,
    );
  }
}

abstract class _PricingInfo extends PricingInfo {
  const factory _PricingInfo(
      {required final double baseFare,
      required final double distanceFare,
      final double additionalFees,
      required final double tax,
      required final double totalFare,
      required final double estimatedDistanceKm,
      required final int estimatedDurationMinutes}) = _$PricingInfoImpl;
  const _PricingInfo._() : super._();

  factory _PricingInfo.fromJson(Map<String, dynamic> json) =
      _$PricingInfoImpl.fromJson;

  /// Base fare amount
  @override
  double get baseFare;

  /// Distance-based fare component
  @override
  double get distanceFare;

  /// Any additional fees
  @override
  double get additionalFees;

  /// Tax amount
  @override
  double get tax;

  /// Total fare amount
  @override
  double get totalFare;

  /// Estimated distance in kilometers
  @override
  double get estimatedDistanceKm;

  /// Estimated duration in minutes
  @override
  int get estimatedDurationMinutes;

  /// Create a copy of PricingInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PricingInfoImplCopyWith<_$PricingInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
