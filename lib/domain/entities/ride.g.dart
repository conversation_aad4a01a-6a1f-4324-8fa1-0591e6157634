// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ride.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RideLocationImpl _$$RideLocationImplFromJson(Map<String, dynamic> json) =>
    _$RideLocationImpl(
      name: json['name'] as String,
      address: json['address'] as String?,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      instructions: json['instructions'] as String?,
    );

Map<String, dynamic> _$$RideLocationImplToJson(_$RideLocationImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      if (instance.address case final value?) 'address': value,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      if (instance.instructions case final value?) 'instructions': value,
    };

_$RideImpl _$$RideImplFromJson(Map<String, dynamic> json) => _$RideImpl(
      id: json['id'] as String,
      riderId: json['riderId'] as String,
      driverId: json['driverId'] as String?,
      status: $enumDecode(_$RideStatusEnumMap, json['status']),
      pickupLocation:
          RideLocation.fromJson(json['pickupLocation'] as Map<String, dynamic>),
      dropoffLocation: RideLocation.fromJson(
          json['dropoffLocation'] as Map<String, dynamic>),
      fixedFare: (json['fixedFare'] as num).toDouble(),
      paymentStatus: $enumDecode(_$PaymentStatusEnumMap, json['paymentStatus']),
      acceptedAt: json['acceptedAt'] == null
          ? null
          : DateTime.parse(json['acceptedAt'] as String),
      startedAt: json['startedAt'] == null
          ? null
          : DateTime.parse(json['startedAt'] as String),
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
      cancelledAt: json['cancelledAt'] == null
          ? null
          : DateTime.parse(json['cancelledAt'] as String),
      specialInstructions: json['specialInstructions'] as String?,
      cancellationReason: $enumDecodeNullable(
          _$CancellationReasonEnumMap, json['cancellationReason']),
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$$RideImplToJson(_$RideImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'riderId': instance.riderId,
      if (instance.driverId case final value?) 'driverId': value,
      'status': _$RideStatusEnumMap[instance.status]!,
      'pickupLocation': instance.pickupLocation.toJson(),
      'dropoffLocation': instance.dropoffLocation.toJson(),
      'fixedFare': instance.fixedFare,
      'paymentStatus': _$PaymentStatusEnumMap[instance.paymentStatus]!,
      if (instance.acceptedAt?.toIso8601String() case final value?)
        'acceptedAt': value,
      if (instance.startedAt?.toIso8601String() case final value?)
        'startedAt': value,
      if (instance.completedAt?.toIso8601String() case final value?)
        'completedAt': value,
      if (instance.cancelledAt?.toIso8601String() case final value?)
        'cancelledAt': value,
      if (instance.specialInstructions case final value?)
        'specialInstructions': value,
      if (_$CancellationReasonEnumMap[instance.cancellationReason]
          case final value?)
        'cancellationReason': value,
      'createdAt': instance.createdAt.toIso8601String(),
    };

const _$RideStatusEnumMap = {
  RideStatus.requested: 'requested',
  RideStatus.accepted: 'accepted',
  RideStatus.inProgress: 'inProgress',
  RideStatus.completed: 'completed',
  RideStatus.cancelled: 'cancelled',
};

const _$PaymentStatusEnumMap = {
  PaymentStatus.pending: 'pending',
  PaymentStatus.completed: 'completed',
  PaymentStatus.failed: 'failed',
  PaymentStatus.refunded: 'refunded',
};

const _$CancellationReasonEnumMap = {
  CancellationReason.riderChangedMind: 'riderChangedMind',
  CancellationReason.riderFoundAlternative: 'riderFoundAlternative',
  CancellationReason.driverTakingTooLong: 'driverTakingTooLong',
  CancellationReason.driverCancelled: 'driverCancelled',
  CancellationReason.emergency: 'emergency',
  CancellationReason.other: 'other',
};

_$RideHistoryImpl _$$RideHistoryImplFromJson(Map<String, dynamic> json) =>
    _$RideHistoryImpl(
      id: json['id'] as String,
      pickupName: json['pickupName'] as String,
      dropoffName: json['dropoffName'] as String,
      fare: (json['fare'] as num).toDouble(),
      status: $enumDecode(_$RideStatusEnumMap, json['status']),
      createdAt: DateTime.parse(json['createdAt'] as String),
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
      driverName: json['driverName'] as String?,
      driverRating: (json['driverRating'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$RideHistoryImplToJson(_$RideHistoryImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'pickupName': instance.pickupName,
      'dropoffName': instance.dropoffName,
      'fare': instance.fare,
      'status': _$RideStatusEnumMap[instance.status]!,
      'createdAt': instance.createdAt.toIso8601String(),
      if (instance.completedAt?.toIso8601String() case final value?)
        'completedAt': value,
      if (instance.driverName case final value?) 'driverName': value,
      if (instance.driverRating case final value?) 'driverRating': value,
    };

_$RideReceiptImpl _$$RideReceiptImplFromJson(Map<String, dynamic> json) =>
    _$RideReceiptImpl(
      rideId: json['rideId'] as String,
      receiptNumber: json['receiptNumber'] as String,
      baseFare: (json['baseFare'] as num).toDouble(),
      additionalFees: (json['additionalFees'] as num?)?.toDouble() ?? 0.0,
      tax: (json['tax'] as num).toDouble(),
      totalAmount: (json['totalAmount'] as num).toDouble(),
      paymentMethod: json['paymentMethod'] as String,
      paymentDate: DateTime.parse(json['paymentDate'] as String),
      pickupLocation: json['pickupLocation'] as String,
      dropoffLocation: json['dropoffLocation'] as String,
      distanceKm: (json['distanceKm'] as num).toDouble(),
      durationMinutes: (json['durationMinutes'] as num).toInt(),
      driverName: json['driverName'] as String,
      vehicleInfo: json['vehicleInfo'] as String,
    );

Map<String, dynamic> _$$RideReceiptImplToJson(_$RideReceiptImpl instance) =>
    <String, dynamic>{
      'rideId': instance.rideId,
      'receiptNumber': instance.receiptNumber,
      'baseFare': instance.baseFare,
      'additionalFees': instance.additionalFees,
      'tax': instance.tax,
      'totalAmount': instance.totalAmount,
      'paymentMethod': instance.paymentMethod,
      'paymentDate': instance.paymentDate.toIso8601String(),
      'pickupLocation': instance.pickupLocation,
      'dropoffLocation': instance.dropoffLocation,
      'distanceKm': instance.distanceKm,
      'durationMinutes': instance.durationMinutes,
      'driverName': instance.driverName,
      'vehicleInfo': instance.vehicleInfo,
    };

_$RideRequestCreateImpl _$$RideRequestCreateImplFromJson(
        Map<String, dynamic> json) =>
    _$RideRequestCreateImpl(
      pickupLocation:
          RideLocation.fromJson(json['pickupLocation'] as Map<String, dynamic>),
      dropoffLocation: RideLocation.fromJson(
          json['dropoffLocation'] as Map<String, dynamic>),
      specialInstructions: json['specialInstructions'] as String?,
    );

Map<String, dynamic> _$$RideRequestCreateImplToJson(
        _$RideRequestCreateImpl instance) =>
    <String, dynamic>{
      'pickupLocation': instance.pickupLocation.toJson(),
      'dropoffLocation': instance.dropoffLocation.toJson(),
      if (instance.specialInstructions case final value?)
        'specialInstructions': value,
    };

_$PricingInfoImpl _$$PricingInfoImplFromJson(Map<String, dynamic> json) =>
    _$PricingInfoImpl(
      baseFare: (json['baseFare'] as num).toDouble(),
      distanceFare: (json['distanceFare'] as num).toDouble(),
      additionalFees: (json['additionalFees'] as num?)?.toDouble() ?? 0.0,
      tax: (json['tax'] as num).toDouble(),
      totalFare: (json['totalFare'] as num).toDouble(),
      estimatedDistanceKm: (json['estimatedDistanceKm'] as num).toDouble(),
      estimatedDurationMinutes:
          (json['estimatedDurationMinutes'] as num).toInt(),
    );

Map<String, dynamic> _$$PricingInfoImplToJson(_$PricingInfoImpl instance) =>
    <String, dynamic>{
      'baseFare': instance.baseFare,
      'distanceFare': instance.distanceFare,
      'additionalFees': instance.additionalFees,
      'tax': instance.tax,
      'totalFare': instance.totalFare,
      'estimatedDistanceKm': instance.estimatedDistanceKm,
      'estimatedDurationMinutes': instance.estimatedDurationMinutes,
    };
