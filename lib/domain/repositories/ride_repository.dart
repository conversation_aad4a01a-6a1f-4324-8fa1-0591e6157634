import 'package:dartz/dartz.dart';
import '../entities/ride.dart';
import '../../core/errors/app_error.dart';

/// Repository interface for ride-related operations
abstract class RideRepository {
  /// Request a new ride
  Future<Either<AppError, Ride>> requestRide(RideRequestCreate request);

  /// Get the status of a specific ride
  Future<Either<AppError, Ride>> getRideStatus(String rideId);

  /// Cancel a ride
  Future<Either<AppError, void>> cancelRide(
    String rideId,
    CancellationReason reason,
  );

  /// Get ride history with pagination
  Future<Either<AppError, List<RideHistory>>> getRideHistory({
    int limit = 20,
    int offset = 0,
  });

  /// Get the active ride for the current user (if any)
  Future<Either<AppError, Ride?>> getActiveRide();

  /// Calculate pricing for a ride between two locations
  Future<Either<AppError, PricingInfo>> calculatePrice(
    RideLocation pickup,
    RideLocation dropoff,
  );

  /// Get a ride receipt
  Future<Either<AppError, RideReceipt>> getRideReceipt(String rideId);
}
