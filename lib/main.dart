import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'core/di/service_locator.dart';
import 'core/theme/app_theme.dart';
import 'core/constants/app_constants.dart';
import 'core/navigation/app_router.dart';
import 'core/utils/performance_utils.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Set preferred orientations for better UX
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Set system UI overlay style for better visual integration
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      statusBarBrightness: Brightness.light,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  // Initialize dependency injection
  await setupServiceLocator();

  runApp(const ProviderScope(child: Lucian<PERSON><PERSON>A<PERSON>()));
}

class LucianRidesApp extends ConsumerWidget {
  const LucianRidesApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Create the app router instance
    final appRouter = AppRouter();

    return MaterialApp.router(
      title: AppConstants.appName,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      debugShowCheckedModeBanner: false,

      // Accessibility features
      showSemanticsDebugger: false,

      // Use AutoRoute for navigation
      routerConfig: appRouter.config(),

      // Enhanced accessibility and performance
      builder: (context, child) {
        // Preload critical resources
        PerformanceUtils.preloadCriticalResources(context);

        // Return the child wrapped with performance monitoring and accessibility enhancements
        return PerformanceMonitor(
          child: MediaQuery(
            // Ensure text scaling doesn't break the UI
            data: MediaQuery.of(context).copyWith(
              textScaler: MediaQuery.of(
                context,
              ).textScaler.clamp(minScaleFactor: 0.8, maxScaleFactor: 1.4),
            ),
            child: child ?? const SizedBox.shrink(),
          ),
        );
      },
    );
  }
}
