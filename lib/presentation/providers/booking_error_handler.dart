import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../core/errors/app_error.dart';
import 'booking_flow_notifier.dart';
import 'booking_flow_state.dart';
import 'real_time_fare_provider.dart';

/// Error severity levels for UI display
enum ErrorSeverity {
  /// Info level - non-critical information
  info,

  /// Warning level - potential issues but not blocking
  warning,

  /// Error level - critical issues that block progress
  error,

  /// Fatal level - unrecoverable errors
  fatal,
}

/// Error message with metadata for UI display
class UserFacingError {
  /// The error message to display
  final String message;

  /// The severity level of the error
  final ErrorSeverity severity;

  /// Whether the error is dismissible
  final bool isDismissible;

  /// Whether the error has an action that can be taken
  final bool hasAction;

  /// The action text (e.g., "Retry", "Fix")
  final String? actionText;

  /// Creates a new user-facing error
  const UserFacingError({
    required this.message,
    this.severity = ErrorSeverity.error,
    this.isDismissible = true,
    this.hasAction = false,
    this.actionText,
  });

  /// Creates an info message
  factory UserFacingError.info(
    String message, {
    bool isDismissible = true,
    bool hasAction = false,
    String? actionText,
  }) {
    return UserFacingError(
      message: message,
      severity: ErrorSeverity.info,
      isDismissible: isDismissible,
      hasAction: hasAction,
      actionText: actionText,
    );
  }

  /// Creates a warning message
  factory UserFacingError.warning(
    String message, {
    bool isDismissible = true,
    bool hasAction = false,
    String? actionText,
  }) {
    return UserFacingError(
      message: message,
      severity: ErrorSeverity.warning,
      isDismissible: isDismissible,
      hasAction: hasAction,
      actionText: actionText,
    );
  }

  /// Creates an error message
  factory UserFacingError.error(
    String message, {
    bool isDismissible = true,
    bool hasAction = false,
    String? actionText,
  }) {
    return UserFacingError(
      message: message,
      severity: ErrorSeverity.error,
      isDismissible: isDismissible,
      hasAction: hasAction,
      actionText: actionText,
    );
  }

  /// Creates a fatal error message
  factory UserFacingError.fatal(
    String message, {
    bool isDismissible = false,
    bool hasAction = true,
    String? actionText = "Contact Support",
  }) {
    return UserFacingError(
      message: message,
      severity: ErrorSeverity.fatal,
      isDismissible: isDismissible,
      hasAction: hasAction,
      actionText: actionText,
    );
  }
}

/// Notifier for handling and translating errors into user-friendly messages
class BookingErrorHandler extends StateNotifier<UserFacingError?> {
  final Ref _ref;

  BookingErrorHandler(this._ref) : super(null) {
    _listenToErrors();
  }

  void _listenToErrors() {
    // Listen to booking flow errors
    final bookingState = _ref.read(bookingFlowProvider);
    if (bookingState.error != null) {
      _handleAppError(bookingState.error!);
    }

    // Listen to fare calculation errors
    final fareState = _ref.read(realTimeFareProvider);
    fareState.whenOrNull(
      error: (error, _) {
        if (error is AppError) {
          _handleAppError(error);
        } else {
          _setGenericError('Unable to calculate fare. Please try again.');
        }
      },
    );
  }

  /// Handle AppError and convert to user-friendly message
  void _handleAppError(AppError error) {
    error.when(
      network: (message, details) {
        state = UserFacingError.error(
          'Network connection issue. Please check your internet connection.',
          hasAction: true,
          actionText: 'Retry',
        );
      },

      server: (message, statusCode) {
        state = UserFacingError.error(
          'Server error. Please try again later.',
          hasAction: true,
          actionText: 'Retry',
        );
      },

      authentication: (message, errorCode) {
        state = UserFacingError.error(
          'Session expired. Please log in again.',
          hasAction: true,
          actionText: 'Log In',
        );
      },

      validation: (message, fieldErrors) {
        state = UserFacingError.warning(
          'Please check your input and try again.',
          isDismissible: true,
        );
      },

      unknown: (message, exception) {
        state = UserFacingError.error(message, isDismissible: true);
      },
    );
  }

  /// Set a generic error message
  void _setGenericError(String message) {
    state = UserFacingError.error(message);
  }

  /// Set a field-specific error message
  void setFieldError(String field, String message) {
    // This doesn't change the state directly but is used by UI components
    // to display field-specific errors
  }

  /// Clear the current error
  void clearError() {
    state = null;
  }

  /// Handle error action (e.g., retry, navigate to settings)
  void handleErrorAction() {
    if (state == null || !state!.hasAction) return;

    final currentState = state;

    // Clear the error first
    clearError();

    // Handle different actions based on the error
    if (currentState!.actionText == 'Retry') {
      // Retry the last operation
      final bookingNotifier = _ref.read(bookingFlowProvider.notifier);

      switch (bookingNotifier.state.currentStep) {
        case BookingStep.pickupSelection:
          // Retry getting current location
          bookingNotifier.initialize();
          break;

        case BookingStep.dropoffSelection:
          // Nothing to retry
          break;

        case BookingStep.pricingReview:
          // Retry pricing calculation
          bookingNotifier.calculatePricing();
          break;

        case BookingStep.confirmation:
          // Nothing to retry
          break;

        case BookingStep.processing:
          // Retry ride request
          bookingNotifier.submitRideRequest();
          break;

        case BookingStep.rideConfirmed:
          // Refresh ride status
          bookingNotifier.refreshRideStatus();
          break;

        case BookingStep.rideCompleted:
        case BookingStep.rideCancelled:
          // Nothing to retry
          break;
      }
    } else if (currentState.actionText == 'Settings') {
      // Navigate to settings (would be handled by UI)
    } else if (currentState.actionText == 'Log In') {
      // Navigate to login (would be handled by UI)
    }
  }
}

/// Provider for booking error handler
final bookingErrorHandlerProvider =
    StateNotifierProvider<BookingErrorHandler, UserFacingError?>((ref) {
      return BookingErrorHandler(ref);
    });

/// Provider for current error message
final currentErrorMessageProvider = Provider<String?>((ref) {
  final error = ref.watch(bookingErrorHandlerProvider);
  return error?.message;
});

/// Provider for error severity
final errorSeverityProvider = Provider<ErrorSeverity?>((ref) {
  final error = ref.watch(bookingErrorHandlerProvider);
  return error?.severity;
});

/// Provider for whether there is an error action available
final hasErrorActionProvider = Provider<bool>((ref) {
  final error = ref.watch(bookingErrorHandlerProvider);
  return error?.hasAction ?? false;
});

/// Provider for error action text
final errorActionTextProvider = Provider<String?>((ref) {
  final error = ref.watch(bookingErrorHandlerProvider);
  return error?.actionText;
});
