import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../core/di/service_locator.dart';
import '../../domain/repositories/ride_repository.dart';
import '../../domain/entities/ride.dart';
import '../../domain/entities/location_models.dart';
import '../../services/location/location_service.dart';
import 'booking_flow_state.dart';

/// StateNotifier for managing the entire ride booking flow
class BookingFlowNotifier extends StateNotifier<BookingFlowState> {
  final RideRepository _rideRepository;
  final LocationService _locationService;

  BookingFlowNotifier(this._rideRepository, this._locationService)
    : super(const BookingFlowState());

  /// Initialize the booking flow
  Future<void> initialize() async {
    if (state.isInitialized) return;

    // Check for any active ride
    await _checkForActiveRide();

    // Try to get current location for pickup
    if (state.currentStep == BookingStep.pickupSelection) {
      await _initializeCurrentLocation();
    }

    state = state.copyWith(isInitialized: true);
  }

  /// Check for any active ride and update state accordingly
  Future<void> _checkForActiveRide() async {
    state = state.copyWith(isLoadingActiveRide: true);

    final result = await _rideRepository.getActiveRide();

    result.fold(
      (error) =>
          state = state.copyWith(isLoadingActiveRide: false, error: error),
      (ride) {
        if (ride != null) {
          // There is an active ride, update state accordingly
          state = state.copyWith(
            isLoadingActiveRide: false,
            currentRide: ride,
            currentStep: ride.status == RideStatus.completed
                ? BookingStep.rideCompleted
                : ride.status == RideStatus.cancelled
                ? BookingStep.rideCancelled
                : BookingStep.rideConfirmed,
            error: null,
          );
        } else {
          // No active ride, continue with normal flow
          state = state.copyWith(isLoadingActiveRide: false, error: null);
        }
      },
    );
  }

  /// Initialize with current location for pickup
  Future<void> _initializeCurrentLocation() async {
    final permissionResult = await _locationService.checkLocationPermission();

    final hasPermission = permissionResult.fold(
      (error) => false,
      (hasPermission) => hasPermission,
    );

    if (!hasPermission) {
      // Don't try to get location if no permission
      return;
    }

    state = state.copyWith(isLoadingPickupLocation: true);

    final locationResult = await _locationService.getCurrentLocation();

    locationResult.fold(
      (error) =>
          state = state.copyWith(isLoadingPickupLocation: false, error: error),
      (location) async {
        // Convert coordinates to RideLocation
        final rideLocationResult = await _locationService
            .coordinatesToRideLocation(location, name: 'Current Location');

        rideLocationResult.fold(
          (error) => state = state.copyWith(
            isLoadingPickupLocation: false,
            error: error,
          ),
          (rideLocation) => state = state.copyWith(
            isLoadingPickupLocation: false,
            pickupLocation: rideLocation,
            error: null,
          ),
        );
      },
    );
  }

  /// Set pickup location and move to next step
  Future<void> setPickupLocation(RideLocation location) async {
    state = state.copyWith(
      pickupLocation: location,
      fieldErrors: {...state.fieldErrors}..remove('pickup'),
      error: null,
      currentStep: BookingStep.dropoffSelection,
    );

    // Store as recent location
    _storeRecentLocation(location, RecentLocationType.pickup);
  }

  /// Set dropoff location and move to next step
  Future<void> setDropoffLocation(RideLocation location) async {
    state = state.copyWith(
      dropoffLocation: location,
      fieldErrors: {...state.fieldErrors}..remove('dropoff'),
      error: null,
    );

    // Store as recent location
    _storeRecentLocation(location, RecentLocationType.dropoff);

    // Calculate pricing automatically
    await calculatePricing();

    // Move to pricing review step
    state = state.copyWith(currentStep: BookingStep.pricingReview);
  }

  /// Store a location as recent
  Future<void> _storeRecentLocation(
    RideLocation location,
    RecentLocationType type,
  ) async {
    final recentLocation = RecentLocation(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: location.name,
      address: location.address ?? location.name,
      coordinates: LocationCoordinates(
        latitude: location.latitude,
        longitude: location.longitude,
      ),
      lastUsed: DateTime.now(),
      type: type,
    );

    await _locationService.storeRecentLocation(recentLocation);
  }

  /// Calculate pricing for the current pickup and dropoff locations
  Future<void> calculatePricing() async {
    if (state.pickupLocation == null || state.dropoffLocation == null) {
      return;
    }

    // Check if we need to recalculate
    if (!state.needsPricingRecalculation && state.pricingInfo != null) {
      return;
    }

    state = state.copyWith(isLoadingPricing: true, error: null);

    final result = await _rideRepository.calculatePrice(
      state.pickupLocation!,
      state.dropoffLocation!,
    );

    result.fold(
      (error) => state = state.copyWith(
        isLoadingPricing: false,
        error: error,
        pricingInfo: null,
      ),
      (pricing) => state = state.copyWith(
        isLoadingPricing: false,
        pricingInfo: pricing,
        lastPricingCalculation: DateTime.now(),
        error: null,
      ),
    );
  }

  /// Set special instructions
  void setSpecialInstructions(String instructions) {
    state = state.copyWith(
      specialInstructions: instructions,
      fieldErrors: {...state.fieldErrors}..remove('specialInstructions'),
    );
  }

  /// Confirm pricing and move to confirmation step
  void confirmPricing() {
    if (state.pricingInfo == null) {
      state = state.copyWith(
        fieldErrors: {
          ...state.fieldErrors,
          'pricing': 'Unable to calculate fare. Please try again.',
        },
      );
      return;
    }

    state = state.copyWith(
      hasPricingConfirmation: true,
      currentStep: BookingStep.confirmation,
    );
  }

  /// Submit ride request
  Future<void> submitRideRequest() async {
    if (!_validateRideRequest()) {
      return;
    }

    state = state.copyWith(
      isSubmittingRequest: true,
      currentStep: BookingStep.processing,
      error: null,
      hasAttemptedSubmit: true,
    );

    final request = RideRequestCreate(
      pickupLocation: state.pickupLocation!,
      dropoffLocation: state.dropoffLocation!,
      specialInstructions: state.specialInstructions.isEmpty
          ? null
          : state.specialInstructions,
    );

    final result = await _rideRepository.requestRide(request);

    result.fold(
      (error) => state = state.copyWith(
        isSubmittingRequest: false,
        error: error,
        currentStep:
            BookingStep.confirmation, // Go back to confirmation on error
      ),
      (ride) => state = state.copyWith(
        isSubmittingRequest: false,
        currentRide: ride,
        currentStep: BookingStep.rideConfirmed,
        error: null,
      ),
    );
  }

  /// Cancel current ride
  Future<void> cancelRide(CancellationReason reason) async {
    if (state.currentRide == null) return;

    final result = await _rideRepository.cancelRide(
      state.currentRide!.id,
      reason,
    );

    result.fold((error) => state = state.copyWith(error: error), (_) {
      // Update ride status to cancelled
      final updatedRide = state.currentRide!.copyWith(
        status: RideStatus.cancelled,
        cancelledAt: DateTime.now(),
        cancellationReason: reason,
      );

      state = state.copyWith(
        currentRide: updatedRide,
        currentStep: BookingStep.rideCancelled,
        error: null,
      );
    });
  }

  /// Go to previous step in the flow
  void goBack() {
    switch (state.currentStep) {
      case BookingStep.pickupSelection:
        // Already at first step, do nothing
        break;
      case BookingStep.dropoffSelection:
        state = state.copyWith(currentStep: BookingStep.pickupSelection);
        break;
      case BookingStep.pricingReview:
        state = state.copyWith(currentStep: BookingStep.dropoffSelection);
        break;
      case BookingStep.confirmation:
        state = state.copyWith(currentStep: BookingStep.pricingReview);
        break;
      case BookingStep.processing:
        // Cannot go back while processing
        break;
      case BookingStep.rideConfirmed:
      case BookingStep.rideCompleted:
      case BookingStep.rideCancelled:
        // Reset the flow to start a new ride
        resetFlow();
        break;
    }
  }

  /// Go to next step in the flow if possible
  void goNext() {
    if (!state.canProceedToNextStep) return;

    switch (state.currentStep) {
      case BookingStep.pickupSelection:
        state = state.copyWith(currentStep: BookingStep.dropoffSelection);
        break;
      case BookingStep.dropoffSelection:
        state = state.copyWith(currentStep: BookingStep.pricingReview);
        break;
      case BookingStep.pricingReview:
        state = state.copyWith(currentStep: BookingStep.confirmation);
        break;
      case BookingStep.confirmation:
        submitRideRequest();
        break;
      case BookingStep.processing:
        // Cannot proceed while processing
        break;
      case BookingStep.rideConfirmed:
      case BookingStep.rideCompleted:
      case BookingStep.rideCancelled:
        // Reset the flow to start a new ride
        resetFlow();
        break;
    }
  }

  /// Reset the booking flow to start a new ride
  void resetFlow() {
    state = const BookingFlowState(isInitialized: true);
    initialize();
  }

  /// Clear errors
  void clearErrors() {
    state = state.copyWith(error: null, fieldErrors: {});
  }

  /// Validate ride request
  bool _validateRideRequest() {
    final errors = <String, String>{};

    if (state.pickupLocation == null) {
      errors['pickup'] = 'Please select a pickup location';
    } else if (!state.pickupLocation!.isValidStLuciaLocation()) {
      errors['pickup'] = 'Pickup location must be within St. Lucia';
    }

    if (state.dropoffLocation == null) {
      errors['dropoff'] = 'Please select a dropoff location';
    } else if (!state.dropoffLocation!.isValidStLuciaLocation()) {
      errors['dropoff'] = 'Dropoff location must be within St. Lucia';
    }

    if (state.pricingInfo == null) {
      errors['pricing'] = 'Unable to calculate fare. Please try again.';
    }

    if (state.specialInstructions.length > 500) {
      errors['specialInstructions'] =
          'Special instructions must be less than 500 characters';
    }

    // Check if pickup and dropoff are too close
    if (state.pickupLocation != null && state.dropoffLocation != null) {
      const double minDistanceKm = 0.5;
      final pickupCoords = _locationService.rideLocationToCoordinates(
        state.pickupLocation!,
      );
      final dropoffCoords = _locationService.rideLocationToCoordinates(
        state.dropoffLocation!,
      );

      final distance = pickupCoords.distanceTo(dropoffCoords);
      if (distance < minDistanceKm) {
        errors['distance'] =
            'Pickup and dropoff locations are too close. Minimum distance is ${minDistanceKm}km.';
      }
    }

    state = state.copyWith(fieldErrors: errors, hasAttemptedSubmit: true);

    return errors.isEmpty;
  }

  /// Refresh ride status
  Future<void> refreshRideStatus() async {
    if (state.currentRide == null) return;

    final result = await _rideRepository.getRideStatus(state.currentRide!.id);

    result.fold((error) => state = state.copyWith(error: error), (ride) {
      state = state.copyWith(
        currentRide: ride,
        currentStep: ride.status == RideStatus.completed
            ? BookingStep.rideCompleted
            : ride.status == RideStatus.cancelled
            ? BookingStep.rideCancelled
            : BookingStep.rideConfirmed,
        error: null,
      );
    });
  }
}

/// Provider for BookingFlowNotifier
final bookingFlowProvider =
    StateNotifierProvider<BookingFlowNotifier, BookingFlowState>((ref) {
      return BookingFlowNotifier(
        getIt<RideRepository>(),
        getIt<LocationService>(),
      );
    });

/// Provider for current booking step
final currentBookingStepProvider = Provider<BookingStep>((ref) {
  final bookingState = ref.watch(bookingFlowProvider);
  return bookingState.currentStep;
});

/// Provider for checking if booking can proceed to next step
final canProceedToNextStepProvider = Provider<bool>((ref) {
  final bookingState = ref.watch(bookingFlowProvider);
  return bookingState.canProceedToNextStep;
});

/// Provider for user-friendly error message
final bookingErrorMessageProvider = Provider<String?>((ref) {
  final bookingState = ref.watch(bookingFlowProvider);
  return bookingState.userFriendlyErrorMessage;
});

/// Provider for checking if any booking operation is loading
final isBookingLoadingProvider = Provider<bool>((ref) {
  final bookingState = ref.watch(bookingFlowProvider);
  return bookingState.isLoading;
});

/// Provider for current ride
final currentRideProvider = Provider<Ride?>((ref) {
  final bookingState = ref.watch(bookingFlowProvider);
  return bookingState.currentRide;
});

/// Provider for current pricing info
final currentPricingInfoProvider = Provider<PricingInfo?>((ref) {
  final bookingState = ref.watch(bookingFlowProvider);
  return bookingState.pricingInfo;
});

/// Provider for field validation errors
final fieldErrorsProvider = Provider.family<String?, String>((ref, field) {
  final bookingState = ref.watch(bookingFlowProvider);
  return bookingState.getFieldError(field);
});

/// Provider for checking if form has been attempted to submit
final hasAttemptedSubmitProvider = Provider<bool>((ref) {
  final bookingState = ref.watch(bookingFlowProvider);
  return bookingState.hasAttemptedSubmit;
});

/// Provider for checking if locations are valid
final hasValidLocationsProvider = Provider<bool>((ref) {
  final bookingState = ref.watch(bookingFlowProvider);
  return bookingState.hasValidLocations;
});
