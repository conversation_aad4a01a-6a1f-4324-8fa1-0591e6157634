import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/ride.dart';
import '../../core/errors/app_error.dart';

part 'booking_flow_state.freezed.dart';

/// Enum representing the steps in the ride booking flow
enum BookingStep {
  /// Initial step for selecting pickup location
  pickupSelection,

  /// Step for selecting dropoff location
  dropoffSelection,

  /// Step for reviewing pricing and ride details
  pricingReview,

  /// Step for confirming and submitting the ride request
  confirmation,

  /// Step showing the ride has been requested and is being processed
  processing,

  /// Step showing the ride has been confirmed and is active
  rideConfirmed,

  /// Step showing the ride has been completed
  rideCompleted,

  /// Step showing the ride has been cancelled
  rideCancelled,
}

/// State for managing the ride booking flow
@freezed
class BookingFlowState with _$BookingFlowState {
  const BookingFlowState._();

  /// Creates a new booking flow state
  const factory BookingFlowState({
    /// Current step in the booking flow
    @Default(BookingStep.pickupSelection) BookingStep currentStep,

    /// Selected pickup location
    RideLocation? pickupLocation,

    /// Selected dropoff location
    RideLocation? dropoffLocation,

    /// Calculated pricing information
    PricingInfo? pricingInfo,

    /// Special instructions for the driver
    @Default('') String specialInstructions,

    /// Current ride request (if any)
    Ride? currentRide,

    /// Loading states for various operations
    @Default(false) bool isLoadingPickupLocation,
    @Default(false) bool isLoadingDropoffLocation,
    @Default(false) bool isLoadingPricing,
    @Default(false) bool isSubmittingRequest,
    @Default(false) bool isLoadingActiveRide,

    /// Error states
    AppError? error,
    @Default({}) Map<String, String> fieldErrors,

    /// Form validation state
    @Default(false) bool hasAttemptedSubmit,

    /// Timestamp of last pricing calculation
    DateTime? lastPricingCalculation,

    /// Flag indicating if user has confirmed the pricing
    @Default(false) bool hasPricingConfirmation,

    /// Flag indicating if the flow has been initialized
    @Default(false) bool isInitialized,
  }) = _BookingFlowState;

  /// Check if both pickup and dropoff locations are selected
  bool get hasValidLocations =>
      pickupLocation != null && dropoffLocation != null;

  /// Check if pricing is available
  bool get hasPricing => pricingInfo != null;

  /// Check if any loading operation is in progress
  bool get isLoading =>
      isLoadingPickupLocation ||
      isLoadingDropoffLocation ||
      isLoadingPricing ||
      isSubmittingRequest ||
      isLoadingActiveRide;

  /// Check if form has any errors
  bool get hasErrors => fieldErrors.isNotEmpty || error != null;

  /// Check if the current step is valid and can proceed to the next step
  bool get canProceedToNextStep {
    switch (currentStep) {
      case BookingStep.pickupSelection:
        return pickupLocation != null && !isLoadingPickupLocation;
      case BookingStep.dropoffSelection:
        return dropoffLocation != null && !isLoadingDropoffLocation;
      case BookingStep.pricingReview:
        return pricingInfo != null && !isLoadingPricing;
      case BookingStep.confirmation:
        return hasValidLocations && hasPricing && !isLoading;
      case BookingStep.processing:
        return false; // Cannot proceed while processing
      case BookingStep.rideConfirmed:
        return currentRide != null && currentRide!.isActive();
      case BookingStep.rideCompleted:
        return true; // Can always proceed from completed
      case BookingStep.rideCancelled:
        return true; // Can always proceed from cancelled
    }
  }

  /// Check if pricing needs recalculation
  bool get needsPricingRecalculation {
    if (pickupLocation == null || dropoffLocation == null) return false;
    if (pricingInfo == null) return true;
    if (lastPricingCalculation == null) return true;

    // Recalculate if more than 5 minutes old
    final now = DateTime.now();
    final timeDiff = now.difference(lastPricingCalculation!);
    return timeDiff.inMinutes > 5;
  }

  /// Get user-friendly error message for the current step
  String? get userFriendlyErrorMessage {
    if (error == null) return null;

    switch (currentStep) {
      case BookingStep.pickupSelection:
        return 'Unable to set pickup location. Please try again.';
      case BookingStep.dropoffSelection:
        return 'Unable to set dropoff location. Please try again.';
      case BookingStep.pricingReview:
        return 'Unable to calculate fare. Please try again.';
      case BookingStep.confirmation:
        return 'Unable to confirm ride details. Please check your inputs.';
      case BookingStep.processing:
        return 'Unable to process your ride request. Please try again.';
      case BookingStep.rideConfirmed:
        return 'Unable to retrieve ride status. Please check the Rides tab.';
      case BookingStep.rideCompleted:
        return 'Unable to complete ride. Please contact support.';
      case BookingStep.rideCancelled:
        return 'Unable to cancel ride. Please try again.';
    }
  }

  /// Get error for specific field
  String? getFieldError(String field) => fieldErrors[field];

  /// Check if specific field has error
  bool hasFieldError(String field) => fieldErrors.containsKey(field);
}
