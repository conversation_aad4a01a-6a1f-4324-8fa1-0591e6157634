// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'booking_flow_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$BookingFlowState {
  /// Current step in the booking flow
  BookingStep get currentStep => throw _privateConstructorUsedError;

  /// Selected pickup location
  RideLocation? get pickupLocation => throw _privateConstructorUsedError;

  /// Selected dropoff location
  RideLocation? get dropoffLocation => throw _privateConstructorUsedError;

  /// Calculated pricing information
  PricingInfo? get pricingInfo => throw _privateConstructorUsedError;

  /// Special instructions for the driver
  String get specialInstructions => throw _privateConstructorUsedError;

  /// Current ride request (if any)
  Ride? get currentRide => throw _privateConstructorUsedError;

  /// Loading states for various operations
  bool get isLoadingPickupLocation => throw _privateConstructorUsedError;
  bool get isLoadingDropoffLocation => throw _privateConstructorUsedError;
  bool get isLoadingPricing => throw _privateConstructorUsedError;
  bool get isSubmittingRequest => throw _privateConstructorUsedError;
  bool get isLoadingActiveRide => throw _privateConstructorUsedError;

  /// Error states
  AppError? get error => throw _privateConstructorUsedError;
  Map<String, String> get fieldErrors => throw _privateConstructorUsedError;

  /// Form validation state
  bool get hasAttemptedSubmit => throw _privateConstructorUsedError;

  /// Timestamp of last pricing calculation
  DateTime? get lastPricingCalculation => throw _privateConstructorUsedError;

  /// Flag indicating if user has confirmed the pricing
  bool get hasPricingConfirmation => throw _privateConstructorUsedError;

  /// Flag indicating if the flow has been initialized
  bool get isInitialized => throw _privateConstructorUsedError;

  /// Create a copy of BookingFlowState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BookingFlowStateCopyWith<BookingFlowState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BookingFlowStateCopyWith<$Res> {
  factory $BookingFlowStateCopyWith(
          BookingFlowState value, $Res Function(BookingFlowState) then) =
      _$BookingFlowStateCopyWithImpl<$Res, BookingFlowState>;
  @useResult
  $Res call(
      {BookingStep currentStep,
      RideLocation? pickupLocation,
      RideLocation? dropoffLocation,
      PricingInfo? pricingInfo,
      String specialInstructions,
      Ride? currentRide,
      bool isLoadingPickupLocation,
      bool isLoadingDropoffLocation,
      bool isLoadingPricing,
      bool isSubmittingRequest,
      bool isLoadingActiveRide,
      AppError? error,
      Map<String, String> fieldErrors,
      bool hasAttemptedSubmit,
      DateTime? lastPricingCalculation,
      bool hasPricingConfirmation,
      bool isInitialized});

  $RideLocationCopyWith<$Res>? get pickupLocation;
  $RideLocationCopyWith<$Res>? get dropoffLocation;
  $PricingInfoCopyWith<$Res>? get pricingInfo;
  $RideCopyWith<$Res>? get currentRide;
  $AppErrorCopyWith<$Res>? get error;
}

/// @nodoc
class _$BookingFlowStateCopyWithImpl<$Res, $Val extends BookingFlowState>
    implements $BookingFlowStateCopyWith<$Res> {
  _$BookingFlowStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BookingFlowState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentStep = null,
    Object? pickupLocation = freezed,
    Object? dropoffLocation = freezed,
    Object? pricingInfo = freezed,
    Object? specialInstructions = null,
    Object? currentRide = freezed,
    Object? isLoadingPickupLocation = null,
    Object? isLoadingDropoffLocation = null,
    Object? isLoadingPricing = null,
    Object? isSubmittingRequest = null,
    Object? isLoadingActiveRide = null,
    Object? error = freezed,
    Object? fieldErrors = null,
    Object? hasAttemptedSubmit = null,
    Object? lastPricingCalculation = freezed,
    Object? hasPricingConfirmation = null,
    Object? isInitialized = null,
  }) {
    return _then(_value.copyWith(
      currentStep: null == currentStep
          ? _value.currentStep
          : currentStep // ignore: cast_nullable_to_non_nullable
              as BookingStep,
      pickupLocation: freezed == pickupLocation
          ? _value.pickupLocation
          : pickupLocation // ignore: cast_nullable_to_non_nullable
              as RideLocation?,
      dropoffLocation: freezed == dropoffLocation
          ? _value.dropoffLocation
          : dropoffLocation // ignore: cast_nullable_to_non_nullable
              as RideLocation?,
      pricingInfo: freezed == pricingInfo
          ? _value.pricingInfo
          : pricingInfo // ignore: cast_nullable_to_non_nullable
              as PricingInfo?,
      specialInstructions: null == specialInstructions
          ? _value.specialInstructions
          : specialInstructions // ignore: cast_nullable_to_non_nullable
              as String,
      currentRide: freezed == currentRide
          ? _value.currentRide
          : currentRide // ignore: cast_nullable_to_non_nullable
              as Ride?,
      isLoadingPickupLocation: null == isLoadingPickupLocation
          ? _value.isLoadingPickupLocation
          : isLoadingPickupLocation // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingDropoffLocation: null == isLoadingDropoffLocation
          ? _value.isLoadingDropoffLocation
          : isLoadingDropoffLocation // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingPricing: null == isLoadingPricing
          ? _value.isLoadingPricing
          : isLoadingPricing // ignore: cast_nullable_to_non_nullable
              as bool,
      isSubmittingRequest: null == isSubmittingRequest
          ? _value.isSubmittingRequest
          : isSubmittingRequest // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingActiveRide: null == isLoadingActiveRide
          ? _value.isLoadingActiveRide
          : isLoadingActiveRide // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppError?,
      fieldErrors: null == fieldErrors
          ? _value.fieldErrors
          : fieldErrors // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      hasAttemptedSubmit: null == hasAttemptedSubmit
          ? _value.hasAttemptedSubmit
          : hasAttemptedSubmit // ignore: cast_nullable_to_non_nullable
              as bool,
      lastPricingCalculation: freezed == lastPricingCalculation
          ? _value.lastPricingCalculation
          : lastPricingCalculation // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      hasPricingConfirmation: null == hasPricingConfirmation
          ? _value.hasPricingConfirmation
          : hasPricingConfirmation // ignore: cast_nullable_to_non_nullable
              as bool,
      isInitialized: null == isInitialized
          ? _value.isInitialized
          : isInitialized // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }

  /// Create a copy of BookingFlowState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RideLocationCopyWith<$Res>? get pickupLocation {
    if (_value.pickupLocation == null) {
      return null;
    }

    return $RideLocationCopyWith<$Res>(_value.pickupLocation!, (value) {
      return _then(_value.copyWith(pickupLocation: value) as $Val);
    });
  }

  /// Create a copy of BookingFlowState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RideLocationCopyWith<$Res>? get dropoffLocation {
    if (_value.dropoffLocation == null) {
      return null;
    }

    return $RideLocationCopyWith<$Res>(_value.dropoffLocation!, (value) {
      return _then(_value.copyWith(dropoffLocation: value) as $Val);
    });
  }

  /// Create a copy of BookingFlowState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PricingInfoCopyWith<$Res>? get pricingInfo {
    if (_value.pricingInfo == null) {
      return null;
    }

    return $PricingInfoCopyWith<$Res>(_value.pricingInfo!, (value) {
      return _then(_value.copyWith(pricingInfo: value) as $Val);
    });
  }

  /// Create a copy of BookingFlowState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RideCopyWith<$Res>? get currentRide {
    if (_value.currentRide == null) {
      return null;
    }

    return $RideCopyWith<$Res>(_value.currentRide!, (value) {
      return _then(_value.copyWith(currentRide: value) as $Val);
    });
  }

  /// Create a copy of BookingFlowState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get error {
    if (_value.error == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.error!, (value) {
      return _then(_value.copyWith(error: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$BookingFlowStateImplCopyWith<$Res>
    implements $BookingFlowStateCopyWith<$Res> {
  factory _$$BookingFlowStateImplCopyWith(_$BookingFlowStateImpl value,
          $Res Function(_$BookingFlowStateImpl) then) =
      __$$BookingFlowStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {BookingStep currentStep,
      RideLocation? pickupLocation,
      RideLocation? dropoffLocation,
      PricingInfo? pricingInfo,
      String specialInstructions,
      Ride? currentRide,
      bool isLoadingPickupLocation,
      bool isLoadingDropoffLocation,
      bool isLoadingPricing,
      bool isSubmittingRequest,
      bool isLoadingActiveRide,
      AppError? error,
      Map<String, String> fieldErrors,
      bool hasAttemptedSubmit,
      DateTime? lastPricingCalculation,
      bool hasPricingConfirmation,
      bool isInitialized});

  @override
  $RideLocationCopyWith<$Res>? get pickupLocation;
  @override
  $RideLocationCopyWith<$Res>? get dropoffLocation;
  @override
  $PricingInfoCopyWith<$Res>? get pricingInfo;
  @override
  $RideCopyWith<$Res>? get currentRide;
  @override
  $AppErrorCopyWith<$Res>? get error;
}

/// @nodoc
class __$$BookingFlowStateImplCopyWithImpl<$Res>
    extends _$BookingFlowStateCopyWithImpl<$Res, _$BookingFlowStateImpl>
    implements _$$BookingFlowStateImplCopyWith<$Res> {
  __$$BookingFlowStateImplCopyWithImpl(_$BookingFlowStateImpl _value,
      $Res Function(_$BookingFlowStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of BookingFlowState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentStep = null,
    Object? pickupLocation = freezed,
    Object? dropoffLocation = freezed,
    Object? pricingInfo = freezed,
    Object? specialInstructions = null,
    Object? currentRide = freezed,
    Object? isLoadingPickupLocation = null,
    Object? isLoadingDropoffLocation = null,
    Object? isLoadingPricing = null,
    Object? isSubmittingRequest = null,
    Object? isLoadingActiveRide = null,
    Object? error = freezed,
    Object? fieldErrors = null,
    Object? hasAttemptedSubmit = null,
    Object? lastPricingCalculation = freezed,
    Object? hasPricingConfirmation = null,
    Object? isInitialized = null,
  }) {
    return _then(_$BookingFlowStateImpl(
      currentStep: null == currentStep
          ? _value.currentStep
          : currentStep // ignore: cast_nullable_to_non_nullable
              as BookingStep,
      pickupLocation: freezed == pickupLocation
          ? _value.pickupLocation
          : pickupLocation // ignore: cast_nullable_to_non_nullable
              as RideLocation?,
      dropoffLocation: freezed == dropoffLocation
          ? _value.dropoffLocation
          : dropoffLocation // ignore: cast_nullable_to_non_nullable
              as RideLocation?,
      pricingInfo: freezed == pricingInfo
          ? _value.pricingInfo
          : pricingInfo // ignore: cast_nullable_to_non_nullable
              as PricingInfo?,
      specialInstructions: null == specialInstructions
          ? _value.specialInstructions
          : specialInstructions // ignore: cast_nullable_to_non_nullable
              as String,
      currentRide: freezed == currentRide
          ? _value.currentRide
          : currentRide // ignore: cast_nullable_to_non_nullable
              as Ride?,
      isLoadingPickupLocation: null == isLoadingPickupLocation
          ? _value.isLoadingPickupLocation
          : isLoadingPickupLocation // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingDropoffLocation: null == isLoadingDropoffLocation
          ? _value.isLoadingDropoffLocation
          : isLoadingDropoffLocation // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingPricing: null == isLoadingPricing
          ? _value.isLoadingPricing
          : isLoadingPricing // ignore: cast_nullable_to_non_nullable
              as bool,
      isSubmittingRequest: null == isSubmittingRequest
          ? _value.isSubmittingRequest
          : isSubmittingRequest // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingActiveRide: null == isLoadingActiveRide
          ? _value.isLoadingActiveRide
          : isLoadingActiveRide // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppError?,
      fieldErrors: null == fieldErrors
          ? _value._fieldErrors
          : fieldErrors // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      hasAttemptedSubmit: null == hasAttemptedSubmit
          ? _value.hasAttemptedSubmit
          : hasAttemptedSubmit // ignore: cast_nullable_to_non_nullable
              as bool,
      lastPricingCalculation: freezed == lastPricingCalculation
          ? _value.lastPricingCalculation
          : lastPricingCalculation // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      hasPricingConfirmation: null == hasPricingConfirmation
          ? _value.hasPricingConfirmation
          : hasPricingConfirmation // ignore: cast_nullable_to_non_nullable
              as bool,
      isInitialized: null == isInitialized
          ? _value.isInitialized
          : isInitialized // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$BookingFlowStateImpl extends _BookingFlowState {
  const _$BookingFlowStateImpl(
      {this.currentStep = BookingStep.pickupSelection,
      this.pickupLocation,
      this.dropoffLocation,
      this.pricingInfo,
      this.specialInstructions = '',
      this.currentRide,
      this.isLoadingPickupLocation = false,
      this.isLoadingDropoffLocation = false,
      this.isLoadingPricing = false,
      this.isSubmittingRequest = false,
      this.isLoadingActiveRide = false,
      this.error,
      final Map<String, String> fieldErrors = const {},
      this.hasAttemptedSubmit = false,
      this.lastPricingCalculation,
      this.hasPricingConfirmation = false,
      this.isInitialized = false})
      : _fieldErrors = fieldErrors,
        super._();

  /// Current step in the booking flow
  @override
  @JsonKey()
  final BookingStep currentStep;

  /// Selected pickup location
  @override
  final RideLocation? pickupLocation;

  /// Selected dropoff location
  @override
  final RideLocation? dropoffLocation;

  /// Calculated pricing information
  @override
  final PricingInfo? pricingInfo;

  /// Special instructions for the driver
  @override
  @JsonKey()
  final String specialInstructions;

  /// Current ride request (if any)
  @override
  final Ride? currentRide;

  /// Loading states for various operations
  @override
  @JsonKey()
  final bool isLoadingPickupLocation;
  @override
  @JsonKey()
  final bool isLoadingDropoffLocation;
  @override
  @JsonKey()
  final bool isLoadingPricing;
  @override
  @JsonKey()
  final bool isSubmittingRequest;
  @override
  @JsonKey()
  final bool isLoadingActiveRide;

  /// Error states
  @override
  final AppError? error;
  final Map<String, String> _fieldErrors;
  @override
  @JsonKey()
  Map<String, String> get fieldErrors {
    if (_fieldErrors is EqualUnmodifiableMapView) return _fieldErrors;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_fieldErrors);
  }

  /// Form validation state
  @override
  @JsonKey()
  final bool hasAttemptedSubmit;

  /// Timestamp of last pricing calculation
  @override
  final DateTime? lastPricingCalculation;

  /// Flag indicating if user has confirmed the pricing
  @override
  @JsonKey()
  final bool hasPricingConfirmation;

  /// Flag indicating if the flow has been initialized
  @override
  @JsonKey()
  final bool isInitialized;

  @override
  String toString() {
    return 'BookingFlowState(currentStep: $currentStep, pickupLocation: $pickupLocation, dropoffLocation: $dropoffLocation, pricingInfo: $pricingInfo, specialInstructions: $specialInstructions, currentRide: $currentRide, isLoadingPickupLocation: $isLoadingPickupLocation, isLoadingDropoffLocation: $isLoadingDropoffLocation, isLoadingPricing: $isLoadingPricing, isSubmittingRequest: $isSubmittingRequest, isLoadingActiveRide: $isLoadingActiveRide, error: $error, fieldErrors: $fieldErrors, hasAttemptedSubmit: $hasAttemptedSubmit, lastPricingCalculation: $lastPricingCalculation, hasPricingConfirmation: $hasPricingConfirmation, isInitialized: $isInitialized)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BookingFlowStateImpl &&
            (identical(other.currentStep, currentStep) ||
                other.currentStep == currentStep) &&
            (identical(other.pickupLocation, pickupLocation) ||
                other.pickupLocation == pickupLocation) &&
            (identical(other.dropoffLocation, dropoffLocation) ||
                other.dropoffLocation == dropoffLocation) &&
            (identical(other.pricingInfo, pricingInfo) ||
                other.pricingInfo == pricingInfo) &&
            (identical(other.specialInstructions, specialInstructions) ||
                other.specialInstructions == specialInstructions) &&
            (identical(other.currentRide, currentRide) ||
                other.currentRide == currentRide) &&
            (identical(
                    other.isLoadingPickupLocation, isLoadingPickupLocation) ||
                other.isLoadingPickupLocation == isLoadingPickupLocation) &&
            (identical(
                    other.isLoadingDropoffLocation, isLoadingDropoffLocation) ||
                other.isLoadingDropoffLocation == isLoadingDropoffLocation) &&
            (identical(other.isLoadingPricing, isLoadingPricing) ||
                other.isLoadingPricing == isLoadingPricing) &&
            (identical(other.isSubmittingRequest, isSubmittingRequest) ||
                other.isSubmittingRequest == isSubmittingRequest) &&
            (identical(other.isLoadingActiveRide, isLoadingActiveRide) ||
                other.isLoadingActiveRide == isLoadingActiveRide) &&
            (identical(other.error, error) || other.error == error) &&
            const DeepCollectionEquality()
                .equals(other._fieldErrors, _fieldErrors) &&
            (identical(other.hasAttemptedSubmit, hasAttemptedSubmit) ||
                other.hasAttemptedSubmit == hasAttemptedSubmit) &&
            (identical(other.lastPricingCalculation, lastPricingCalculation) ||
                other.lastPricingCalculation == lastPricingCalculation) &&
            (identical(other.hasPricingConfirmation, hasPricingConfirmation) ||
                other.hasPricingConfirmation == hasPricingConfirmation) &&
            (identical(other.isInitialized, isInitialized) ||
                other.isInitialized == isInitialized));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      currentStep,
      pickupLocation,
      dropoffLocation,
      pricingInfo,
      specialInstructions,
      currentRide,
      isLoadingPickupLocation,
      isLoadingDropoffLocation,
      isLoadingPricing,
      isSubmittingRequest,
      isLoadingActiveRide,
      error,
      const DeepCollectionEquality().hash(_fieldErrors),
      hasAttemptedSubmit,
      lastPricingCalculation,
      hasPricingConfirmation,
      isInitialized);

  /// Create a copy of BookingFlowState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BookingFlowStateImplCopyWith<_$BookingFlowStateImpl> get copyWith =>
      __$$BookingFlowStateImplCopyWithImpl<_$BookingFlowStateImpl>(
          this, _$identity);
}

abstract class _BookingFlowState extends BookingFlowState {
  const factory _BookingFlowState(
      {final BookingStep currentStep,
      final RideLocation? pickupLocation,
      final RideLocation? dropoffLocation,
      final PricingInfo? pricingInfo,
      final String specialInstructions,
      final Ride? currentRide,
      final bool isLoadingPickupLocation,
      final bool isLoadingDropoffLocation,
      final bool isLoadingPricing,
      final bool isSubmittingRequest,
      final bool isLoadingActiveRide,
      final AppError? error,
      final Map<String, String> fieldErrors,
      final bool hasAttemptedSubmit,
      final DateTime? lastPricingCalculation,
      final bool hasPricingConfirmation,
      final bool isInitialized}) = _$BookingFlowStateImpl;
  const _BookingFlowState._() : super._();

  /// Current step in the booking flow
  @override
  BookingStep get currentStep;

  /// Selected pickup location
  @override
  RideLocation? get pickupLocation;

  /// Selected dropoff location
  @override
  RideLocation? get dropoffLocation;

  /// Calculated pricing information
  @override
  PricingInfo? get pricingInfo;

  /// Special instructions for the driver
  @override
  String get specialInstructions;

  /// Current ride request (if any)
  @override
  Ride? get currentRide;

  /// Loading states for various operations
  @override
  bool get isLoadingPickupLocation;
  @override
  bool get isLoadingDropoffLocation;
  @override
  bool get isLoadingPricing;
  @override
  bool get isSubmittingRequest;
  @override
  bool get isLoadingActiveRide;

  /// Error states
  @override
  AppError? get error;
  @override
  Map<String, String> get fieldErrors;

  /// Form validation state
  @override
  bool get hasAttemptedSubmit;

  /// Timestamp of last pricing calculation
  @override
  DateTime? get lastPricingCalculation;

  /// Flag indicating if user has confirmed the pricing
  @override
  bool get hasPricingConfirmation;

  /// Flag indicating if the flow has been initialized
  @override
  bool get isInitialized;

  /// Create a copy of BookingFlowState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BookingFlowStateImplCopyWith<_$BookingFlowStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
