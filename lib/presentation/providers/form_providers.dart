import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/utils/form_validators.dart';
import '../../domain/entities/user.dart';
import 'form_states.dart';

/// Login form state provider with real-time validation
class LoginFormNotifier extends StateNotifier<LoginFormState> {
  LoginFormNotifier() : super(const LoginFormState());

  /// Update email field and validate
  void updateEmail(String email) {
    state = state.copyWith(
      email: email,
      fieldErrors: _updateFieldErrors(
        'email',
        FormValidators.validateEmail(email),
      ),
    );
    _updateFormValidity();
  }

  /// Update password field and validate
  void updatePassword(String password) {
    state = state.copyWith(
      password: password,
      fieldErrors: _updateFieldErrors(
        'password',
        FormValidators.validatePassword(password),
      ),
    );
    _updateFormValidity();
  }

  /// Toggle password visibility
  void togglePasswordVisibility() {
    state = state.copyWith(obscurePassword: !state.obscurePassword);
  }

  /// Set loading state
  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  /// Set form error message
  void setError(String? errorMessage) {
    state = state.copyWith(errorMessage: errorMessage);
  }

  /// Clear form error message
  void clearError() {
    state = state.copyWith(errorMessage: null);
  }

  /// Mark form as attempted submit for validation display
  void markAttemptedSubmit() {
    state = state.copyWith(hasAttemptedSubmit: true);
    _validateAllFields();
  }

  /// Reset form to initial state
  void reset() {
    state = const LoginFormState();
  }

  /// Validate all fields and update form validity
  void _validateAllFields() {
    final errors = FormValidators.validateLoginForm(
      email: state.email,
      password: state.password,
    );

    state = state.copyWith(
      fieldErrors: errors,
      isFormValid: FormValidators.isLoginFormValid(
        email: state.email,
        password: state.password,
      ),
    );
  }

  /// Update field errors map
  Map<String, String> _updateFieldErrors(String field, String? error) {
    final errors = Map<String, String>.from(state.fieldErrors);
    if (error != null) {
      errors[field] = error;
    } else {
      errors.remove(field);
    }
    return errors;
  }

  /// Update form validity based on current state
  void _updateFormValidity() {
    final isValid = FormValidators.isLoginFormValid(
      email: state.email,
      password: state.password,
    );

    if (state.isFormValid != isValid) {
      state = state.copyWith(isFormValid: isValid);
    }
  }
}

/// Register form state provider with real-time validation
class RegisterFormNotifier extends StateNotifier<RegisterFormState> {
  RegisterFormNotifier() : super(const RegisterFormState());

  /// Update name field and validate
  void updateName(String name) {
    state = state.copyWith(
      name: name,
      fieldErrors: _updateFieldErrors(
        'name',
        FormValidators.validateName(name),
      ),
    );
    _updateFormValidity();
  }

  /// Update email field and validate
  void updateEmail(String email) {
    state = state.copyWith(
      email: email,
      fieldErrors: _updateFieldErrors(
        'email',
        FormValidators.validateEmail(email),
      ),
    );
    _updateFormValidity();
  }

  /// Update password field and validate
  void updatePassword(String password) {
    state = state.copyWith(
      password: password,
      fieldErrors: _updateFieldErrors(
        'password',
        FormValidators.validatePassword(password),
      ),
    );
    // Re-validate confirm password if it exists
    if (state.confirmPassword.isNotEmpty) {
      final confirmError = FormValidators.validatePasswordConfirmation(
        state.confirmPassword,
        password,
      );
      state = state.copyWith(
        fieldErrors: _updateFieldErrors('confirmPassword', confirmError),
      );
    }
    _updateFormValidity();
  }

  /// Update confirm password field and validate
  void updateConfirmPassword(String confirmPassword) {
    final error = FormValidators.validatePasswordConfirmation(
      confirmPassword,
      state.password,
    );
    state = state.copyWith(
      confirmPassword: confirmPassword,
      fieldErrors: _updateFieldErrors('confirmPassword', error),
    );
    _updateFormValidity();
  }

  /// Update phone field and validate
  void updatePhone(String phone) {
    state = state.copyWith(
      phone: phone,
      fieldErrors: _updateFieldErrors(
        'phone',
        FormValidators.validatePhone(phone),
      ),
    );
    _updateFormValidity();
  }

  /// Update user type
  void updateUserType(UserType userType) {
    state = state.copyWith(userType: userType);
  }

  /// Toggle password visibility
  void togglePasswordVisibility() {
    state = state.copyWith(obscurePassword: !state.obscurePassword);
  }

  /// Toggle confirm password visibility
  void toggleConfirmPasswordVisibility() {
    state = state.copyWith(
      obscureConfirmPassword: !state.obscureConfirmPassword,
    );
  }

  /// Set loading state
  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  /// Set form error message
  void setError(String? errorMessage) {
    state = state.copyWith(errorMessage: errorMessage);
  }

  /// Clear form error message
  void clearError() {
    state = state.copyWith(errorMessage: null);
  }

  /// Mark form as attempted submit for validation display
  void markAttemptedSubmit() {
    state = state.copyWith(hasAttemptedSubmit: true);
    _validateAllFields();
  }

  /// Reset form to initial state
  void reset() {
    state = const RegisterFormState();
  }

  /// Validate all fields and update form validity
  void _validateAllFields() {
    final errors = FormValidators.validateRegisterForm(
      name: state.name,
      email: state.email,
      password: state.password,
      confirmPassword: state.confirmPassword,
      phone: state.phone.isEmpty ? null : state.phone,
    );

    state = state.copyWith(
      fieldErrors: errors,
      isFormValid: FormValidators.isRegisterFormValid(
        name: state.name,
        email: state.email,
        password: state.password,
        confirmPassword: state.confirmPassword,
        phone: state.phone.isEmpty ? null : state.phone,
      ),
    );
  }

  /// Update field errors map
  Map<String, String> _updateFieldErrors(String field, String? error) {
    final errors = Map<String, String>.from(state.fieldErrors);
    if (error != null) {
      errors[field] = error;
    } else {
      errors.remove(field);
    }
    return errors;
  }

  /// Update form validity based on current state
  void _updateFormValidity() {
    final isValid = FormValidators.isRegisterFormValid(
      name: state.name,
      email: state.email,
      password: state.password,
      confirmPassword: state.confirmPassword,
      phone: state.phone.isEmpty ? null : state.phone,
    );

    if (state.isFormValid != isValid) {
      state = state.copyWith(isFormValid: isValid);
    }
  }
}

/// Profile form state provider with real-time validation
class ProfileFormNotifier extends StateNotifier<ProfileFormState> {
  ProfileFormNotifier() : super(const ProfileFormState());

  String _originalName = '';
  String _originalEmail = '';
  String _originalPhone = '';

  /// Initialize form with current user data
  void initialize({
    required String name,
    required String email,
    String? phone,
  }) {
    _originalName = name;
    _originalEmail = email;
    _originalPhone = phone ?? '';

    state = ProfileFormState(
      name: name,
      email: email,
      phone: phone ?? '',
      isFormValid: true, // Initially valid since it's existing data
    );
  }

  /// Update name field and validate
  void updateName(String name) {
    state = state.copyWith(
      name: name,
      fieldErrors: _updateFieldErrors(
        'name',
        FormValidators.validateName(name),
      ),
    );
    _updateFormValidity();
    _updateHasChanges();
  }

  /// Update email field and validate
  void updateEmail(String email) {
    state = state.copyWith(
      email: email,
      fieldErrors: _updateFieldErrors(
        'email',
        FormValidators.validateEmail(email),
      ),
    );
    _updateFormValidity();
    _updateHasChanges();
  }

  /// Update phone field and validate
  void updatePhone(String phone) {
    state = state.copyWith(
      phone: phone,
      fieldErrors: _updateFieldErrors(
        'phone',
        FormValidators.validatePhone(phone),
      ),
    );
    _updateFormValidity();
    _updateHasChanges();
  }

  /// Set loading state
  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  /// Set form error message
  void setError(String? errorMessage) {
    state = state.copyWith(errorMessage: errorMessage);
  }

  /// Clear form error message
  void clearError() {
    state = state.copyWith(errorMessage: null);
  }

  /// Mark form as attempted submit for validation display
  void markAttemptedSubmit() {
    state = state.copyWith(hasAttemptedSubmit: true);
    _validateAllFields();
  }

  /// Reset form to original values
  void reset() {
    state = ProfileFormState(
      name: _originalName,
      email: _originalEmail,
      phone: _originalPhone,
      isFormValid: true,
      hasChanges: false,
    );
  }

  /// Validate all fields and update form validity
  void _validateAllFields() {
    final errors = FormValidators.validateProfileForm(
      name: state.name,
      email: state.email,
      phone: state.phone.isEmpty ? null : state.phone,
    );

    state = state.copyWith(
      fieldErrors: errors,
      isFormValid: FormValidators.isProfileFormValid(
        name: state.name,
        email: state.email,
        phone: state.phone.isEmpty ? null : state.phone,
      ),
    );
  }

  /// Update field errors map
  Map<String, String> _updateFieldErrors(String field, String? error) {
    final errors = Map<String, String>.from(state.fieldErrors);
    if (error != null) {
      errors[field] = error;
    } else {
      errors.remove(field);
    }
    return errors;
  }

  /// Update form validity based on current state
  void _updateFormValidity() {
    final isValid = FormValidators.isProfileFormValid(
      name: state.name,
      email: state.email,
      phone: state.phone.isEmpty ? null : state.phone,
    );

    if (state.isFormValid != isValid) {
      state = state.copyWith(isFormValid: isValid);
    }
  }

  /// Update has changes flag
  void _updateHasChanges() {
    final hasChanges =
        state.name != _originalName ||
        state.email != _originalEmail ||
        state.phone != _originalPhone;

    if (state.hasChanges != hasChanges) {
      state = state.copyWith(hasChanges: hasChanges);
    }
  }
}

/// Provider instances for form state management
final loginFormProvider =
    StateNotifierProvider<LoginFormNotifier, LoginFormState>(
      (ref) => LoginFormNotifier(),
    );

final registerFormProvider =
    StateNotifierProvider<RegisterFormNotifier, RegisterFormState>(
      (ref) => RegisterFormNotifier(),
    );

final profileFormProvider =
    StateNotifierProvider<ProfileFormNotifier, ProfileFormState>(
      (ref) => ProfileFormNotifier(),
    );
