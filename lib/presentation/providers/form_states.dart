import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/user.dart';

part 'form_states.freezed.dart';

/// Login form state with validation and loading states
@freezed
class LoginFormState with _$LoginFormState {
  const factory LoginFormState({
    @Default('') String email,
    @Default('') String password,
    @Default(false) bool isLoading,
    @Default(false) bool obscurePassword,
    String? errorMessage,
    @Default({}) Map<String, String> fieldErrors,
    @Default(false) bool isFormValid,
    @Default(false) bool hasAttemptedSubmit,
  }) = _LoginFormState;

  const LoginFormState._();

  /// Check if form has any errors
  bool get hasErrors => fieldErrors.isNotEmpty || errorMessage != null;

  /// Check if form can be submitted
  bool get canSubmit => isFormValid && !isLoading && !hasErrors;

  /// Get error for specific field
  String? getFieldError(String field) => fieldErrors[field];

  /// Check if specific field has error
  bool hasFieldError(String field) => fieldErrors.containsKey(field);
}

/// Register form state with validation and loading states
@freezed
class RegisterFormState with _$RegisterFormState {
  const factory RegisterFormState({
    @Default('') String name,
    @Default('') String email,
    @Default('') String password,
    @Default('') String confirmPassword,
    @Default('') String phone,
    @Default(UserType.rider) UserType userType,
    @Default(false) bool isLoading,
    @Default(false) bool obscurePassword,
    @Default(false) bool obscureConfirmPassword,
    String? errorMessage,
    @Default({}) Map<String, String> fieldErrors,
    @Default(false) bool isFormValid,
    @Default(false) bool hasAttemptedSubmit,
  }) = _RegisterFormState;

  const RegisterFormState._();

  /// Check if form has any errors
  bool get hasErrors => fieldErrors.isNotEmpty || errorMessage != null;

  /// Check if form can be submitted
  bool get canSubmit => isFormValid && !isLoading && !hasErrors;

  /// Get error for specific field
  String? getFieldError(String field) => fieldErrors[field];

  /// Check if specific field has error
  bool hasFieldError(String field) => fieldErrors.containsKey(field);

  /// Check if passwords match
  bool get passwordsMatch => password == confirmPassword;
}

/// Profile form state for user profile updates
@freezed
class ProfileFormState with _$ProfileFormState {
  const factory ProfileFormState({
    @Default('') String name,
    @Default('') String email,
    @Default('') String phone,
    @Default(false) bool isLoading,
    String? errorMessage,
    @Default({}) Map<String, String> fieldErrors,
    @Default(false) bool isFormValid,
    @Default(false) bool hasAttemptedSubmit,
    @Default(false) bool hasChanges,
  }) = _ProfileFormState;

  const ProfileFormState._();

  /// Check if form has any errors
  bool get hasErrors => fieldErrors.isNotEmpty || errorMessage != null;

  /// Check if form can be submitted
  bool get canSubmit => isFormValid && !isLoading && !hasErrors && hasChanges;

  /// Get error for specific field
  String? getFieldError(String field) => fieldErrors[field];

  /// Check if specific field has error
  bool hasFieldError(String field) => fieldErrors.containsKey(field);
}
