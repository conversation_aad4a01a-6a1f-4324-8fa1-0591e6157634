import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../core/di/service_locator.dart';
import '../../services/sync/offline_data_service.dart';
import '../../services/sync/data_sync_service.dart';
import '../../domain/entities/ride.dart';

part 'offline_data_provider.freezed.dart';

/// State for offline data management
@freezed
class OfflineDataState with _$OfflineDataState {
  const factory OfflineDataState({
    @Default(false) bool isInitialized,
    @Default(false) bool isSyncing,
    @Default(null) DateTime? lastSyncTime,
    @Default(SyncStatus.idle) SyncStatus syncStatus,
    @Default(null) String? syncError,
    @Default(null) CacheStatistics? cacheStats,
    @Default(false) bool hasActiveRide,
    @Default(0) int rideHistoryCount,
    @Default(0) int recentLocationsCount,
  }) = _OfflineDataState;
}

/// Notifier for managing offline data state
class OfflineDataNotifier extends StateNotifier<OfflineDataState> {
  final OfflineDataService _offlineDataService;
  final DataSyncService _dataSyncService;

  OfflineDataNotifier(this._offlineDataService, this._dataSyncService)
    : super(const OfflineDataState()) {
    _initialize();
  }

  /// Initialize the offline data service
  Future<void> _initialize() async {
    try {
      await _offlineDataService.initialize();
      await _dataSyncService.initialize();

      // Get initial cache statistics
      await refreshCacheStatistics();

      state = state.copyWith(
        isInitialized: true,
        lastSyncTime: await _dataSyncService.getLastSyncTime(),
        syncStatus: _dataSyncService.getSyncStatus(),
      );
    } catch (e) {
      state = state.copyWith(
        isInitialized: true,
        syncError: 'Failed to initialize offline data service: $e',
      );
    }
  }

  /// Refresh cache statistics
  Future<void> refreshCacheStatistics() async {
    try {
      final stats = await _offlineDataService.getCacheStatistics();
      state = state.copyWith(
        cacheStats: stats,
        hasActiveRide: stats.hasActiveRide,
        rideHistoryCount: stats.rideHistoryCount,
        recentLocationsCount: stats.recentLocationsCount,
      );
    } catch (e) {
      state = state.copyWith(syncError: 'Failed to get cache statistics: $e');
    }
  }

  /// Perform full data synchronization
  Future<void> performFullSync() async {
    if (state.isSyncing) return;

    state = state.copyWith(
      isSyncing: true,
      syncStatus: SyncStatus.syncing,
      syncError: null,
    );

    try {
      final result = await _dataSyncService.performFullSync();

      state = state.copyWith(
        isSyncing: false,
        syncStatus: result.success ? SyncStatus.completed : SyncStatus.failed,
        syncError: result.success ? null : result.message,
        lastSyncTime: await _dataSyncService.getLastSyncTime(),
      );

      // Refresh cache statistics after sync
      await refreshCacheStatistics();
    } catch (e) {
      state = state.copyWith(
        isSyncing: false,
        syncStatus: SyncStatus.failed,
        syncError: 'Sync failed: $e',
      );
    }
  }

  /// Force synchronization regardless of last sync time
  Future<void> forceSync() async {
    if (state.isSyncing) return;

    state = state.copyWith(
      isSyncing: true,
      syncStatus: SyncStatus.syncing,
      syncError: null,
    );

    try {
      final result = await _dataSyncService.forceSync();

      state = state.copyWith(
        isSyncing: false,
        syncStatus: result.success ? SyncStatus.completed : SyncStatus.failed,
        syncError: result.success ? null : result.message,
        lastSyncTime: await _dataSyncService.getLastSyncTime(),
      );

      // Refresh cache statistics after sync
      await refreshCacheStatistics();
    } catch (e) {
      state = state.copyWith(
        isSyncing: false,
        syncStatus: SyncStatus.failed,
        syncError: 'Force sync failed: $e',
      );
    }
  }

  /// Clear all cached data
  Future<void> clearAllCache() async {
    try {
      await _offlineDataService.clearAllCache();
      await refreshCacheStatistics();
    } catch (e) {
      state = state.copyWith(syncError: 'Failed to clear cache: $e');
    }
  }

  /// Get cached active ride
  Future<Ride?> getCachedActiveRide() async {
    try {
      return await _offlineDataService.getCachedActiveRide();
    } catch (e) {
      state = state.copyWith(syncError: 'Failed to get cached active ride: $e');
      return null;
    }
  }

  /// Get cached ride history
  Future<List<RideHistory>?> getCachedRideHistory() async {
    try {
      return await _offlineDataService.getCachedRideHistory();
    } catch (e) {
      state = state.copyWith(
        syncError: 'Failed to get cached ride history: $e',
      );
      return null;
    }
  }

  /// Get cached recent locations
  Future<List<RideLocation>?> getCachedRecentLocations() async {
    try {
      return await _offlineDataService.getCachedRecentLocations();
    } catch (e) {
      state = state.copyWith(
        syncError: 'Failed to get cached recent locations: $e',
      );
      return null;
    }
  }

  /// Check if data needs refresh
  Future<bool> needsRefresh(String key) async {
    try {
      return await _offlineDataService.needsRefresh(key);
    } catch (e) {
      return true; // Assume refresh is needed if check fails
    }
  }

  /// Clear sync error
  void clearSyncError() {
    state = state.copyWith(syncError: null);
  }

  @override
  void dispose() {
    _offlineDataService.dispose();
    _dataSyncService.dispose();
    super.dispose();
  }
}

/// Provider for offline data service
final offlineDataServiceProvider = Provider<OfflineDataService>((ref) {
  return getIt<OfflineDataService>();
});

/// Provider for data sync service
final dataSyncServiceProvider = Provider<DataSyncService>((ref) {
  return getIt<DataSyncService>();
});

/// Provider for offline data state
final offlineDataProvider =
    StateNotifierProvider<OfflineDataNotifier, OfflineDataState>((ref) {
      final offlineDataService = ref.read(offlineDataServiceProvider);
      final dataSyncService = ref.read(dataSyncServiceProvider);
      return OfflineDataNotifier(offlineDataService, dataSyncService);
    });

/// Provider to check if sync is needed
final needsSyncProvider = FutureProvider<bool>((ref) async {
  final dataSyncService = ref.read(dataSyncServiceProvider);
  return await dataSyncService.needsSync();
});

/// Provider for cache statistics
final cacheStatisticsProvider = FutureProvider<CacheStatistics>((ref) async {
  final offlineDataService = ref.read(offlineDataServiceProvider);
  return await offlineDataService.getCacheStatistics();
});

/// Provider for cached active ride
final cachedActiveRideProvider = FutureProvider<Ride?>((ref) async {
  final offlineDataService = ref.read(offlineDataServiceProvider);
  return await offlineDataService.getCachedActiveRide();
});

/// Provider for cached ride history
final cachedRideHistoryProvider = FutureProvider<List<RideHistory>?>((
  ref,
) async {
  final offlineDataService = ref.read(offlineDataServiceProvider);
  return await offlineDataService.getCachedRideHistory();
});

/// Provider for cached recent locations
final cachedRecentLocationsProvider = FutureProvider<List<RideLocation>?>((
  ref,
) async {
  final offlineDataService = ref.read(offlineDataServiceProvider);
  return await offlineDataService.getCachedRecentLocations();
});
