// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'offline_data_provider.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$OfflineDataState {
  bool get isInitialized => throw _privateConstructorUsedError;
  bool get isSyncing => throw _privateConstructorUsedError;
  DateTime? get lastSyncTime => throw _privateConstructorUsedError;
  SyncStatus get syncStatus => throw _privateConstructorUsedError;
  String? get syncError => throw _privateConstructorUsedError;
  CacheStatistics? get cacheStats => throw _privateConstructorUsedError;
  bool get hasActiveRide => throw _privateConstructorUsedError;
  int get rideHistoryCount => throw _privateConstructorUsedError;
  int get recentLocationsCount => throw _privateConstructorUsedError;

  /// Create a copy of OfflineDataState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OfflineDataStateCopyWith<OfflineDataState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OfflineDataStateCopyWith<$Res> {
  factory $OfflineDataStateCopyWith(
          OfflineDataState value, $Res Function(OfflineDataState) then) =
      _$OfflineDataStateCopyWithImpl<$Res, OfflineDataState>;
  @useResult
  $Res call(
      {bool isInitialized,
      bool isSyncing,
      DateTime? lastSyncTime,
      SyncStatus syncStatus,
      String? syncError,
      CacheStatistics? cacheStats,
      bool hasActiveRide,
      int rideHistoryCount,
      int recentLocationsCount});
}

/// @nodoc
class _$OfflineDataStateCopyWithImpl<$Res, $Val extends OfflineDataState>
    implements $OfflineDataStateCopyWith<$Res> {
  _$OfflineDataStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OfflineDataState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isInitialized = null,
    Object? isSyncing = null,
    Object? lastSyncTime = freezed,
    Object? syncStatus = null,
    Object? syncError = freezed,
    Object? cacheStats = freezed,
    Object? hasActiveRide = null,
    Object? rideHistoryCount = null,
    Object? recentLocationsCount = null,
  }) {
    return _then(_value.copyWith(
      isInitialized: null == isInitialized
          ? _value.isInitialized
          : isInitialized // ignore: cast_nullable_to_non_nullable
              as bool,
      isSyncing: null == isSyncing
          ? _value.isSyncing
          : isSyncing // ignore: cast_nullable_to_non_nullable
              as bool,
      lastSyncTime: freezed == lastSyncTime
          ? _value.lastSyncTime
          : lastSyncTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      syncStatus: null == syncStatus
          ? _value.syncStatus
          : syncStatus // ignore: cast_nullable_to_non_nullable
              as SyncStatus,
      syncError: freezed == syncError
          ? _value.syncError
          : syncError // ignore: cast_nullable_to_non_nullable
              as String?,
      cacheStats: freezed == cacheStats
          ? _value.cacheStats
          : cacheStats // ignore: cast_nullable_to_non_nullable
              as CacheStatistics?,
      hasActiveRide: null == hasActiveRide
          ? _value.hasActiveRide
          : hasActiveRide // ignore: cast_nullable_to_non_nullable
              as bool,
      rideHistoryCount: null == rideHistoryCount
          ? _value.rideHistoryCount
          : rideHistoryCount // ignore: cast_nullable_to_non_nullable
              as int,
      recentLocationsCount: null == recentLocationsCount
          ? _value.recentLocationsCount
          : recentLocationsCount // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OfflineDataStateImplCopyWith<$Res>
    implements $OfflineDataStateCopyWith<$Res> {
  factory _$$OfflineDataStateImplCopyWith(_$OfflineDataStateImpl value,
          $Res Function(_$OfflineDataStateImpl) then) =
      __$$OfflineDataStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isInitialized,
      bool isSyncing,
      DateTime? lastSyncTime,
      SyncStatus syncStatus,
      String? syncError,
      CacheStatistics? cacheStats,
      bool hasActiveRide,
      int rideHistoryCount,
      int recentLocationsCount});
}

/// @nodoc
class __$$OfflineDataStateImplCopyWithImpl<$Res>
    extends _$OfflineDataStateCopyWithImpl<$Res, _$OfflineDataStateImpl>
    implements _$$OfflineDataStateImplCopyWith<$Res> {
  __$$OfflineDataStateImplCopyWithImpl(_$OfflineDataStateImpl _value,
      $Res Function(_$OfflineDataStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of OfflineDataState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isInitialized = null,
    Object? isSyncing = null,
    Object? lastSyncTime = freezed,
    Object? syncStatus = null,
    Object? syncError = freezed,
    Object? cacheStats = freezed,
    Object? hasActiveRide = null,
    Object? rideHistoryCount = null,
    Object? recentLocationsCount = null,
  }) {
    return _then(_$OfflineDataStateImpl(
      isInitialized: null == isInitialized
          ? _value.isInitialized
          : isInitialized // ignore: cast_nullable_to_non_nullable
              as bool,
      isSyncing: null == isSyncing
          ? _value.isSyncing
          : isSyncing // ignore: cast_nullable_to_non_nullable
              as bool,
      lastSyncTime: freezed == lastSyncTime
          ? _value.lastSyncTime
          : lastSyncTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      syncStatus: null == syncStatus
          ? _value.syncStatus
          : syncStatus // ignore: cast_nullable_to_non_nullable
              as SyncStatus,
      syncError: freezed == syncError
          ? _value.syncError
          : syncError // ignore: cast_nullable_to_non_nullable
              as String?,
      cacheStats: freezed == cacheStats
          ? _value.cacheStats
          : cacheStats // ignore: cast_nullable_to_non_nullable
              as CacheStatistics?,
      hasActiveRide: null == hasActiveRide
          ? _value.hasActiveRide
          : hasActiveRide // ignore: cast_nullable_to_non_nullable
              as bool,
      rideHistoryCount: null == rideHistoryCount
          ? _value.rideHistoryCount
          : rideHistoryCount // ignore: cast_nullable_to_non_nullable
              as int,
      recentLocationsCount: null == recentLocationsCount
          ? _value.recentLocationsCount
          : recentLocationsCount // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$OfflineDataStateImpl implements _OfflineDataState {
  const _$OfflineDataStateImpl(
      {this.isInitialized = false,
      this.isSyncing = false,
      this.lastSyncTime = null,
      this.syncStatus = SyncStatus.idle,
      this.syncError = null,
      this.cacheStats = null,
      this.hasActiveRide = false,
      this.rideHistoryCount = 0,
      this.recentLocationsCount = 0});

  @override
  @JsonKey()
  final bool isInitialized;
  @override
  @JsonKey()
  final bool isSyncing;
  @override
  @JsonKey()
  final DateTime? lastSyncTime;
  @override
  @JsonKey()
  final SyncStatus syncStatus;
  @override
  @JsonKey()
  final String? syncError;
  @override
  @JsonKey()
  final CacheStatistics? cacheStats;
  @override
  @JsonKey()
  final bool hasActiveRide;
  @override
  @JsonKey()
  final int rideHistoryCount;
  @override
  @JsonKey()
  final int recentLocationsCount;

  @override
  String toString() {
    return 'OfflineDataState(isInitialized: $isInitialized, isSyncing: $isSyncing, lastSyncTime: $lastSyncTime, syncStatus: $syncStatus, syncError: $syncError, cacheStats: $cacheStats, hasActiveRide: $hasActiveRide, rideHistoryCount: $rideHistoryCount, recentLocationsCount: $recentLocationsCount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OfflineDataStateImpl &&
            (identical(other.isInitialized, isInitialized) ||
                other.isInitialized == isInitialized) &&
            (identical(other.isSyncing, isSyncing) ||
                other.isSyncing == isSyncing) &&
            (identical(other.lastSyncTime, lastSyncTime) ||
                other.lastSyncTime == lastSyncTime) &&
            (identical(other.syncStatus, syncStatus) ||
                other.syncStatus == syncStatus) &&
            (identical(other.syncError, syncError) ||
                other.syncError == syncError) &&
            (identical(other.cacheStats, cacheStats) ||
                other.cacheStats == cacheStats) &&
            (identical(other.hasActiveRide, hasActiveRide) ||
                other.hasActiveRide == hasActiveRide) &&
            (identical(other.rideHistoryCount, rideHistoryCount) ||
                other.rideHistoryCount == rideHistoryCount) &&
            (identical(other.recentLocationsCount, recentLocationsCount) ||
                other.recentLocationsCount == recentLocationsCount));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isInitialized,
      isSyncing,
      lastSyncTime,
      syncStatus,
      syncError,
      cacheStats,
      hasActiveRide,
      rideHistoryCount,
      recentLocationsCount);

  /// Create a copy of OfflineDataState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OfflineDataStateImplCopyWith<_$OfflineDataStateImpl> get copyWith =>
      __$$OfflineDataStateImplCopyWithImpl<_$OfflineDataStateImpl>(
          this, _$identity);
}

abstract class _OfflineDataState implements OfflineDataState {
  const factory _OfflineDataState(
      {final bool isInitialized,
      final bool isSyncing,
      final DateTime? lastSyncTime,
      final SyncStatus syncStatus,
      final String? syncError,
      final CacheStatistics? cacheStats,
      final bool hasActiveRide,
      final int rideHistoryCount,
      final int recentLocationsCount}) = _$OfflineDataStateImpl;

  @override
  bool get isInitialized;
  @override
  bool get isSyncing;
  @override
  DateTime? get lastSyncTime;
  @override
  SyncStatus get syncStatus;
  @override
  String? get syncError;
  @override
  CacheStatistics? get cacheStats;
  @override
  bool get hasActiveRide;
  @override
  int get rideHistoryCount;
  @override
  int get recentLocationsCount;

  /// Create a copy of OfflineDataState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OfflineDataStateImplCopyWith<_$OfflineDataStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
