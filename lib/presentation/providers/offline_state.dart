import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../services/network/network_service.dart';
import '../../core/di/service_locator.dart';

part 'offline_state.freezed.dart';

/// Offline state model
@freezed
class OfflineState with _$OfflineState {
  const factory OfflineState({
    @Default(ConnectivityResult.none) ConnectivityResult connectivity,
    @Default(false) bool isOnline,
    @Default(true) bool isInitialized,
    String? lastConnectedAt,
    @Default(false) bool showOfflineIndicator,
  }) = _OfflineState;
}

/// Offline state notifier to manage network connectivity state
class OfflineStateNotifier extends StateNotifier<OfflineState> {
  final NetworkService _networkService;

  OfflineStateNotifier(this._networkService) : super(const OfflineState()) {
    _initialize();
  }

  /// Initialize network monitoring
  Future<void> _initialize() async {
    try {
      await _networkService.initialize();

      // Get initial connectivity status
      final connectivity = await _networkService.getCurrentConnectivity();
      final isOnline = connectivity != ConnectivityResult.none;

      state = state.copyWith(
        connectivity: connectivity,
        isOnline: isOnline,
        isInitialized: true,
        showOfflineIndicator: !isOnline,
        lastConnectedAt: isOnline ? DateTime.now().toIso8601String() : null,
      );

      // Listen for connectivity changes
      _networkService.connectivityStream.listen((connectivity) {
        final isOnline = connectivity != ConnectivityResult.none;

        state = state.copyWith(
          connectivity: connectivity,
          isOnline: isOnline,
          showOfflineIndicator: !isOnline,
          lastConnectedAt: isOnline
              ? DateTime.now().toIso8601String()
              : state.lastConnectedAt,
        );
      });
    } catch (e) {
      // If initialization fails, assume offline
      state = state.copyWith(
        connectivity: ConnectivityResult.none,
        isOnline: false,
        isInitialized: true,
        showOfflineIndicator: true,
      );
    }
  }

  /// Manually refresh connectivity status
  Future<void> refreshConnectivity() async {
    try {
      final connectivity = await _networkService.getCurrentConnectivity();
      final isOnline = connectivity != ConnectivityResult.none;

      state = state.copyWith(
        connectivity: connectivity,
        isOnline: isOnline,
        showOfflineIndicator: !isOnline,
        lastConnectedAt: isOnline
            ? DateTime.now().toIso8601String()
            : state.lastConnectedAt,
      );
    } catch (e) {
      // If refresh fails, assume offline
      state = state.copyWith(
        connectivity: ConnectivityResult.none,
        isOnline: false,
        showOfflineIndicator: true,
      );
    }
  }

  /// Hide offline indicator temporarily
  void hideOfflineIndicator() {
    state = state.copyWith(showOfflineIndicator: false);
  }

  /// Show offline indicator
  void showOfflineIndicator() {
    state = state.copyWith(showOfflineIndicator: !state.isOnline);
  }

  @override
  void dispose() {
    _networkService.dispose();
    super.dispose();
  }
}

/// Provider for network service
final networkServiceProvider = Provider<NetworkService>((ref) {
  return getIt<NetworkService>();
});

/// Provider for offline state
final offlineStateProvider =
    StateNotifierProvider<OfflineStateNotifier, OfflineState>((ref) {
      final networkService = ref.read(networkServiceProvider);
      return OfflineStateNotifier(networkService);
    });

/// Provider to check if device is online
final isOnlineProvider = Provider<bool>((ref) {
  final offlineState = ref.watch(offlineStateProvider);
  return offlineState.isOnline;
});

/// Provider to check if device is offline
final isOfflineProvider = Provider<bool>((ref) {
  final offlineState = ref.watch(offlineStateProvider);
  return !offlineState.isOnline;
});

/// Provider for connectivity status
final connectivityProvider = Provider<ConnectivityResult>((ref) {
  final offlineState = ref.watch(offlineStateProvider);
  return offlineState.connectivity;
});
