// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'offline_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$OfflineState {
  ConnectivityResult get connectivity => throw _privateConstructorUsedError;
  bool get isOnline => throw _privateConstructorUsedError;
  bool get isInitialized => throw _privateConstructorUsedError;
  String? get lastConnectedAt => throw _privateConstructorUsedError;
  bool get showOfflineIndicator => throw _privateConstructorUsedError;

  /// Create a copy of OfflineState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OfflineStateCopyWith<OfflineState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OfflineStateCopyWith<$Res> {
  factory $OfflineStateCopyWith(
          OfflineState value, $Res Function(OfflineState) then) =
      _$OfflineStateCopyWithImpl<$Res, OfflineState>;
  @useResult
  $Res call(
      {ConnectivityResult connectivity,
      bool isOnline,
      bool isInitialized,
      String? lastConnectedAt,
      bool showOfflineIndicator});
}

/// @nodoc
class _$OfflineStateCopyWithImpl<$Res, $Val extends OfflineState>
    implements $OfflineStateCopyWith<$Res> {
  _$OfflineStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OfflineState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? connectivity = null,
    Object? isOnline = null,
    Object? isInitialized = null,
    Object? lastConnectedAt = freezed,
    Object? showOfflineIndicator = null,
  }) {
    return _then(_value.copyWith(
      connectivity: null == connectivity
          ? _value.connectivity
          : connectivity // ignore: cast_nullable_to_non_nullable
              as ConnectivityResult,
      isOnline: null == isOnline
          ? _value.isOnline
          : isOnline // ignore: cast_nullable_to_non_nullable
              as bool,
      isInitialized: null == isInitialized
          ? _value.isInitialized
          : isInitialized // ignore: cast_nullable_to_non_nullable
              as bool,
      lastConnectedAt: freezed == lastConnectedAt
          ? _value.lastConnectedAt
          : lastConnectedAt // ignore: cast_nullable_to_non_nullable
              as String?,
      showOfflineIndicator: null == showOfflineIndicator
          ? _value.showOfflineIndicator
          : showOfflineIndicator // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OfflineStateImplCopyWith<$Res>
    implements $OfflineStateCopyWith<$Res> {
  factory _$$OfflineStateImplCopyWith(
          _$OfflineStateImpl value, $Res Function(_$OfflineStateImpl) then) =
      __$$OfflineStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {ConnectivityResult connectivity,
      bool isOnline,
      bool isInitialized,
      String? lastConnectedAt,
      bool showOfflineIndicator});
}

/// @nodoc
class __$$OfflineStateImplCopyWithImpl<$Res>
    extends _$OfflineStateCopyWithImpl<$Res, _$OfflineStateImpl>
    implements _$$OfflineStateImplCopyWith<$Res> {
  __$$OfflineStateImplCopyWithImpl(
      _$OfflineStateImpl _value, $Res Function(_$OfflineStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of OfflineState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? connectivity = null,
    Object? isOnline = null,
    Object? isInitialized = null,
    Object? lastConnectedAt = freezed,
    Object? showOfflineIndicator = null,
  }) {
    return _then(_$OfflineStateImpl(
      connectivity: null == connectivity
          ? _value.connectivity
          : connectivity // ignore: cast_nullable_to_non_nullable
              as ConnectivityResult,
      isOnline: null == isOnline
          ? _value.isOnline
          : isOnline // ignore: cast_nullable_to_non_nullable
              as bool,
      isInitialized: null == isInitialized
          ? _value.isInitialized
          : isInitialized // ignore: cast_nullable_to_non_nullable
              as bool,
      lastConnectedAt: freezed == lastConnectedAt
          ? _value.lastConnectedAt
          : lastConnectedAt // ignore: cast_nullable_to_non_nullable
              as String?,
      showOfflineIndicator: null == showOfflineIndicator
          ? _value.showOfflineIndicator
          : showOfflineIndicator // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$OfflineStateImpl implements _OfflineState {
  const _$OfflineStateImpl(
      {this.connectivity = ConnectivityResult.none,
      this.isOnline = false,
      this.isInitialized = true,
      this.lastConnectedAt,
      this.showOfflineIndicator = false});

  @override
  @JsonKey()
  final ConnectivityResult connectivity;
  @override
  @JsonKey()
  final bool isOnline;
  @override
  @JsonKey()
  final bool isInitialized;
  @override
  final String? lastConnectedAt;
  @override
  @JsonKey()
  final bool showOfflineIndicator;

  @override
  String toString() {
    return 'OfflineState(connectivity: $connectivity, isOnline: $isOnline, isInitialized: $isInitialized, lastConnectedAt: $lastConnectedAt, showOfflineIndicator: $showOfflineIndicator)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OfflineStateImpl &&
            (identical(other.connectivity, connectivity) ||
                other.connectivity == connectivity) &&
            (identical(other.isOnline, isOnline) ||
                other.isOnline == isOnline) &&
            (identical(other.isInitialized, isInitialized) ||
                other.isInitialized == isInitialized) &&
            (identical(other.lastConnectedAt, lastConnectedAt) ||
                other.lastConnectedAt == lastConnectedAt) &&
            (identical(other.showOfflineIndicator, showOfflineIndicator) ||
                other.showOfflineIndicator == showOfflineIndicator));
  }

  @override
  int get hashCode => Object.hash(runtimeType, connectivity, isOnline,
      isInitialized, lastConnectedAt, showOfflineIndicator);

  /// Create a copy of OfflineState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OfflineStateImplCopyWith<_$OfflineStateImpl> get copyWith =>
      __$$OfflineStateImplCopyWithImpl<_$OfflineStateImpl>(this, _$identity);
}

abstract class _OfflineState implements OfflineState {
  const factory _OfflineState(
      {final ConnectivityResult connectivity,
      final bool isOnline,
      final bool isInitialized,
      final String? lastConnectedAt,
      final bool showOfflineIndicator}) = _$OfflineStateImpl;

  @override
  ConnectivityResult get connectivity;
  @override
  bool get isOnline;
  @override
  bool get isInitialized;
  @override
  String? get lastConnectedAt;
  @override
  bool get showOfflineIndicator;

  /// Create a copy of OfflineState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OfflineStateImplCopyWith<_$OfflineStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
