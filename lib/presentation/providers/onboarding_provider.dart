import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

/// Provider for managing onboarding state
final onboardingProvider =
    StateNotifierProvider<OnboardingNotifier, OnboardingState>((ref) {
      return OnboardingNotifier();
    });

/// Onboarding state notifier
class OnboardingNotifier extends StateNotifier<OnboardingState> {
  OnboardingNotifier() : super(const OnboardingState()) {
    _checkOnboardingStatus();
  }

  static const _storage = FlutterSecureStorage();
  static const _onboardingKey = 'has_completed_onboarding';
  static const _welcomeBannerKey = 'has_dismissed_welcome_banner';

  /// Check if user has completed onboarding
  Future<void> _checkOnboardingStatus() async {
    try {
      final hasCompleted = await _storage.read(key: _onboardingKey);
      final hasDismissedBanner = await _storage.read(key: _welcomeBannerKey);

      state = state.copyWith(
        hasCompletedOnboarding: hasCompleted == 'true',
        hasDismissedWelcomeBanner: hasDismissedBanner == 'true',
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        hasCompletedOnboarding: false,
        hasDismissedWelcomeBanner: false,
        isLoading: false,
      );
    }
  }

  /// Mark onboarding as completed
  Future<void> completeOnboarding() async {
    try {
      await _storage.write(key: _onboardingKey, value: 'true');
      state = state.copyWith(hasCompletedOnboarding: true);
    } catch (e) {
      // Handle error silently - onboarding will show again next time
    }
  }

  /// Dismiss welcome banner
  Future<void> dismissWelcomeBanner() async {
    try {
      await _storage.write(key: _welcomeBannerKey, value: 'true');
      state = state.copyWith(hasDismissedWelcomeBanner: true);
    } catch (e) {
      // Handle error silently
    }
  }

  /// Reset onboarding (for testing purposes)
  Future<void> resetOnboarding() async {
    try {
      await _storage.delete(key: _onboardingKey);
      await _storage.delete(key: _welcomeBannerKey);
      state = state.copyWith(
        hasCompletedOnboarding: false,
        hasDismissedWelcomeBanner: false,
      );
    } catch (e) {
      // Handle error silently
    }
  }

  /// Check if should show welcome banner
  bool shouldShowWelcomeBanner() {
    return state.hasCompletedOnboarding && !state.hasDismissedWelcomeBanner;
  }

  /// Check if should show onboarding flow
  bool shouldShowOnboarding() {
    return !state.hasCompletedOnboarding && !state.isLoading;
  }
}

/// Onboarding state
class OnboardingState {
  const OnboardingState({
    this.hasCompletedOnboarding = false,
    this.hasDismissedWelcomeBanner = false,
    this.isLoading = true,
  });

  final bool hasCompletedOnboarding;
  final bool hasDismissedWelcomeBanner;
  final bool isLoading;

  OnboardingState copyWith({
    bool? hasCompletedOnboarding,
    bool? hasDismissedWelcomeBanner,
    bool? isLoading,
  }) {
    return OnboardingState(
      hasCompletedOnboarding:
          hasCompletedOnboarding ?? this.hasCompletedOnboarding,
      hasDismissedWelcomeBanner:
          hasDismissedWelcomeBanner ?? this.hasDismissedWelcomeBanner,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}
