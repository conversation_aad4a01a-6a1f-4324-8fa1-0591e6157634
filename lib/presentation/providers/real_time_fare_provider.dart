import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';

import '../../domain/entities/ride.dart';
import '../../domain/repositories/ride_repository.dart';
import '../../core/di/service_locator.dart';
import 'booking_flow_notifier.dart';

/// Provider for real-time fare calculation
class RealTimeFareNotifier extends StateNotifier<AsyncValue<PricingInfo?>> {
  final RideRepository _rideRepository;
  final Ref _ref;
  Timer? _debounceTimer;
  
  RealTimeFareNotifier(this._rideRepository, this._ref) 
      : super(const AsyncValue.loading()) {
    _initialize();
  }
  
  void _initialize() {
    _calculateFare();
    _listenToLocationChanges();
  }
  
  void _listenToLocationChanges() {
    final bookingState = _ref.read(bookingFlowProvider);
    
    if (bookingState.pickupLocation != null && 
        bookingState.dropoffLocation != null) {
      _debouncedCalculateFare();
    }
  }
  
  void _debouncedCalculateFare() {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      _calculateFare();
    });
  }
  
  Future<void> _calculateFare() async {
    final bookingState = _ref.read(bookingFlowProvider);
    
    if (bookingState.pickupLocation == null || 
        bookingState.dropoffLocation == null) {
      state = const AsyncValue.data(null);
      return;
    }
    
    state = const AsyncValue.loading();
    
    try {
      final result = await _rideRepository.calculatePrice(
        bookingState.pickupLocation!,
        bookingState.dropoffLocation!,
      );
      
      result.fold(
        (error) => state = AsyncValue.error(error, StackTrace.current),
        (pricing) => state = AsyncValue.data(pricing),
      );
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
    }
  }
  
  Future<void> calculateFare() async {
    await _calculateFare();
  }
  
  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }
}

/// Provider for real-time fare calculation
final realTimeFareProvider = StateNotifierProvider<RealTimeFareNotifier, 
    AsyncValue<PricingInfo?>>((ref) {
  return RealTimeFareNotifier(
    getIt<RideRepository>(),
    ref,
  );
});

/// Provider for formatted fare display
final formattedFareProvider = Provider<String>((ref) {
  final fareState = ref.watch(realTimeFareProvider);
  
  return fareState.when(
    data: (pricing) {
      if (pricing == null) return 'Calculating...';
      return '\$${pricing.totalFare.toStringAsFixed(2)} USD';
    },
    loading: () => 'Calculating...',
    error: (_, __) => 'Unable to calculate fare',
  );
});

/// Provider for fare breakdown
final fareBreakdownProvider = Provider<Map<String, String>>((ref) {
  final fareState = ref.watch(realTimeFareProvider);
  
  return fareState.when(
    data: (pricing) {
      if (pricing == null) return {};
      
      return {
        'Base Fare': '\$${pricing.baseFare.toStringAsFixed(2)}',
        'Distance Fare': '\$${pricing.distanceFare.toStringAsFixed(2)}',
        'Additional Fees': '\$${pricing.additionalFees.toStringAsFixed(2)}',
        'Tax': '\$${pricing.tax.toStringAsFixed(2)}',
        'Total': '\$${pricing.totalFare.toStringAsFixed(2)}',
        'Estimated Distance': '${pricing.estimatedDistanceKm.toStringAsFixed(1)} km',
        'Estimated Duration': '${pricing.estimatedDurationMinutes} min',
      };
    },
    loading: () => {},
    error: (_, __) => {},
  );
});
