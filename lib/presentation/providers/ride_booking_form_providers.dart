import 'dart:math' as dart_math;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'ride_booking_notifier.dart';

/// Form validation providers for ride booking

/// Provider for pickup location validation
final pickupLocationValidationProvider = Provider<String?>((ref) {
  final bookingState = ref.watch(rideBookingNotifierProvider);
  final formState = ref.watch(rideBookingFormNotifierProvider);

  if (!formState.hasAttemptedSubmit && !bookingState.hasAttemptedSubmit) {
    return null;
  }

  if (bookingState.pickupLocation == null) {
    return 'Please select a pickup location';
  }

  if (!bookingState.pickupLocation!.isValidStLuciaLocation()) {
    return 'Pickup location must be within St. Lucia';
  }

  return null;
});

/// Provider for dropoff location validation
final dropoffLocationValidationProvider = Provider<String?>((ref) {
  final bookingState = ref.watch(rideBookingNotifierProvider);
  final formState = ref.watch(rideBookingFormNotifierProvider);

  if (!formState.hasAttemptedSubmit && !bookingState.hasAttemptedSubmit) {
    return null;
  }

  if (bookingState.dropoffLocation == null) {
    return 'Please select a dropoff location';
  }

  if (!bookingState.dropoffLocation!.isValidStLuciaLocation()) {
    return 'Dropoff location must be within St. Lucia';
  }

  // Check if pickup and dropoff are the same
  final pickup = bookingState.pickupLocation;
  final dropoff = bookingState.dropoffLocation!;

  if (pickup != null) {
    const double tolerance = 0.001; // ~111 meters
    final latDiff = (pickup.latitude - dropoff.latitude).abs();
    final lngDiff = (pickup.longitude - dropoff.longitude).abs();

    if (latDiff < tolerance && lngDiff < tolerance) {
      return 'Pickup and dropoff locations cannot be the same';
    }
  }

  return null;
});

/// Provider for special instructions validation
final specialInstructionsValidationProvider = Provider<String?>((ref) {
  final formState = ref.watch(rideBookingFormNotifierProvider);

  if (formState.specialInstructions.length > 500) {
    return 'Special instructions must be less than 500 characters';
  }

  return null;
});

/// Provider for pricing validation
final pricingValidationProvider = Provider<String?>((ref) {
  final bookingState = ref.watch(rideBookingNotifierProvider);

  if (!bookingState.hasAttemptedSubmit) {
    return null;
  }

  if (bookingState.hasValidLocations && bookingState.pricingInfo == null) {
    if (bookingState.isLoadingPricing) {
      return 'Calculating fare...';
    }
    return 'Unable to calculate fare. Please try again.';
  }

  return null;
});

/// Provider for overall form validation
final rideBookingFormValidationProvider = Provider<Map<String, String?>>((ref) {
  return {
    'pickup': ref.watch(pickupLocationValidationProvider),
    'dropoff': ref.watch(dropoffLocationValidationProvider),
    'specialInstructions': ref.watch(specialInstructionsValidationProvider),
    'pricing': ref.watch(pricingValidationProvider),
  };
});

/// Provider for checking if form is valid
final isRideBookingFormValidProvider = Provider<bool>((ref) {
  final validationErrors = ref.watch(rideBookingFormValidationProvider);
  return validationErrors.values.every((error) => error == null);
});

/// Provider for location search validation
final locationSearchValidationProvider = Provider.family<String?, String>((
  ref,
  query,
) {
  if (query.trim().isEmpty) {
    return 'Please enter a location';
  }

  if (query.trim().length < 2) {
    return 'Please enter at least 2 characters';
  }

  return null;
});

/// Provider for checking if locations are too close
final locationsDistanceValidationProvider = Provider<String?>((ref) {
  final bookingState = ref.watch(rideBookingNotifierProvider);

  if (bookingState.pickupLocation == null ||
      bookingState.dropoffLocation == null) {
    return null;
  }

  final pickup = bookingState.pickupLocation!;
  final dropoff = bookingState.dropoffLocation!;

  // Calculate distance using Haversine formula
  const double earthRadius = 6371; // Earth radius in kilometers
  const double minDistance = 0.5; // Minimum distance in kilometers

  final double latDiff = _toRadians(dropoff.latitude - pickup.latitude);
  final double lngDiff = _toRadians(dropoff.longitude - pickup.longitude);

  final double a =
      (latDiff / 2).sin() * (latDiff / 2).sin() +
      _toRadians(pickup.latitude).cos() *
          _toRadians(dropoff.latitude).cos() *
          (lngDiff / 2).sin() *
          (lngDiff / 2).sin();

  final double c = 2 * (a.sqrt()).asin();
  final double distance = earthRadius * c;

  if (distance < minDistance) {
    return 'Pickup and dropoff locations are too close. Minimum distance is ${minDistance}km.';
  }

  return null;
});

/// Helper function to convert degrees to radians
double _toRadians(double degrees) {
  return degrees * (3.14159265359 / 180);
}

/// Extension for sin, cos, asin, sqrt functions
extension MathExtensions on double {
  double sin() => dart_math.sin(this);
  double cos() => dart_math.cos(this);
  double asin() => dart_math.asin(this);
  double sqrt() => dart_math.sqrt(this);
}
