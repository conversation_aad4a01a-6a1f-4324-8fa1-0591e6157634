import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../core/di/service_locator.dart';
import '../../domain/repositories/ride_repository.dart';
import '../../domain/entities/ride.dart';
import '../../domain/entities/location_models.dart';
import '../../services/location/location_service.dart';
import 'ride_booking_state.dart';

/// StateNotifier for managing ride booking flow
class RideBookingNotifier extends StateNotifier<RideBookingState> {
  final RideRepository _rideRepository;

  RideBookingNotifier(this._rideRepository) : super(const RideBookingState());

  /// Set the pickup location
  Future<void> setPickupLocation(RideLocation location) async {
    state = state.copyWith(
      pickupLocation: location,
      errorMessage: null,
      fieldErrors: {...state.fieldErrors}..remove('pickup'),
    );

    // Trigger pricing calculation if both locations are set
    if (state.dropoffLocation != null) {
      await _calculatePricing();
    }
  }

  /// Set the dropoff location
  Future<void> setDropoffLocation(RideLocation location) async {
    state = state.copyWith(
      dropoffLocation: location,
      errorMessage: null,
      fieldErrors: {...state.fieldErrors}..remove('dropoff'),
    );

    // Trigger pricing calculation if both locations are set
    if (state.pickupLocation != null) {
      await _calculatePricing();
    }
  }

  /// Set special instructions
  void setSpecialInstructions(String instructions) {
    state = state.copyWith(
      specialInstructions: instructions,
      fieldErrors: {...state.fieldErrors}..remove('specialInstructions'),
    );
  }

  /// Calculate pricing for the current pickup and dropoff locations
  Future<void> _calculatePricing() async {
    if (state.pickupLocation == null || state.dropoffLocation == null) {
      return;
    }

    state = state.copyWith(isLoadingPricing: true, errorMessage: null);

    final result = await _rideRepository.calculatePrice(
      state.pickupLocation!,
      state.dropoffLocation!,
    );

    result.fold(
      (error) => state = state.copyWith(
        isLoadingPricing: false,
        errorMessage: error.message,
        pricingInfo: null,
      ),
      (pricing) => state = state.copyWith(
        isLoadingPricing: false,
        pricingInfo: pricing,
        errorMessage: null,
      ),
    );
  }

  /// Manually trigger pricing calculation
  Future<void> calculatePricing() async {
    await _calculatePricing();
  }

  /// Submit ride request
  Future<void> requestRide() async {
    if (!_validateRideRequest()) {
      return;
    }

    state = state.copyWith(
      isRequestingRide: true,
      errorMessage: null,
      hasAttemptedSubmit: true,
    );

    final request = RideRequestCreate(
      pickupLocation: state.pickupLocation!,
      dropoffLocation: state.dropoffLocation!,
      specialInstructions: state.specialInstructions.isEmpty
          ? null
          : state.specialInstructions,
    );

    final result = await _rideRepository.requestRide(request);

    result.fold(
      (error) => state = state.copyWith(
        isRequestingRide: false,
        errorMessage: error.message,
      ),
      (ride) => state = state.copyWith(
        isRequestingRide: false,
        currentRide: ride,
        errorMessage: null,
      ),
    );
  }

  /// Load active ride
  Future<void> loadActiveRide() async {
    state = state.copyWith(isLoadingActiveRide: true);

    final result = await _rideRepository.getActiveRide();

    result.fold(
      (error) => state = state.copyWith(
        isLoadingActiveRide: false,
        errorMessage: error.message,
      ),
      (ride) => state = state.copyWith(
        isLoadingActiveRide: false,
        currentRide: ride,
        errorMessage: null,
      ),
    );
  }

  /// Cancel current ride
  Future<void> cancelRide(CancellationReason reason) async {
    if (state.currentRide == null) return;

    final result = await _rideRepository.cancelRide(
      state.currentRide!.id,
      reason,
    );

    result.fold(
      (error) => state = state.copyWith(errorMessage: error.message),
      (_) => state = state.copyWith(currentRide: null),
    );
  }

  /// Clear current booking state
  void clearBooking() {
    state = const RideBookingState();
  }

  /// Clear errors
  void clearErrors() {
    state = state.copyWith(errorMessage: null, fieldErrors: {});
  }

  /// Validate ride request
  bool _validateRideRequest() {
    final errors = <String, String>{};

    if (state.pickupLocation == null) {
      errors['pickup'] = 'Please select a pickup location';
    }

    if (state.dropoffLocation == null) {
      errors['dropoff'] = 'Please select a dropoff location';
    }

    if (state.pricingInfo == null) {
      errors['pricing'] = 'Unable to calculate fare. Please try again.';
    }

    state = state.copyWith(fieldErrors: errors, hasAttemptedSubmit: true);

    return errors.isEmpty;
  }
}

/// StateNotifier for managing location state
class LocationNotifier extends StateNotifier<LocationState> {
  final LocationService _locationService;

  LocationNotifier(this._locationService) : super(const LocationState());

  /// Initialize location services
  Future<void> initialize() async {
    await _checkLocationPermission();
    await _loadRecentLocations();
  }

  /// Check location permission
  Future<void> _checkLocationPermission() async {
    final result = await _locationService.checkLocationPermission();
    result.fold(
      (error) => state = state.copyWith(errorMessage: error.message),
      (hasPermission) =>
          state = state.copyWith(hasLocationPermission: hasPermission),
    );
  }

  /// Request location permission
  Future<void> requestLocationPermission() async {
    state = state.copyWith(isRequestingPermission: true);

    final result = await _locationService.requestLocationPermission();

    result.fold(
      (error) => state = state.copyWith(
        isRequestingPermission: false,
        errorMessage: error.message,
      ),
      (granted) => state = state.copyWith(
        isRequestingPermission: false,
        hasLocationPermission: granted,
      ),
    );
  }

  /// Get current location
  Future<void> getCurrentLocation() async {
    state = state.copyWith(isLoadingCurrentLocation: true);

    final result = await _locationService.getCurrentLocation();

    result.fold(
      (error) => state = state.copyWith(
        isLoadingCurrentLocation: false,
        errorMessage: error.message,
      ),
      (location) => state = state.copyWith(
        isLoadingCurrentLocation: false,
        currentLocation: location,
      ),
    );
  }

  /// Search for location suggestions
  Future<void> searchLocations(String query) async {
    if (query.isEmpty) {
      state = state.copyWith(suggestions: [], searchQuery: '');
      return;
    }

    state = state.copyWith(isLoadingSuggestions: true, searchQuery: query);

    final result = await _locationService.getLocationSuggestions(query);

    result.fold(
      (error) => state = state.copyWith(
        isLoadingSuggestions: false,
        errorMessage: error.message,
      ),
      (suggestions) => state = state.copyWith(
        isLoadingSuggestions: false,
        suggestions: suggestions,
      ),
    );
  }

  /// Load recent locations
  Future<void> _loadRecentLocations() async {
    state = state.copyWith(isLoadingRecentLocations: true);

    final result = await _locationService.getRecentLocations();

    result.fold(
      (error) => state = state.copyWith(
        isLoadingRecentLocations: false,
        errorMessage: error.message,
      ),
      (locations) => state = state.copyWith(
        isLoadingRecentLocations: false,
        recentLocations: locations,
      ),
    );
  }

  /// Store a recent location
  Future<void> storeRecentLocation(RecentLocation location) async {
    await _locationService.storeRecentLocation(location);
    await _loadRecentLocations();
  }

  /// Clear search results
  void clearSearch() {
    state = state.copyWith(suggestions: [], searchQuery: '');
  }

  /// Clear errors
  void clearErrors() {
    state = state.copyWith(errorMessage: null, fieldErrors: {});
  }
}

/// StateNotifier for managing pricing state
class PricingNotifier extends StateNotifier<PricingState> {
  final RideRepository _rideRepository;

  PricingNotifier(this._rideRepository) : super(const PricingState());

  /// Calculate pricing for given locations
  Future<void> calculatePricing(
    RideLocation pickup,
    RideLocation dropoff,
  ) async {
    // Check if we already have valid pricing for these locations
    if (!state.needsRecalculation && state.locationsMatch(pickup, dropoff)) {
      return;
    }

    state = state.copyWith(
      isCalculating: true,
      pickupLocation: pickup,
      dropoffLocation: dropoff,
      errorMessage: null,
    );

    final result = await _rideRepository.calculatePrice(pickup, dropoff);

    result.fold(
      (error) => state = state.copyWith(
        isCalculating: false,
        errorMessage: error.message,
        pricingInfo: null,
      ),
      (pricing) => state = state.copyWith(
        isCalculating: false,
        pricingInfo: pricing,
        lastCalculated: DateTime.now(),
        errorMessage: null,
      ),
    );
  }

  /// Clear pricing information
  void clearPricing() {
    state = const PricingState();
  }
}

/// StateNotifier for managing form validation
class RideBookingFormNotifier extends StateNotifier<RideBookingFormState> {
  RideBookingFormNotifier() : super(const RideBookingFormState());

  /// Update pickup text
  void updatePickupText(String text) {
    state = state.copyWith(
      pickupText: text,
      fieldErrors: {...state.fieldErrors}..remove('pickup'),
    );
  }

  /// Update dropoff text
  void updateDropoffText(String text) {
    state = state.copyWith(
      dropoffText: text,
      fieldErrors: {...state.fieldErrors}..remove('dropoff'),
    );
  }

  /// Update special instructions
  void updateSpecialInstructions(String text) {
    state = state.copyWith(
      specialInstructions: text,
      fieldErrors: {...state.fieldErrors}..remove('specialInstructions'),
    );
  }

  /// Validate form
  bool validateForm() {
    state = state.copyWith(isValidating: true, hasAttemptedSubmit: true);

    final errors = <String, String>{};

    if (state.pickupText.trim().isEmpty) {
      errors['pickup'] = 'Pickup location is required';
    }

    if (state.dropoffText.trim().isEmpty) {
      errors['dropoff'] = 'Dropoff location is required';
    }

    if (state.specialInstructions.length > 500) {
      errors['specialInstructions'] =
          'Special instructions must be less than 500 characters';
    }

    state = state.copyWith(fieldErrors: errors, isValidating: false);

    return errors.isEmpty;
  }

  /// Clear form
  void clearForm() {
    state = const RideBookingFormState();
  }

  /// Clear errors
  void clearErrors() {
    state = state.copyWith(fieldErrors: {}, hasAttemptedSubmit: false);
  }
}

/// Providers for ride booking state management

/// Provider for RideBookingNotifier
final rideBookingNotifierProvider =
    StateNotifierProvider<RideBookingNotifier, RideBookingState>((ref) {
      return RideBookingNotifier(getIt<RideRepository>());
    });

/// Provider for LocationNotifier
final locationNotifierProvider =
    StateNotifierProvider<LocationNotifier, LocationState>((ref) {
      return LocationNotifier(getIt<LocationService>());
    });

/// Provider for PricingNotifier
final pricingNotifierProvider =
    StateNotifierProvider<PricingNotifier, PricingState>((ref) {
      return PricingNotifier(getIt<RideRepository>());
    });

/// Provider for RideBookingFormNotifier
final rideBookingFormNotifierProvider =
    StateNotifierProvider<RideBookingFormNotifier, RideBookingFormState>((ref) {
      return RideBookingFormNotifier();
    });

/// Computed providers for derived state

/// Provider for checking if ride can be requested
final canRequestRideProvider = Provider<bool>((ref) {
  final bookingState = ref.watch(rideBookingNotifierProvider);
  return bookingState.canSubmitRide;
});

/// Provider for checking if locations are valid
final hasValidLocationsProvider = Provider<bool>((ref) {
  final bookingState = ref.watch(rideBookingNotifierProvider);
  return bookingState.hasValidLocations;
});

/// Provider for checking if pricing is available
final hasPricingProvider = Provider<bool>((ref) {
  final bookingState = ref.watch(rideBookingNotifierProvider);
  return bookingState.hasPricing;
});

/// Provider for checking if any operation is loading
final isBookingLoadingProvider = Provider<bool>((ref) {
  final bookingState = ref.watch(rideBookingNotifierProvider);
  final locationState = ref.watch(locationNotifierProvider);
  final pricingState = ref.watch(pricingNotifierProvider);

  return bookingState.isLoading ||
      locationState.isLoading ||
      pricingState.isCalculating;
});

/// Provider for current ride
final currentRideProvider = Provider<Ride?>((ref) {
  final bookingState = ref.watch(rideBookingNotifierProvider);
  return bookingState.currentRide;
});

/// Provider for current pricing info
final currentPricingProvider = Provider<PricingInfo?>((ref) {
  final bookingState = ref.watch(rideBookingNotifierProvider);
  return bookingState.pricingInfo;
});
