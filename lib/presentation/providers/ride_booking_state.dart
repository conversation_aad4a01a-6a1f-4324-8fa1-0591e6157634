import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/ride.dart';
import '../../domain/entities/location_models.dart';

part 'ride_booking_state.freezed.dart';

/// State for the ride booking flow
@freezed
class RideBookingState with _$RideBookingState {
  const RideBookingState._();

  /// Creates a new ride booking state
  const factory RideBookingState({
    /// Selected pickup location
    RideLocation? pickupLocation,

    /// Selected dropoff location
    RideLocation? dropoffLocation,

    /// Calculated pricing information
    PricingInfo? pricingInfo,

    /// Special instructions for the driver
    @Default('') String specialInstructions,

    /// Current ride request (if any)
    Ride? currentRide,

    /// Loading state for various operations
    @Default(false) bool isLoadingPricing,
    @Default(false) bool isRequestingRide,
    @Default(false) bool isLoadingActiveRide,

    /// Error states
    String? errorMessage,
    @Default({}) Map<String, String> fieldErrors,

    /// Form validation state
    @Default(false) bool hasAttemptedSubmit,
  }) = _RideBookingState;

  /// Check if both pickup and dropoff locations are selected
  bool get hasValidLocations =>
      pickupLocation != null && dropoffLocation != null;

  /// Check if pricing is available
  bool get hasPricing => pricingInfo != null;

  /// Check if form can be submitted
  bool get canSubmitRide =>
      hasValidLocations &&
      hasPricing &&
      !isRequestingRide &&
      !isLoadingPricing &&
      errorMessage == null;

  /// Check if form has any errors
  bool get hasErrors => fieldErrors.isNotEmpty || errorMessage != null;

  /// Get error for specific field
  String? getFieldError(String field) => fieldErrors[field];

  /// Check if specific field has error
  bool hasFieldError(String field) => fieldErrors.containsKey(field);

  /// Check if any loading operation is in progress
  bool get isLoading =>
      isLoadingPricing || isRequestingRide || isLoadingActiveRide;
}

/// State for location selection and management
@freezed
class LocationState with _$LocationState {
  const LocationState._();

  /// Creates a new location state
  const factory LocationState({
    /// Current device location
    LocationCoordinates? currentLocation,

    /// Location suggestions for autocomplete
    @Default([]) List<LocationSuggestion> suggestions,

    /// Recent locations used by the user
    @Default([]) List<RecentLocation> recentLocations,

    /// Loading states
    @Default(false) bool isLoadingCurrentLocation,
    @Default(false) bool isLoadingSuggestions,
    @Default(false) bool isLoadingRecentLocations,

    /// Permission states
    @Default(false) bool hasLocationPermission,
    @Default(false) bool isRequestingPermission,

    /// Error states
    String? errorMessage,
    @Default({}) Map<String, String> fieldErrors,

    /// Search query for location suggestions
    @Default('') String searchQuery,
  }) = _LocationState;

  /// Check if location services are available
  bool get isLocationAvailable =>
      hasLocationPermission && currentLocation != null;

  /// Check if any loading operation is in progress
  bool get isLoading =>
      isLoadingCurrentLocation ||
      isLoadingSuggestions ||
      isLoadingRecentLocations ||
      isRequestingPermission;

  /// Check if form has any errors
  bool get hasErrors => fieldErrors.isNotEmpty || errorMessage != null;

  /// Get error for specific field
  String? getFieldError(String field) => fieldErrors[field];

  /// Check if specific field has error
  bool hasFieldError(String field) => fieldErrors.containsKey(field);
}

/// State for pricing calculations and display
@freezed
class PricingState with _$PricingState {
  const PricingState._();

  /// Creates a new pricing state
  const factory PricingState({
    /// Current pricing information
    PricingInfo? pricingInfo,

    /// Pickup location for pricing calculation
    RideLocation? pickupLocation,

    /// Dropoff location for pricing calculation
    RideLocation? dropoffLocation,

    /// Loading state for pricing calculation
    @Default(false) bool isCalculating,

    /// Error message for pricing calculation
    String? errorMessage,

    /// Timestamp of last pricing calculation
    DateTime? lastCalculated,
  }) = _PricingState;

  /// Check if pricing is available and valid
  bool get hasPricing => pricingInfo != null && !isCalculating;

  /// Check if pricing calculation is needed
  bool get needsRecalculation {
    if (pickupLocation == null || dropoffLocation == null) return false;
    if (pricingInfo == null) return true;
    if (lastCalculated == null) return true;

    // Recalculate if more than 5 minutes old
    final now = DateTime.now();
    final timeDiff = now.difference(lastCalculated!);
    return timeDiff.inMinutes > 5;
  }

  /// Check if locations match the current pricing
  bool locationsMatch(RideLocation? pickup, RideLocation? dropoff) {
    if (pickup == null || dropoff == null) return false;
    if (pickupLocation == null || dropoffLocation == null) return false;

    return _locationsEqual(pickup, pickupLocation!) &&
        _locationsEqual(dropoff, dropoffLocation!);
  }

  /// Helper method to compare two RideLocation objects
  bool _locationsEqual(RideLocation a, RideLocation b) {
    const double tolerance = 0.0001; // ~11 meters
    return (a.latitude - b.latitude).abs() < tolerance &&
        (a.longitude - b.longitude).abs() < tolerance;
  }
}

/// State for form validation in ride booking
@freezed
class RideBookingFormState with _$RideBookingFormState {
  const RideBookingFormState._();

  /// Creates a new ride booking form state
  const factory RideBookingFormState({
    /// Pickup location input text
    @Default('') String pickupText,

    /// Dropoff location input text
    @Default('') String dropoffText,

    /// Special instructions text
    @Default('') String specialInstructions,

    /// Form validation errors
    @Default({}) Map<String, String> fieldErrors,

    /// Whether form submission has been attempted
    @Default(false) bool hasAttemptedSubmit,

    /// Whether form is currently being validated
    @Default(false) bool isValidating,
  }) = _RideBookingFormState;

  /// Check if form has any errors
  bool get hasErrors => fieldErrors.isNotEmpty;

  /// Check if form is valid for submission
  bool get isValid =>
      !hasErrors && pickupText.isNotEmpty && dropoffText.isNotEmpty;

  /// Get error for specific field
  String? getFieldError(String field) => fieldErrors[field];

  /// Check if specific field has error
  bool hasFieldError(String field) => fieldErrors.containsKey(field);
}
