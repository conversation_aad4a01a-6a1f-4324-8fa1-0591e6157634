// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ride_booking_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$RideBookingState {
  /// Selected pickup location
  RideLocation? get pickupLocation => throw _privateConstructorUsedError;

  /// Selected dropoff location
  RideLocation? get dropoffLocation => throw _privateConstructorUsedError;

  /// Calculated pricing information
  PricingInfo? get pricingInfo => throw _privateConstructorUsedError;

  /// Special instructions for the driver
  String get specialInstructions => throw _privateConstructorUsedError;

  /// Current ride request (if any)
  Ride? get currentRide => throw _privateConstructorUsedError;

  /// Loading state for various operations
  bool get isLoadingPricing => throw _privateConstructorUsedError;
  bool get isRequestingRide => throw _privateConstructorUsedError;
  bool get isLoadingActiveRide => throw _privateConstructorUsedError;

  /// Error states
  String? get errorMessage => throw _privateConstructorUsedError;
  Map<String, String> get fieldErrors => throw _privateConstructorUsedError;

  /// Form validation state
  bool get hasAttemptedSubmit => throw _privateConstructorUsedError;

  /// Create a copy of RideBookingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RideBookingStateCopyWith<RideBookingState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RideBookingStateCopyWith<$Res> {
  factory $RideBookingStateCopyWith(
          RideBookingState value, $Res Function(RideBookingState) then) =
      _$RideBookingStateCopyWithImpl<$Res, RideBookingState>;
  @useResult
  $Res call(
      {RideLocation? pickupLocation,
      RideLocation? dropoffLocation,
      PricingInfo? pricingInfo,
      String specialInstructions,
      Ride? currentRide,
      bool isLoadingPricing,
      bool isRequestingRide,
      bool isLoadingActiveRide,
      String? errorMessage,
      Map<String, String> fieldErrors,
      bool hasAttemptedSubmit});

  $RideLocationCopyWith<$Res>? get pickupLocation;
  $RideLocationCopyWith<$Res>? get dropoffLocation;
  $PricingInfoCopyWith<$Res>? get pricingInfo;
  $RideCopyWith<$Res>? get currentRide;
}

/// @nodoc
class _$RideBookingStateCopyWithImpl<$Res, $Val extends RideBookingState>
    implements $RideBookingStateCopyWith<$Res> {
  _$RideBookingStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RideBookingState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pickupLocation = freezed,
    Object? dropoffLocation = freezed,
    Object? pricingInfo = freezed,
    Object? specialInstructions = null,
    Object? currentRide = freezed,
    Object? isLoadingPricing = null,
    Object? isRequestingRide = null,
    Object? isLoadingActiveRide = null,
    Object? errorMessage = freezed,
    Object? fieldErrors = null,
    Object? hasAttemptedSubmit = null,
  }) {
    return _then(_value.copyWith(
      pickupLocation: freezed == pickupLocation
          ? _value.pickupLocation
          : pickupLocation // ignore: cast_nullable_to_non_nullable
              as RideLocation?,
      dropoffLocation: freezed == dropoffLocation
          ? _value.dropoffLocation
          : dropoffLocation // ignore: cast_nullable_to_non_nullable
              as RideLocation?,
      pricingInfo: freezed == pricingInfo
          ? _value.pricingInfo
          : pricingInfo // ignore: cast_nullable_to_non_nullable
              as PricingInfo?,
      specialInstructions: null == specialInstructions
          ? _value.specialInstructions
          : specialInstructions // ignore: cast_nullable_to_non_nullable
              as String,
      currentRide: freezed == currentRide
          ? _value.currentRide
          : currentRide // ignore: cast_nullable_to_non_nullable
              as Ride?,
      isLoadingPricing: null == isLoadingPricing
          ? _value.isLoadingPricing
          : isLoadingPricing // ignore: cast_nullable_to_non_nullable
              as bool,
      isRequestingRide: null == isRequestingRide
          ? _value.isRequestingRide
          : isRequestingRide // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingActiveRide: null == isLoadingActiveRide
          ? _value.isLoadingActiveRide
          : isLoadingActiveRide // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      fieldErrors: null == fieldErrors
          ? _value.fieldErrors
          : fieldErrors // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      hasAttemptedSubmit: null == hasAttemptedSubmit
          ? _value.hasAttemptedSubmit
          : hasAttemptedSubmit // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }

  /// Create a copy of RideBookingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RideLocationCopyWith<$Res>? get pickupLocation {
    if (_value.pickupLocation == null) {
      return null;
    }

    return $RideLocationCopyWith<$Res>(_value.pickupLocation!, (value) {
      return _then(_value.copyWith(pickupLocation: value) as $Val);
    });
  }

  /// Create a copy of RideBookingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RideLocationCopyWith<$Res>? get dropoffLocation {
    if (_value.dropoffLocation == null) {
      return null;
    }

    return $RideLocationCopyWith<$Res>(_value.dropoffLocation!, (value) {
      return _then(_value.copyWith(dropoffLocation: value) as $Val);
    });
  }

  /// Create a copy of RideBookingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PricingInfoCopyWith<$Res>? get pricingInfo {
    if (_value.pricingInfo == null) {
      return null;
    }

    return $PricingInfoCopyWith<$Res>(_value.pricingInfo!, (value) {
      return _then(_value.copyWith(pricingInfo: value) as $Val);
    });
  }

  /// Create a copy of RideBookingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RideCopyWith<$Res>? get currentRide {
    if (_value.currentRide == null) {
      return null;
    }

    return $RideCopyWith<$Res>(_value.currentRide!, (value) {
      return _then(_value.copyWith(currentRide: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$RideBookingStateImplCopyWith<$Res>
    implements $RideBookingStateCopyWith<$Res> {
  factory _$$RideBookingStateImplCopyWith(_$RideBookingStateImpl value,
          $Res Function(_$RideBookingStateImpl) then) =
      __$$RideBookingStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {RideLocation? pickupLocation,
      RideLocation? dropoffLocation,
      PricingInfo? pricingInfo,
      String specialInstructions,
      Ride? currentRide,
      bool isLoadingPricing,
      bool isRequestingRide,
      bool isLoadingActiveRide,
      String? errorMessage,
      Map<String, String> fieldErrors,
      bool hasAttemptedSubmit});

  @override
  $RideLocationCopyWith<$Res>? get pickupLocation;
  @override
  $RideLocationCopyWith<$Res>? get dropoffLocation;
  @override
  $PricingInfoCopyWith<$Res>? get pricingInfo;
  @override
  $RideCopyWith<$Res>? get currentRide;
}

/// @nodoc
class __$$RideBookingStateImplCopyWithImpl<$Res>
    extends _$RideBookingStateCopyWithImpl<$Res, _$RideBookingStateImpl>
    implements _$$RideBookingStateImplCopyWith<$Res> {
  __$$RideBookingStateImplCopyWithImpl(_$RideBookingStateImpl _value,
      $Res Function(_$RideBookingStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of RideBookingState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pickupLocation = freezed,
    Object? dropoffLocation = freezed,
    Object? pricingInfo = freezed,
    Object? specialInstructions = null,
    Object? currentRide = freezed,
    Object? isLoadingPricing = null,
    Object? isRequestingRide = null,
    Object? isLoadingActiveRide = null,
    Object? errorMessage = freezed,
    Object? fieldErrors = null,
    Object? hasAttemptedSubmit = null,
  }) {
    return _then(_$RideBookingStateImpl(
      pickupLocation: freezed == pickupLocation
          ? _value.pickupLocation
          : pickupLocation // ignore: cast_nullable_to_non_nullable
              as RideLocation?,
      dropoffLocation: freezed == dropoffLocation
          ? _value.dropoffLocation
          : dropoffLocation // ignore: cast_nullable_to_non_nullable
              as RideLocation?,
      pricingInfo: freezed == pricingInfo
          ? _value.pricingInfo
          : pricingInfo // ignore: cast_nullable_to_non_nullable
              as PricingInfo?,
      specialInstructions: null == specialInstructions
          ? _value.specialInstructions
          : specialInstructions // ignore: cast_nullable_to_non_nullable
              as String,
      currentRide: freezed == currentRide
          ? _value.currentRide
          : currentRide // ignore: cast_nullable_to_non_nullable
              as Ride?,
      isLoadingPricing: null == isLoadingPricing
          ? _value.isLoadingPricing
          : isLoadingPricing // ignore: cast_nullable_to_non_nullable
              as bool,
      isRequestingRide: null == isRequestingRide
          ? _value.isRequestingRide
          : isRequestingRide // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingActiveRide: null == isLoadingActiveRide
          ? _value.isLoadingActiveRide
          : isLoadingActiveRide // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      fieldErrors: null == fieldErrors
          ? _value._fieldErrors
          : fieldErrors // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      hasAttemptedSubmit: null == hasAttemptedSubmit
          ? _value.hasAttemptedSubmit
          : hasAttemptedSubmit // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$RideBookingStateImpl extends _RideBookingState {
  const _$RideBookingStateImpl(
      {this.pickupLocation,
      this.dropoffLocation,
      this.pricingInfo,
      this.specialInstructions = '',
      this.currentRide,
      this.isLoadingPricing = false,
      this.isRequestingRide = false,
      this.isLoadingActiveRide = false,
      this.errorMessage,
      final Map<String, String> fieldErrors = const {},
      this.hasAttemptedSubmit = false})
      : _fieldErrors = fieldErrors,
        super._();

  /// Selected pickup location
  @override
  final RideLocation? pickupLocation;

  /// Selected dropoff location
  @override
  final RideLocation? dropoffLocation;

  /// Calculated pricing information
  @override
  final PricingInfo? pricingInfo;

  /// Special instructions for the driver
  @override
  @JsonKey()
  final String specialInstructions;

  /// Current ride request (if any)
  @override
  final Ride? currentRide;

  /// Loading state for various operations
  @override
  @JsonKey()
  final bool isLoadingPricing;
  @override
  @JsonKey()
  final bool isRequestingRide;
  @override
  @JsonKey()
  final bool isLoadingActiveRide;

  /// Error states
  @override
  final String? errorMessage;
  final Map<String, String> _fieldErrors;
  @override
  @JsonKey()
  Map<String, String> get fieldErrors {
    if (_fieldErrors is EqualUnmodifiableMapView) return _fieldErrors;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_fieldErrors);
  }

  /// Form validation state
  @override
  @JsonKey()
  final bool hasAttemptedSubmit;

  @override
  String toString() {
    return 'RideBookingState(pickupLocation: $pickupLocation, dropoffLocation: $dropoffLocation, pricingInfo: $pricingInfo, specialInstructions: $specialInstructions, currentRide: $currentRide, isLoadingPricing: $isLoadingPricing, isRequestingRide: $isRequestingRide, isLoadingActiveRide: $isLoadingActiveRide, errorMessage: $errorMessage, fieldErrors: $fieldErrors, hasAttemptedSubmit: $hasAttemptedSubmit)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RideBookingStateImpl &&
            (identical(other.pickupLocation, pickupLocation) ||
                other.pickupLocation == pickupLocation) &&
            (identical(other.dropoffLocation, dropoffLocation) ||
                other.dropoffLocation == dropoffLocation) &&
            (identical(other.pricingInfo, pricingInfo) ||
                other.pricingInfo == pricingInfo) &&
            (identical(other.specialInstructions, specialInstructions) ||
                other.specialInstructions == specialInstructions) &&
            (identical(other.currentRide, currentRide) ||
                other.currentRide == currentRide) &&
            (identical(other.isLoadingPricing, isLoadingPricing) ||
                other.isLoadingPricing == isLoadingPricing) &&
            (identical(other.isRequestingRide, isRequestingRide) ||
                other.isRequestingRide == isRequestingRide) &&
            (identical(other.isLoadingActiveRide, isLoadingActiveRide) ||
                other.isLoadingActiveRide == isLoadingActiveRide) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            const DeepCollectionEquality()
                .equals(other._fieldErrors, _fieldErrors) &&
            (identical(other.hasAttemptedSubmit, hasAttemptedSubmit) ||
                other.hasAttemptedSubmit == hasAttemptedSubmit));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      pickupLocation,
      dropoffLocation,
      pricingInfo,
      specialInstructions,
      currentRide,
      isLoadingPricing,
      isRequestingRide,
      isLoadingActiveRide,
      errorMessage,
      const DeepCollectionEquality().hash(_fieldErrors),
      hasAttemptedSubmit);

  /// Create a copy of RideBookingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RideBookingStateImplCopyWith<_$RideBookingStateImpl> get copyWith =>
      __$$RideBookingStateImplCopyWithImpl<_$RideBookingStateImpl>(
          this, _$identity);
}

abstract class _RideBookingState extends RideBookingState {
  const factory _RideBookingState(
      {final RideLocation? pickupLocation,
      final RideLocation? dropoffLocation,
      final PricingInfo? pricingInfo,
      final String specialInstructions,
      final Ride? currentRide,
      final bool isLoadingPricing,
      final bool isRequestingRide,
      final bool isLoadingActiveRide,
      final String? errorMessage,
      final Map<String, String> fieldErrors,
      final bool hasAttemptedSubmit}) = _$RideBookingStateImpl;
  const _RideBookingState._() : super._();

  /// Selected pickup location
  @override
  RideLocation? get pickupLocation;

  /// Selected dropoff location
  @override
  RideLocation? get dropoffLocation;

  /// Calculated pricing information
  @override
  PricingInfo? get pricingInfo;

  /// Special instructions for the driver
  @override
  String get specialInstructions;

  /// Current ride request (if any)
  @override
  Ride? get currentRide;

  /// Loading state for various operations
  @override
  bool get isLoadingPricing;
  @override
  bool get isRequestingRide;
  @override
  bool get isLoadingActiveRide;

  /// Error states
  @override
  String? get errorMessage;
  @override
  Map<String, String> get fieldErrors;

  /// Form validation state
  @override
  bool get hasAttemptedSubmit;

  /// Create a copy of RideBookingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RideBookingStateImplCopyWith<_$RideBookingStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$LocationState {
  /// Current device location
  LocationCoordinates? get currentLocation =>
      throw _privateConstructorUsedError;

  /// Location suggestions for autocomplete
  List<LocationSuggestion> get suggestions =>
      throw _privateConstructorUsedError;

  /// Recent locations used by the user
  List<RecentLocation> get recentLocations =>
      throw _privateConstructorUsedError;

  /// Loading states
  bool get isLoadingCurrentLocation => throw _privateConstructorUsedError;
  bool get isLoadingSuggestions => throw _privateConstructorUsedError;
  bool get isLoadingRecentLocations => throw _privateConstructorUsedError;

  /// Permission states
  bool get hasLocationPermission => throw _privateConstructorUsedError;
  bool get isRequestingPermission => throw _privateConstructorUsedError;

  /// Error states
  String? get errorMessage => throw _privateConstructorUsedError;
  Map<String, String> get fieldErrors => throw _privateConstructorUsedError;

  /// Search query for location suggestions
  String get searchQuery => throw _privateConstructorUsedError;

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LocationStateCopyWith<LocationState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocationStateCopyWith<$Res> {
  factory $LocationStateCopyWith(
          LocationState value, $Res Function(LocationState) then) =
      _$LocationStateCopyWithImpl<$Res, LocationState>;
  @useResult
  $Res call(
      {LocationCoordinates? currentLocation,
      List<LocationSuggestion> suggestions,
      List<RecentLocation> recentLocations,
      bool isLoadingCurrentLocation,
      bool isLoadingSuggestions,
      bool isLoadingRecentLocations,
      bool hasLocationPermission,
      bool isRequestingPermission,
      String? errorMessage,
      Map<String, String> fieldErrors,
      String searchQuery});

  $LocationCoordinatesCopyWith<$Res>? get currentLocation;
}

/// @nodoc
class _$LocationStateCopyWithImpl<$Res, $Val extends LocationState>
    implements $LocationStateCopyWith<$Res> {
  _$LocationStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentLocation = freezed,
    Object? suggestions = null,
    Object? recentLocations = null,
    Object? isLoadingCurrentLocation = null,
    Object? isLoadingSuggestions = null,
    Object? isLoadingRecentLocations = null,
    Object? hasLocationPermission = null,
    Object? isRequestingPermission = null,
    Object? errorMessage = freezed,
    Object? fieldErrors = null,
    Object? searchQuery = null,
  }) {
    return _then(_value.copyWith(
      currentLocation: freezed == currentLocation
          ? _value.currentLocation
          : currentLocation // ignore: cast_nullable_to_non_nullable
              as LocationCoordinates?,
      suggestions: null == suggestions
          ? _value.suggestions
          : suggestions // ignore: cast_nullable_to_non_nullable
              as List<LocationSuggestion>,
      recentLocations: null == recentLocations
          ? _value.recentLocations
          : recentLocations // ignore: cast_nullable_to_non_nullable
              as List<RecentLocation>,
      isLoadingCurrentLocation: null == isLoadingCurrentLocation
          ? _value.isLoadingCurrentLocation
          : isLoadingCurrentLocation // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingSuggestions: null == isLoadingSuggestions
          ? _value.isLoadingSuggestions
          : isLoadingSuggestions // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingRecentLocations: null == isLoadingRecentLocations
          ? _value.isLoadingRecentLocations
          : isLoadingRecentLocations // ignore: cast_nullable_to_non_nullable
              as bool,
      hasLocationPermission: null == hasLocationPermission
          ? _value.hasLocationPermission
          : hasLocationPermission // ignore: cast_nullable_to_non_nullable
              as bool,
      isRequestingPermission: null == isRequestingPermission
          ? _value.isRequestingPermission
          : isRequestingPermission // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      fieldErrors: null == fieldErrors
          ? _value.fieldErrors
          : fieldErrors // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      searchQuery: null == searchQuery
          ? _value.searchQuery
          : searchQuery // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LocationCoordinatesCopyWith<$Res>? get currentLocation {
    if (_value.currentLocation == null) {
      return null;
    }

    return $LocationCoordinatesCopyWith<$Res>(_value.currentLocation!, (value) {
      return _then(_value.copyWith(currentLocation: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$LocationStateImplCopyWith<$Res>
    implements $LocationStateCopyWith<$Res> {
  factory _$$LocationStateImplCopyWith(
          _$LocationStateImpl value, $Res Function(_$LocationStateImpl) then) =
      __$$LocationStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {LocationCoordinates? currentLocation,
      List<LocationSuggestion> suggestions,
      List<RecentLocation> recentLocations,
      bool isLoadingCurrentLocation,
      bool isLoadingSuggestions,
      bool isLoadingRecentLocations,
      bool hasLocationPermission,
      bool isRequestingPermission,
      String? errorMessage,
      Map<String, String> fieldErrors,
      String searchQuery});

  @override
  $LocationCoordinatesCopyWith<$Res>? get currentLocation;
}

/// @nodoc
class __$$LocationStateImplCopyWithImpl<$Res>
    extends _$LocationStateCopyWithImpl<$Res, _$LocationStateImpl>
    implements _$$LocationStateImplCopyWith<$Res> {
  __$$LocationStateImplCopyWithImpl(
      _$LocationStateImpl _value, $Res Function(_$LocationStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentLocation = freezed,
    Object? suggestions = null,
    Object? recentLocations = null,
    Object? isLoadingCurrentLocation = null,
    Object? isLoadingSuggestions = null,
    Object? isLoadingRecentLocations = null,
    Object? hasLocationPermission = null,
    Object? isRequestingPermission = null,
    Object? errorMessage = freezed,
    Object? fieldErrors = null,
    Object? searchQuery = null,
  }) {
    return _then(_$LocationStateImpl(
      currentLocation: freezed == currentLocation
          ? _value.currentLocation
          : currentLocation // ignore: cast_nullable_to_non_nullable
              as LocationCoordinates?,
      suggestions: null == suggestions
          ? _value._suggestions
          : suggestions // ignore: cast_nullable_to_non_nullable
              as List<LocationSuggestion>,
      recentLocations: null == recentLocations
          ? _value._recentLocations
          : recentLocations // ignore: cast_nullable_to_non_nullable
              as List<RecentLocation>,
      isLoadingCurrentLocation: null == isLoadingCurrentLocation
          ? _value.isLoadingCurrentLocation
          : isLoadingCurrentLocation // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingSuggestions: null == isLoadingSuggestions
          ? _value.isLoadingSuggestions
          : isLoadingSuggestions // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingRecentLocations: null == isLoadingRecentLocations
          ? _value.isLoadingRecentLocations
          : isLoadingRecentLocations // ignore: cast_nullable_to_non_nullable
              as bool,
      hasLocationPermission: null == hasLocationPermission
          ? _value.hasLocationPermission
          : hasLocationPermission // ignore: cast_nullable_to_non_nullable
              as bool,
      isRequestingPermission: null == isRequestingPermission
          ? _value.isRequestingPermission
          : isRequestingPermission // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      fieldErrors: null == fieldErrors
          ? _value._fieldErrors
          : fieldErrors // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      searchQuery: null == searchQuery
          ? _value.searchQuery
          : searchQuery // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$LocationStateImpl extends _LocationState {
  const _$LocationStateImpl(
      {this.currentLocation,
      final List<LocationSuggestion> suggestions = const [],
      final List<RecentLocation> recentLocations = const [],
      this.isLoadingCurrentLocation = false,
      this.isLoadingSuggestions = false,
      this.isLoadingRecentLocations = false,
      this.hasLocationPermission = false,
      this.isRequestingPermission = false,
      this.errorMessage,
      final Map<String, String> fieldErrors = const {},
      this.searchQuery = ''})
      : _suggestions = suggestions,
        _recentLocations = recentLocations,
        _fieldErrors = fieldErrors,
        super._();

  /// Current device location
  @override
  final LocationCoordinates? currentLocation;

  /// Location suggestions for autocomplete
  final List<LocationSuggestion> _suggestions;

  /// Location suggestions for autocomplete
  @override
  @JsonKey()
  List<LocationSuggestion> get suggestions {
    if (_suggestions is EqualUnmodifiableListView) return _suggestions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_suggestions);
  }

  /// Recent locations used by the user
  final List<RecentLocation> _recentLocations;

  /// Recent locations used by the user
  @override
  @JsonKey()
  List<RecentLocation> get recentLocations {
    if (_recentLocations is EqualUnmodifiableListView) return _recentLocations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_recentLocations);
  }

  /// Loading states
  @override
  @JsonKey()
  final bool isLoadingCurrentLocation;
  @override
  @JsonKey()
  final bool isLoadingSuggestions;
  @override
  @JsonKey()
  final bool isLoadingRecentLocations;

  /// Permission states
  @override
  @JsonKey()
  final bool hasLocationPermission;
  @override
  @JsonKey()
  final bool isRequestingPermission;

  /// Error states
  @override
  final String? errorMessage;
  final Map<String, String> _fieldErrors;
  @override
  @JsonKey()
  Map<String, String> get fieldErrors {
    if (_fieldErrors is EqualUnmodifiableMapView) return _fieldErrors;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_fieldErrors);
  }

  /// Search query for location suggestions
  @override
  @JsonKey()
  final String searchQuery;

  @override
  String toString() {
    return 'LocationState(currentLocation: $currentLocation, suggestions: $suggestions, recentLocations: $recentLocations, isLoadingCurrentLocation: $isLoadingCurrentLocation, isLoadingSuggestions: $isLoadingSuggestions, isLoadingRecentLocations: $isLoadingRecentLocations, hasLocationPermission: $hasLocationPermission, isRequestingPermission: $isRequestingPermission, errorMessage: $errorMessage, fieldErrors: $fieldErrors, searchQuery: $searchQuery)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocationStateImpl &&
            (identical(other.currentLocation, currentLocation) ||
                other.currentLocation == currentLocation) &&
            const DeepCollectionEquality()
                .equals(other._suggestions, _suggestions) &&
            const DeepCollectionEquality()
                .equals(other._recentLocations, _recentLocations) &&
            (identical(
                    other.isLoadingCurrentLocation, isLoadingCurrentLocation) ||
                other.isLoadingCurrentLocation == isLoadingCurrentLocation) &&
            (identical(other.isLoadingSuggestions, isLoadingSuggestions) ||
                other.isLoadingSuggestions == isLoadingSuggestions) &&
            (identical(
                    other.isLoadingRecentLocations, isLoadingRecentLocations) ||
                other.isLoadingRecentLocations == isLoadingRecentLocations) &&
            (identical(other.hasLocationPermission, hasLocationPermission) ||
                other.hasLocationPermission == hasLocationPermission) &&
            (identical(other.isRequestingPermission, isRequestingPermission) ||
                other.isRequestingPermission == isRequestingPermission) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            const DeepCollectionEquality()
                .equals(other._fieldErrors, _fieldErrors) &&
            (identical(other.searchQuery, searchQuery) ||
                other.searchQuery == searchQuery));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      currentLocation,
      const DeepCollectionEquality().hash(_suggestions),
      const DeepCollectionEquality().hash(_recentLocations),
      isLoadingCurrentLocation,
      isLoadingSuggestions,
      isLoadingRecentLocations,
      hasLocationPermission,
      isRequestingPermission,
      errorMessage,
      const DeepCollectionEquality().hash(_fieldErrors),
      searchQuery);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LocationStateImplCopyWith<_$LocationStateImpl> get copyWith =>
      __$$LocationStateImplCopyWithImpl<_$LocationStateImpl>(this, _$identity);
}

abstract class _LocationState extends LocationState {
  const factory _LocationState(
      {final LocationCoordinates? currentLocation,
      final List<LocationSuggestion> suggestions,
      final List<RecentLocation> recentLocations,
      final bool isLoadingCurrentLocation,
      final bool isLoadingSuggestions,
      final bool isLoadingRecentLocations,
      final bool hasLocationPermission,
      final bool isRequestingPermission,
      final String? errorMessage,
      final Map<String, String> fieldErrors,
      final String searchQuery}) = _$LocationStateImpl;
  const _LocationState._() : super._();

  /// Current device location
  @override
  LocationCoordinates? get currentLocation;

  /// Location suggestions for autocomplete
  @override
  List<LocationSuggestion> get suggestions;

  /// Recent locations used by the user
  @override
  List<RecentLocation> get recentLocations;

  /// Loading states
  @override
  bool get isLoadingCurrentLocation;
  @override
  bool get isLoadingSuggestions;
  @override
  bool get isLoadingRecentLocations;

  /// Permission states
  @override
  bool get hasLocationPermission;
  @override
  bool get isRequestingPermission;

  /// Error states
  @override
  String? get errorMessage;
  @override
  Map<String, String> get fieldErrors;

  /// Search query for location suggestions
  @override
  String get searchQuery;

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LocationStateImplCopyWith<_$LocationStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$PricingState {
  /// Current pricing information
  PricingInfo? get pricingInfo => throw _privateConstructorUsedError;

  /// Pickup location for pricing calculation
  RideLocation? get pickupLocation => throw _privateConstructorUsedError;

  /// Dropoff location for pricing calculation
  RideLocation? get dropoffLocation => throw _privateConstructorUsedError;

  /// Loading state for pricing calculation
  bool get isCalculating => throw _privateConstructorUsedError;

  /// Error message for pricing calculation
  String? get errorMessage => throw _privateConstructorUsedError;

  /// Timestamp of last pricing calculation
  DateTime? get lastCalculated => throw _privateConstructorUsedError;

  /// Create a copy of PricingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PricingStateCopyWith<PricingState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PricingStateCopyWith<$Res> {
  factory $PricingStateCopyWith(
          PricingState value, $Res Function(PricingState) then) =
      _$PricingStateCopyWithImpl<$Res, PricingState>;
  @useResult
  $Res call(
      {PricingInfo? pricingInfo,
      RideLocation? pickupLocation,
      RideLocation? dropoffLocation,
      bool isCalculating,
      String? errorMessage,
      DateTime? lastCalculated});

  $PricingInfoCopyWith<$Res>? get pricingInfo;
  $RideLocationCopyWith<$Res>? get pickupLocation;
  $RideLocationCopyWith<$Res>? get dropoffLocation;
}

/// @nodoc
class _$PricingStateCopyWithImpl<$Res, $Val extends PricingState>
    implements $PricingStateCopyWith<$Res> {
  _$PricingStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PricingState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pricingInfo = freezed,
    Object? pickupLocation = freezed,
    Object? dropoffLocation = freezed,
    Object? isCalculating = null,
    Object? errorMessage = freezed,
    Object? lastCalculated = freezed,
  }) {
    return _then(_value.copyWith(
      pricingInfo: freezed == pricingInfo
          ? _value.pricingInfo
          : pricingInfo // ignore: cast_nullable_to_non_nullable
              as PricingInfo?,
      pickupLocation: freezed == pickupLocation
          ? _value.pickupLocation
          : pickupLocation // ignore: cast_nullable_to_non_nullable
              as RideLocation?,
      dropoffLocation: freezed == dropoffLocation
          ? _value.dropoffLocation
          : dropoffLocation // ignore: cast_nullable_to_non_nullable
              as RideLocation?,
      isCalculating: null == isCalculating
          ? _value.isCalculating
          : isCalculating // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      lastCalculated: freezed == lastCalculated
          ? _value.lastCalculated
          : lastCalculated // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }

  /// Create a copy of PricingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PricingInfoCopyWith<$Res>? get pricingInfo {
    if (_value.pricingInfo == null) {
      return null;
    }

    return $PricingInfoCopyWith<$Res>(_value.pricingInfo!, (value) {
      return _then(_value.copyWith(pricingInfo: value) as $Val);
    });
  }

  /// Create a copy of PricingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RideLocationCopyWith<$Res>? get pickupLocation {
    if (_value.pickupLocation == null) {
      return null;
    }

    return $RideLocationCopyWith<$Res>(_value.pickupLocation!, (value) {
      return _then(_value.copyWith(pickupLocation: value) as $Val);
    });
  }

  /// Create a copy of PricingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RideLocationCopyWith<$Res>? get dropoffLocation {
    if (_value.dropoffLocation == null) {
      return null;
    }

    return $RideLocationCopyWith<$Res>(_value.dropoffLocation!, (value) {
      return _then(_value.copyWith(dropoffLocation: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$PricingStateImplCopyWith<$Res>
    implements $PricingStateCopyWith<$Res> {
  factory _$$PricingStateImplCopyWith(
          _$PricingStateImpl value, $Res Function(_$PricingStateImpl) then) =
      __$$PricingStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {PricingInfo? pricingInfo,
      RideLocation? pickupLocation,
      RideLocation? dropoffLocation,
      bool isCalculating,
      String? errorMessage,
      DateTime? lastCalculated});

  @override
  $PricingInfoCopyWith<$Res>? get pricingInfo;
  @override
  $RideLocationCopyWith<$Res>? get pickupLocation;
  @override
  $RideLocationCopyWith<$Res>? get dropoffLocation;
}

/// @nodoc
class __$$PricingStateImplCopyWithImpl<$Res>
    extends _$PricingStateCopyWithImpl<$Res, _$PricingStateImpl>
    implements _$$PricingStateImplCopyWith<$Res> {
  __$$PricingStateImplCopyWithImpl(
      _$PricingStateImpl _value, $Res Function(_$PricingStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of PricingState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pricingInfo = freezed,
    Object? pickupLocation = freezed,
    Object? dropoffLocation = freezed,
    Object? isCalculating = null,
    Object? errorMessage = freezed,
    Object? lastCalculated = freezed,
  }) {
    return _then(_$PricingStateImpl(
      pricingInfo: freezed == pricingInfo
          ? _value.pricingInfo
          : pricingInfo // ignore: cast_nullable_to_non_nullable
              as PricingInfo?,
      pickupLocation: freezed == pickupLocation
          ? _value.pickupLocation
          : pickupLocation // ignore: cast_nullable_to_non_nullable
              as RideLocation?,
      dropoffLocation: freezed == dropoffLocation
          ? _value.dropoffLocation
          : dropoffLocation // ignore: cast_nullable_to_non_nullable
              as RideLocation?,
      isCalculating: null == isCalculating
          ? _value.isCalculating
          : isCalculating // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      lastCalculated: freezed == lastCalculated
          ? _value.lastCalculated
          : lastCalculated // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc

class _$PricingStateImpl extends _PricingState {
  const _$PricingStateImpl(
      {this.pricingInfo,
      this.pickupLocation,
      this.dropoffLocation,
      this.isCalculating = false,
      this.errorMessage,
      this.lastCalculated})
      : super._();

  /// Current pricing information
  @override
  final PricingInfo? pricingInfo;

  /// Pickup location for pricing calculation
  @override
  final RideLocation? pickupLocation;

  /// Dropoff location for pricing calculation
  @override
  final RideLocation? dropoffLocation;

  /// Loading state for pricing calculation
  @override
  @JsonKey()
  final bool isCalculating;

  /// Error message for pricing calculation
  @override
  final String? errorMessage;

  /// Timestamp of last pricing calculation
  @override
  final DateTime? lastCalculated;

  @override
  String toString() {
    return 'PricingState(pricingInfo: $pricingInfo, pickupLocation: $pickupLocation, dropoffLocation: $dropoffLocation, isCalculating: $isCalculating, errorMessage: $errorMessage, lastCalculated: $lastCalculated)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PricingStateImpl &&
            (identical(other.pricingInfo, pricingInfo) ||
                other.pricingInfo == pricingInfo) &&
            (identical(other.pickupLocation, pickupLocation) ||
                other.pickupLocation == pickupLocation) &&
            (identical(other.dropoffLocation, dropoffLocation) ||
                other.dropoffLocation == dropoffLocation) &&
            (identical(other.isCalculating, isCalculating) ||
                other.isCalculating == isCalculating) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.lastCalculated, lastCalculated) ||
                other.lastCalculated == lastCalculated));
  }

  @override
  int get hashCode => Object.hash(runtimeType, pricingInfo, pickupLocation,
      dropoffLocation, isCalculating, errorMessage, lastCalculated);

  /// Create a copy of PricingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PricingStateImplCopyWith<_$PricingStateImpl> get copyWith =>
      __$$PricingStateImplCopyWithImpl<_$PricingStateImpl>(this, _$identity);
}

abstract class _PricingState extends PricingState {
  const factory _PricingState(
      {final PricingInfo? pricingInfo,
      final RideLocation? pickupLocation,
      final RideLocation? dropoffLocation,
      final bool isCalculating,
      final String? errorMessage,
      final DateTime? lastCalculated}) = _$PricingStateImpl;
  const _PricingState._() : super._();

  /// Current pricing information
  @override
  PricingInfo? get pricingInfo;

  /// Pickup location for pricing calculation
  @override
  RideLocation? get pickupLocation;

  /// Dropoff location for pricing calculation
  @override
  RideLocation? get dropoffLocation;

  /// Loading state for pricing calculation
  @override
  bool get isCalculating;

  /// Error message for pricing calculation
  @override
  String? get errorMessage;

  /// Timestamp of last pricing calculation
  @override
  DateTime? get lastCalculated;

  /// Create a copy of PricingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PricingStateImplCopyWith<_$PricingStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$RideBookingFormState {
  /// Pickup location input text
  String get pickupText => throw _privateConstructorUsedError;

  /// Dropoff location input text
  String get dropoffText => throw _privateConstructorUsedError;

  /// Special instructions text
  String get specialInstructions => throw _privateConstructorUsedError;

  /// Form validation errors
  Map<String, String> get fieldErrors => throw _privateConstructorUsedError;

  /// Whether form submission has been attempted
  bool get hasAttemptedSubmit => throw _privateConstructorUsedError;

  /// Whether form is currently being validated
  bool get isValidating => throw _privateConstructorUsedError;

  /// Create a copy of RideBookingFormState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RideBookingFormStateCopyWith<RideBookingFormState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RideBookingFormStateCopyWith<$Res> {
  factory $RideBookingFormStateCopyWith(RideBookingFormState value,
          $Res Function(RideBookingFormState) then) =
      _$RideBookingFormStateCopyWithImpl<$Res, RideBookingFormState>;
  @useResult
  $Res call(
      {String pickupText,
      String dropoffText,
      String specialInstructions,
      Map<String, String> fieldErrors,
      bool hasAttemptedSubmit,
      bool isValidating});
}

/// @nodoc
class _$RideBookingFormStateCopyWithImpl<$Res,
        $Val extends RideBookingFormState>
    implements $RideBookingFormStateCopyWith<$Res> {
  _$RideBookingFormStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RideBookingFormState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pickupText = null,
    Object? dropoffText = null,
    Object? specialInstructions = null,
    Object? fieldErrors = null,
    Object? hasAttemptedSubmit = null,
    Object? isValidating = null,
  }) {
    return _then(_value.copyWith(
      pickupText: null == pickupText
          ? _value.pickupText
          : pickupText // ignore: cast_nullable_to_non_nullable
              as String,
      dropoffText: null == dropoffText
          ? _value.dropoffText
          : dropoffText // ignore: cast_nullable_to_non_nullable
              as String,
      specialInstructions: null == specialInstructions
          ? _value.specialInstructions
          : specialInstructions // ignore: cast_nullable_to_non_nullable
              as String,
      fieldErrors: null == fieldErrors
          ? _value.fieldErrors
          : fieldErrors // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      hasAttemptedSubmit: null == hasAttemptedSubmit
          ? _value.hasAttemptedSubmit
          : hasAttemptedSubmit // ignore: cast_nullable_to_non_nullable
              as bool,
      isValidating: null == isValidating
          ? _value.isValidating
          : isValidating // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RideBookingFormStateImplCopyWith<$Res>
    implements $RideBookingFormStateCopyWith<$Res> {
  factory _$$RideBookingFormStateImplCopyWith(_$RideBookingFormStateImpl value,
          $Res Function(_$RideBookingFormStateImpl) then) =
      __$$RideBookingFormStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String pickupText,
      String dropoffText,
      String specialInstructions,
      Map<String, String> fieldErrors,
      bool hasAttemptedSubmit,
      bool isValidating});
}

/// @nodoc
class __$$RideBookingFormStateImplCopyWithImpl<$Res>
    extends _$RideBookingFormStateCopyWithImpl<$Res, _$RideBookingFormStateImpl>
    implements _$$RideBookingFormStateImplCopyWith<$Res> {
  __$$RideBookingFormStateImplCopyWithImpl(_$RideBookingFormStateImpl _value,
      $Res Function(_$RideBookingFormStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of RideBookingFormState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pickupText = null,
    Object? dropoffText = null,
    Object? specialInstructions = null,
    Object? fieldErrors = null,
    Object? hasAttemptedSubmit = null,
    Object? isValidating = null,
  }) {
    return _then(_$RideBookingFormStateImpl(
      pickupText: null == pickupText
          ? _value.pickupText
          : pickupText // ignore: cast_nullable_to_non_nullable
              as String,
      dropoffText: null == dropoffText
          ? _value.dropoffText
          : dropoffText // ignore: cast_nullable_to_non_nullable
              as String,
      specialInstructions: null == specialInstructions
          ? _value.specialInstructions
          : specialInstructions // ignore: cast_nullable_to_non_nullable
              as String,
      fieldErrors: null == fieldErrors
          ? _value._fieldErrors
          : fieldErrors // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      hasAttemptedSubmit: null == hasAttemptedSubmit
          ? _value.hasAttemptedSubmit
          : hasAttemptedSubmit // ignore: cast_nullable_to_non_nullable
              as bool,
      isValidating: null == isValidating
          ? _value.isValidating
          : isValidating // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$RideBookingFormStateImpl extends _RideBookingFormState {
  const _$RideBookingFormStateImpl(
      {this.pickupText = '',
      this.dropoffText = '',
      this.specialInstructions = '',
      final Map<String, String> fieldErrors = const {},
      this.hasAttemptedSubmit = false,
      this.isValidating = false})
      : _fieldErrors = fieldErrors,
        super._();

  /// Pickup location input text
  @override
  @JsonKey()
  final String pickupText;

  /// Dropoff location input text
  @override
  @JsonKey()
  final String dropoffText;

  /// Special instructions text
  @override
  @JsonKey()
  final String specialInstructions;

  /// Form validation errors
  final Map<String, String> _fieldErrors;

  /// Form validation errors
  @override
  @JsonKey()
  Map<String, String> get fieldErrors {
    if (_fieldErrors is EqualUnmodifiableMapView) return _fieldErrors;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_fieldErrors);
  }

  /// Whether form submission has been attempted
  @override
  @JsonKey()
  final bool hasAttemptedSubmit;

  /// Whether form is currently being validated
  @override
  @JsonKey()
  final bool isValidating;

  @override
  String toString() {
    return 'RideBookingFormState(pickupText: $pickupText, dropoffText: $dropoffText, specialInstructions: $specialInstructions, fieldErrors: $fieldErrors, hasAttemptedSubmit: $hasAttemptedSubmit, isValidating: $isValidating)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RideBookingFormStateImpl &&
            (identical(other.pickupText, pickupText) ||
                other.pickupText == pickupText) &&
            (identical(other.dropoffText, dropoffText) ||
                other.dropoffText == dropoffText) &&
            (identical(other.specialInstructions, specialInstructions) ||
                other.specialInstructions == specialInstructions) &&
            const DeepCollectionEquality()
                .equals(other._fieldErrors, _fieldErrors) &&
            (identical(other.hasAttemptedSubmit, hasAttemptedSubmit) ||
                other.hasAttemptedSubmit == hasAttemptedSubmit) &&
            (identical(other.isValidating, isValidating) ||
                other.isValidating == isValidating));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      pickupText,
      dropoffText,
      specialInstructions,
      const DeepCollectionEquality().hash(_fieldErrors),
      hasAttemptedSubmit,
      isValidating);

  /// Create a copy of RideBookingFormState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RideBookingFormStateImplCopyWith<_$RideBookingFormStateImpl>
      get copyWith =>
          __$$RideBookingFormStateImplCopyWithImpl<_$RideBookingFormStateImpl>(
              this, _$identity);
}

abstract class _RideBookingFormState extends RideBookingFormState {
  const factory _RideBookingFormState(
      {final String pickupText,
      final String dropoffText,
      final String specialInstructions,
      final Map<String, String> fieldErrors,
      final bool hasAttemptedSubmit,
      final bool isValidating}) = _$RideBookingFormStateImpl;
  const _RideBookingFormState._() : super._();

  /// Pickup location input text
  @override
  String get pickupText;

  /// Dropoff location input text
  @override
  String get dropoffText;

  /// Special instructions text
  @override
  String get specialInstructions;

  /// Form validation errors
  @override
  Map<String, String> get fieldErrors;

  /// Whether form submission has been attempted
  @override
  bool get hasAttemptedSubmit;

  /// Whether form is currently being validated
  @override
  bool get isValidating;

  /// Create a copy of RideBookingFormState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RideBookingFormStateImplCopyWith<_$RideBookingFormStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
