import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../core/di/service_locator.dart';
import '../../domain/repositories/ride_repository.dart';
import '../../domain/entities/ride.dart';
import 'ride_cancellation_state.dart';

/// StateNotifier for managing ride cancellation functionality
class RideCancellationNotifier extends StateNotifier<RideCancellationState> {
  final RideRepository _rideRepository;

  RideCancellationNotifier(this._rideRepository)
    : super(const RideCancellationState());

  /// Initialize cancellation flow for a specific ride
  Future<void> initializeCancellation(Ride ride) async {
    // Load cancellation policy based on ride status and timing
    final policy = _getCancellationPolicy(ride);

    // Calculate potential refund and fees
    final estimatedRefund = policy.calculateRefund(
      ride.fixedFare,
      ride.createdAt,
    );
    final cancellationFee = policy.calculateCancellationFee(
      ride.fixedFare,
      ride.createdAt,
    );

    state = state.copyWith(
      rideToCancel: ride,
      cancellationPolicy: policy,
      estimatedRefund: estimatedRefund,
      cancellationFee: cancellationFee,
      showReasonDialog: true,
      errorMessage: null,
      cancellationSuccessful: false,
    );
  }

  /// Get cancellation policy based on ride status and timing
  CancellationPolicy _getCancellationPolicy(Ride ride) {
    // Policy varies based on ride status
    switch (ride.status) {
      case RideStatus.requested:
        // Free cancellation for requested rides
        return const CancellationPolicy(
          freeCancellationAllowed: true,
          freeCancellationTimeLimit: null, // No time limit
          refundPercentage: 1.0, // Full refund
          policyText:
              'You can cancel this ride for free since no driver has been assigned yet.',
          additionalTerms: [
            'No cancellation fee applies',
            'Full refund will be processed immediately',
          ],
        );

      case RideStatus.accepted:
        // Time-based cancellation policy for accepted rides
        final timeSinceAccepted = DateTime.now().difference(
          ride.acceptedAt ?? ride.createdAt,
        );

        if (timeSinceAccepted.inMinutes <= 5) {
          // Free cancellation within 5 minutes of acceptance
          return const CancellationPolicy(
            freeCancellationAllowed: true,
            freeCancellationTimeLimit: 5,
            refundPercentage: 1.0,
            policyText:
                'Free cancellation is available within 5 minutes of driver acceptance.',
            additionalTerms: [
              'No cancellation fee applies within 5 minutes',
              'Full refund will be processed',
            ],
          );
        } else {
          // Cancellation fee applies after 5 minutes
          return const CancellationPolicy(
            freeCancellationAllowed: false,
            cancellationFee: 5.0, // $5 cancellation fee
            refundPercentage: 0.8, // 80% refund
            policyText:
                'A cancellation fee applies since the driver is already on the way.',
            additionalTerms: [
              'Cancellation fee: \$5.00',
              '80% of the fare will be refunded',
              'Refund will be processed within 3-5 business days',
            ],
          );
        }

      case RideStatus.inProgress:
        // No cancellation allowed for rides in progress
        return const CancellationPolicy(
          freeCancellationAllowed: false,
          refundPercentage: 0.0,
          policyText:
              'Rides in progress cannot be cancelled. Please contact support if you need assistance.',
          additionalTerms: [
            'No refund available for rides in progress',
            'Contact customer support for emergency situations',
          ],
        );

      default:
        // Default policy for other statuses
        return const CancellationPolicy(
          freeCancellationAllowed: false,
          refundPercentage: 0.0,
          policyText: 'This ride cannot be cancelled.',
        );
    }
  }

  /// Select a cancellation reason
  void selectReason(CancellationReason reason) {
    state = state.copyWith(
      selectedReason: reason,
      customReason: reason == CancellationReason.other
          ? state.customReason
          : null,
      errorMessage: null,
    );
  }

  /// Set custom reason text (for "other" reason)
  void setCustomReason(String customReason) {
    state = state.copyWith(
      customReason: customReason.trim(),
      errorMessage: null,
    );
  }

  /// Show reason selection dialog
  void showReasonSelection() {
    state = state.copyWith(
      showReasonDialog: true,
      showConfirmationDialog: false,
      errorMessage: null,
    );
  }

  /// Hide reason selection dialog
  void hideReasonSelection() {
    state = state.copyWith(showReasonDialog: false);
  }

  /// Show cancellation confirmation dialog
  void showConfirmation() {
    if (!state.hasSelectedReason || !state.isCustomReasonValid) {
      state = state.copyWith(
        errorMessage: 'Please select a cancellation reason',
      );
      return;
    }

    state = state.copyWith(
      showReasonDialog: false,
      showConfirmationDialog: true,
      errorMessage: null,
    );
  }

  /// Hide cancellation confirmation dialog
  void hideConfirmation() {
    state = state.copyWith(showConfirmationDialog: false);
  }

  /// Confirm understanding of cancellation policy
  void confirmPolicy(bool confirmed) {
    state = state.copyWith(policyConfirmed: confirmed, errorMessage: null);
  }

  /// Execute the ride cancellation
  Future<void> cancelRide() async {
    if (!state.canProceedWithCancellation) {
      state = state.copyWith(
        errorMessage: 'Please confirm you understand the cancellation policy',
      );
      return;
    }

    if (state.rideToCancel == null || state.selectedReason == null) {
      state = state.copyWith(errorMessage: 'Invalid cancellation request');
      return;
    }

    state = state.copyWith(isCancelling: true, errorMessage: null);

    final result = await _rideRepository.cancelRide(
      state.rideToCancel!.id,
      state.selectedReason!,
    );

    result.fold(
      (error) => state = state.copyWith(
        isCancelling: false,
        errorMessage: error.message,
      ),
      (_) => state = state.copyWith(
        isCancelling: false,
        cancellationSuccessful: true,
        showConfirmationDialog: false,
        errorMessage: null,
      ),
    );
  }

  /// Reset the cancellation state
  void resetCancellation() {
    state = const RideCancellationState();
  }

  /// Clear any error messages
  void clearError() {
    state = state.copyWith(errorMessage: null);
  }

  /// Validate the current cancellation form
  bool validateCancellation() {
    if (!state.hasSelectedReason) {
      state = state.copyWith(
        errorMessage: 'Please select a cancellation reason',
      );
      return false;
    }

    if (!state.isCustomReasonValid) {
      state = state.copyWith(
        errorMessage: 'Please provide a reason for cancellation',
      );
      return false;
    }

    if (!state.policyConfirmed) {
      state = state.copyWith(
        errorMessage: 'Please confirm you understand the cancellation policy',
      );
      return false;
    }

    state = state.copyWith(errorMessage: null);
    return true;
  }

  /// Get formatted refund information text
  String getRefundInfoText() {
    if (state.estimatedRefund == null) return 'No refund available';

    final refund = state.estimatedRefund!;
    final fee = state.cancellationFee ?? 0.0;
    final netRefund = state.netRefundAmount;

    if (fee > 0) {
      return 'Refund: \$${refund.toStringAsFixed(2)} - Fee: \$${fee.toStringAsFixed(2)} = \$${netRefund.toStringAsFixed(2)}';
    } else {
      return 'Full refund: \$${refund.toStringAsFixed(2)}';
    }
  }

  /// Check if the ride can be cancelled based on current status
  bool canCancelRide(Ride ride) {
    return ride.canBeCancelledByRider();
  }
}

/// Provider for RideCancellationNotifier
final rideCancellationNotifierProvider =
    StateNotifierProvider<RideCancellationNotifier, RideCancellationState>((
      ref,
    ) {
      return RideCancellationNotifier(getIt<RideRepository>());
    });

/// Provider for checking if cancellation dialog should be shown
final showCancellationDialogProvider = Provider<bool>((ref) {
  final cancellationState = ref.watch(rideCancellationNotifierProvider);
  return cancellationState.showReasonDialog ||
      cancellationState.showConfirmationDialog;
});

/// Provider for checking if cancellation is in progress
final isCancellingProvider = Provider<bool>((ref) {
  final cancellationState = ref.watch(rideCancellationNotifierProvider);
  return cancellationState.isCancelling;
});

/// Provider for cancellation success status
final cancellationSuccessfulProvider = Provider<bool>((ref) {
  final cancellationState = ref.watch(rideCancellationNotifierProvider);
  return cancellationState.cancellationSuccessful;
});

/// Provider for cancellation error message
final cancellationErrorProvider = Provider<String?>((ref) {
  final cancellationState = ref.watch(rideCancellationNotifierProvider);
  return cancellationState.errorMessage;
});

/// Provider for selected cancellation reason
final selectedCancellationReasonProvider = Provider<CancellationReason?>((ref) {
  final cancellationState = ref.watch(rideCancellationNotifierProvider);
  return cancellationState.selectedReason;
});

/// Provider for cancellation policy
final cancellationPolicyProvider = Provider<CancellationPolicy?>((ref) {
  final cancellationState = ref.watch(rideCancellationNotifierProvider);
  return cancellationState.cancellationPolicy;
});

/// Provider for refund information
final refundInfoProvider = Provider<String>((ref) {
  final notifier = ref.read(rideCancellationNotifierProvider.notifier);
  return notifier.getRefundInfoText();
});

/// Provider for checking if cancellation can proceed
final canProceedWithCancellationProvider = Provider<bool>((ref) {
  final cancellationState = ref.watch(rideCancellationNotifierProvider);
  return cancellationState.canProceedWithCancellation;
});
