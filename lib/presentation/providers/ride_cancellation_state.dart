import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/ride.dart';

part 'ride_cancellation_state.freezed.dart';

/// State for ride cancellation functionality
@freezed
class RideCancellationState with _$RideCancellationState {
  const RideCancellationState._();

  /// Creates a new ride cancellation state
  const factory RideCancellationState({
    /// The ride being cancelled
    Ride? rideToCancel,

    /// Selected cancellation reason
    CancellationReason? selectedReason,

    /// Custom reason text (when "other" is selected)
    String? customReason,

    /// Whether cancellation is in progress
    @Default(false) bool isCancelling,

    /// Whether to show cancellation confirmation dialog
    @Default(false) bool showConfirmationDialog,

    /// Whether to show reason selection dialog
    @Default(false) bool showReasonDialog,

    /// Whether cancellation was successful
    @Default(false) bool cancellationSuccessful,

    /// Error message if cancellation failed
    String? errorMessage,

    /// Cancellation policy information
    CancellationPolicy? cancellationPolicy,

    /// Whether the user has confirmed they understand the policy
    @Default(false) bool policyConfirmed,

    /// Estimated refund amount (if applicable)
    double? estimatedRefund,

    /// Processing fee for cancellation (if applicable)
    double? cancellationFee,
  }) = _RideCancellationState;

  /// Check if a reason is selected
  bool get hasSelectedReason => selectedReason != null;

  /// Check if custom reason is required and provided
  bool get isCustomReasonValid {
    if (selectedReason != CancellationReason.other) return true;
    return customReason != null && customReason!.trim().isNotEmpty;
  }

  /// Check if cancellation can proceed
  bool get canProceedWithCancellation {
    return hasSelectedReason &&
        isCustomReasonValid &&
        policyConfirmed &&
        !isCancelling;
  }

  /// Check if there are any errors
  bool get hasError => errorMessage != null;

  /// Get the display text for selected reason
  String get selectedReasonText {
    if (selectedReason == null) return '';

    switch (selectedReason!) {
      case CancellationReason.riderChangedMind:
        return 'Changed my mind';
      case CancellationReason.riderFoundAlternative:
        return 'Found alternative transportation';
      case CancellationReason.driverTakingTooLong:
        return 'Driver is taking too long';
      case CancellationReason.driverCancelled:
        return 'Driver cancelled';
      case CancellationReason.emergency:
        return 'Emergency situation';
      case CancellationReason.other:
        return customReason ?? 'Other';
    }
  }

  /// Get net refund amount after fees
  double get netRefundAmount {
    if (estimatedRefund == null) return 0.0;
    final fee = cancellationFee ?? 0.0;
    return (estimatedRefund! - fee).clamp(0.0, estimatedRefund!);
  }

  /// Check if there will be a cancellation fee
  bool get hasCancellationFee =>
      cancellationFee != null && cancellationFee! > 0;

  /// Check if there will be a refund
  bool get hasRefund => estimatedRefund != null && estimatedRefund! > 0;
}

/// Cancellation policy information
@freezed
class CancellationPolicy with _$CancellationPolicy {
  const CancellationPolicy._();

  /// Creates a new cancellation policy
  const factory CancellationPolicy({
    /// Whether free cancellation is allowed
    required bool freeCancellationAllowed,

    /// Time limit for free cancellation in minutes
    int? freeCancellationTimeLimit,

    /// Cancellation fee amount
    double? cancellationFee,

    /// Refund percentage (0.0 to 1.0)
    required double refundPercentage,

    /// Policy description text
    required String policyText,

    /// Additional terms and conditions
    List<String>? additionalTerms,
  }) = _CancellationPolicy;

  /// Check if cancellation is free based on ride timing
  bool isCancellationFree(DateTime rideCreatedAt) {
    if (!freeCancellationAllowed) return false;
    if (freeCancellationTimeLimit == null) return true;

    final now = DateTime.now();
    final timeSinceCreation = now.difference(rideCreatedAt);
    return timeSinceCreation.inMinutes <= freeCancellationTimeLimit!;
  }

  /// Calculate refund amount for a given fare
  double calculateRefund(double originalFare, DateTime rideCreatedAt) {
    if (isCancellationFree(rideCreatedAt)) {
      return originalFare; // Full refund
    }

    return originalFare * refundPercentage;
  }

  /// Calculate cancellation fee for a given fare
  double calculateCancellationFee(double originalFare, DateTime rideCreatedAt) {
    if (isCancellationFree(rideCreatedAt)) {
      return 0.0; // No fee for free cancellation
    }

    return cancellationFee ?? 0.0;
  }
}

/// Available cancellation reasons with display information
class CancellationReasonInfo {
  final CancellationReason reason;
  final String title;
  final String description;
  final bool requiresCustomText;

  const CancellationReasonInfo({
    required this.reason,
    required this.title,
    required this.description,
    this.requiresCustomText = false,
  });

  static const List<CancellationReasonInfo> availableReasons = [
    CancellationReasonInfo(
      reason: CancellationReason.riderChangedMind,
      title: 'Changed my mind',
      description: 'I no longer need this ride',
    ),
    CancellationReasonInfo(
      reason: CancellationReason.riderFoundAlternative,
      title: 'Found alternative transportation',
      description: 'I found another way to get to my destination',
    ),
    CancellationReasonInfo(
      reason: CancellationReason.driverTakingTooLong,
      title: 'Driver is taking too long',
      description: 'The driver is taking longer than expected to arrive',
    ),
    CancellationReasonInfo(
      reason: CancellationReason.emergency,
      title: 'Emergency situation',
      description: 'I have an emergency and cannot take this ride',
    ),
    CancellationReasonInfo(
      reason: CancellationReason.other,
      title: 'Other',
      description: 'Please specify your reason',
      requiresCustomText: true,
    ),
  ];

  static CancellationReasonInfo? getReasonInfo(CancellationReason reason) {
    try {
      return availableReasons.firstWhere((info) => info.reason == reason);
    } catch (e) {
      return null;
    }
  }
}
