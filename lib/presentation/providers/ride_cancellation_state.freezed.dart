// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ride_cancellation_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$RideCancellationState {
  /// The ride being cancelled
  Ride? get rideToCancel => throw _privateConstructorUsedError;

  /// Selected cancellation reason
  CancellationReason? get selectedReason => throw _privateConstructorUsedError;

  /// Custom reason text (when "other" is selected)
  String? get customReason => throw _privateConstructorUsedError;

  /// Whether cancellation is in progress
  bool get isCancelling => throw _privateConstructorUsedError;

  /// Whether to show cancellation confirmation dialog
  bool get showConfirmationDialog => throw _privateConstructorUsedError;

  /// Whether to show reason selection dialog
  bool get showReasonDialog => throw _privateConstructorUsedError;

  /// Whether cancellation was successful
  bool get cancellationSuccessful => throw _privateConstructorUsedError;

  /// Error message if cancellation failed
  String? get errorMessage => throw _privateConstructorUsedError;

  /// Cancellation policy information
  CancellationPolicy? get cancellationPolicy =>
      throw _privateConstructorUsedError;

  /// Whether the user has confirmed they understand the policy
  bool get policyConfirmed => throw _privateConstructorUsedError;

  /// Estimated refund amount (if applicable)
  double? get estimatedRefund => throw _privateConstructorUsedError;

  /// Processing fee for cancellation (if applicable)
  double? get cancellationFee => throw _privateConstructorUsedError;

  /// Create a copy of RideCancellationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RideCancellationStateCopyWith<RideCancellationState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RideCancellationStateCopyWith<$Res> {
  factory $RideCancellationStateCopyWith(RideCancellationState value,
          $Res Function(RideCancellationState) then) =
      _$RideCancellationStateCopyWithImpl<$Res, RideCancellationState>;
  @useResult
  $Res call(
      {Ride? rideToCancel,
      CancellationReason? selectedReason,
      String? customReason,
      bool isCancelling,
      bool showConfirmationDialog,
      bool showReasonDialog,
      bool cancellationSuccessful,
      String? errorMessage,
      CancellationPolicy? cancellationPolicy,
      bool policyConfirmed,
      double? estimatedRefund,
      double? cancellationFee});

  $RideCopyWith<$Res>? get rideToCancel;
  $CancellationPolicyCopyWith<$Res>? get cancellationPolicy;
}

/// @nodoc
class _$RideCancellationStateCopyWithImpl<$Res,
        $Val extends RideCancellationState>
    implements $RideCancellationStateCopyWith<$Res> {
  _$RideCancellationStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RideCancellationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? rideToCancel = freezed,
    Object? selectedReason = freezed,
    Object? customReason = freezed,
    Object? isCancelling = null,
    Object? showConfirmationDialog = null,
    Object? showReasonDialog = null,
    Object? cancellationSuccessful = null,
    Object? errorMessage = freezed,
    Object? cancellationPolicy = freezed,
    Object? policyConfirmed = null,
    Object? estimatedRefund = freezed,
    Object? cancellationFee = freezed,
  }) {
    return _then(_value.copyWith(
      rideToCancel: freezed == rideToCancel
          ? _value.rideToCancel
          : rideToCancel // ignore: cast_nullable_to_non_nullable
              as Ride?,
      selectedReason: freezed == selectedReason
          ? _value.selectedReason
          : selectedReason // ignore: cast_nullable_to_non_nullable
              as CancellationReason?,
      customReason: freezed == customReason
          ? _value.customReason
          : customReason // ignore: cast_nullable_to_non_nullable
              as String?,
      isCancelling: null == isCancelling
          ? _value.isCancelling
          : isCancelling // ignore: cast_nullable_to_non_nullable
              as bool,
      showConfirmationDialog: null == showConfirmationDialog
          ? _value.showConfirmationDialog
          : showConfirmationDialog // ignore: cast_nullable_to_non_nullable
              as bool,
      showReasonDialog: null == showReasonDialog
          ? _value.showReasonDialog
          : showReasonDialog // ignore: cast_nullable_to_non_nullable
              as bool,
      cancellationSuccessful: null == cancellationSuccessful
          ? _value.cancellationSuccessful
          : cancellationSuccessful // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      cancellationPolicy: freezed == cancellationPolicy
          ? _value.cancellationPolicy
          : cancellationPolicy // ignore: cast_nullable_to_non_nullable
              as CancellationPolicy?,
      policyConfirmed: null == policyConfirmed
          ? _value.policyConfirmed
          : policyConfirmed // ignore: cast_nullable_to_non_nullable
              as bool,
      estimatedRefund: freezed == estimatedRefund
          ? _value.estimatedRefund
          : estimatedRefund // ignore: cast_nullable_to_non_nullable
              as double?,
      cancellationFee: freezed == cancellationFee
          ? _value.cancellationFee
          : cancellationFee // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }

  /// Create a copy of RideCancellationState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RideCopyWith<$Res>? get rideToCancel {
    if (_value.rideToCancel == null) {
      return null;
    }

    return $RideCopyWith<$Res>(_value.rideToCancel!, (value) {
      return _then(_value.copyWith(rideToCancel: value) as $Val);
    });
  }

  /// Create a copy of RideCancellationState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CancellationPolicyCopyWith<$Res>? get cancellationPolicy {
    if (_value.cancellationPolicy == null) {
      return null;
    }

    return $CancellationPolicyCopyWith<$Res>(_value.cancellationPolicy!,
        (value) {
      return _then(_value.copyWith(cancellationPolicy: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$RideCancellationStateImplCopyWith<$Res>
    implements $RideCancellationStateCopyWith<$Res> {
  factory _$$RideCancellationStateImplCopyWith(
          _$RideCancellationStateImpl value,
          $Res Function(_$RideCancellationStateImpl) then) =
      __$$RideCancellationStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Ride? rideToCancel,
      CancellationReason? selectedReason,
      String? customReason,
      bool isCancelling,
      bool showConfirmationDialog,
      bool showReasonDialog,
      bool cancellationSuccessful,
      String? errorMessage,
      CancellationPolicy? cancellationPolicy,
      bool policyConfirmed,
      double? estimatedRefund,
      double? cancellationFee});

  @override
  $RideCopyWith<$Res>? get rideToCancel;
  @override
  $CancellationPolicyCopyWith<$Res>? get cancellationPolicy;
}

/// @nodoc
class __$$RideCancellationStateImplCopyWithImpl<$Res>
    extends _$RideCancellationStateCopyWithImpl<$Res,
        _$RideCancellationStateImpl>
    implements _$$RideCancellationStateImplCopyWith<$Res> {
  __$$RideCancellationStateImplCopyWithImpl(_$RideCancellationStateImpl _value,
      $Res Function(_$RideCancellationStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of RideCancellationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? rideToCancel = freezed,
    Object? selectedReason = freezed,
    Object? customReason = freezed,
    Object? isCancelling = null,
    Object? showConfirmationDialog = null,
    Object? showReasonDialog = null,
    Object? cancellationSuccessful = null,
    Object? errorMessage = freezed,
    Object? cancellationPolicy = freezed,
    Object? policyConfirmed = null,
    Object? estimatedRefund = freezed,
    Object? cancellationFee = freezed,
  }) {
    return _then(_$RideCancellationStateImpl(
      rideToCancel: freezed == rideToCancel
          ? _value.rideToCancel
          : rideToCancel // ignore: cast_nullable_to_non_nullable
              as Ride?,
      selectedReason: freezed == selectedReason
          ? _value.selectedReason
          : selectedReason // ignore: cast_nullable_to_non_nullable
              as CancellationReason?,
      customReason: freezed == customReason
          ? _value.customReason
          : customReason // ignore: cast_nullable_to_non_nullable
              as String?,
      isCancelling: null == isCancelling
          ? _value.isCancelling
          : isCancelling // ignore: cast_nullable_to_non_nullable
              as bool,
      showConfirmationDialog: null == showConfirmationDialog
          ? _value.showConfirmationDialog
          : showConfirmationDialog // ignore: cast_nullable_to_non_nullable
              as bool,
      showReasonDialog: null == showReasonDialog
          ? _value.showReasonDialog
          : showReasonDialog // ignore: cast_nullable_to_non_nullable
              as bool,
      cancellationSuccessful: null == cancellationSuccessful
          ? _value.cancellationSuccessful
          : cancellationSuccessful // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      cancellationPolicy: freezed == cancellationPolicy
          ? _value.cancellationPolicy
          : cancellationPolicy // ignore: cast_nullable_to_non_nullable
              as CancellationPolicy?,
      policyConfirmed: null == policyConfirmed
          ? _value.policyConfirmed
          : policyConfirmed // ignore: cast_nullable_to_non_nullable
              as bool,
      estimatedRefund: freezed == estimatedRefund
          ? _value.estimatedRefund
          : estimatedRefund // ignore: cast_nullable_to_non_nullable
              as double?,
      cancellationFee: freezed == cancellationFee
          ? _value.cancellationFee
          : cancellationFee // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc

class _$RideCancellationStateImpl extends _RideCancellationState {
  const _$RideCancellationStateImpl(
      {this.rideToCancel,
      this.selectedReason,
      this.customReason,
      this.isCancelling = false,
      this.showConfirmationDialog = false,
      this.showReasonDialog = false,
      this.cancellationSuccessful = false,
      this.errorMessage,
      this.cancellationPolicy,
      this.policyConfirmed = false,
      this.estimatedRefund,
      this.cancellationFee})
      : super._();

  /// The ride being cancelled
  @override
  final Ride? rideToCancel;

  /// Selected cancellation reason
  @override
  final CancellationReason? selectedReason;

  /// Custom reason text (when "other" is selected)
  @override
  final String? customReason;

  /// Whether cancellation is in progress
  @override
  @JsonKey()
  final bool isCancelling;

  /// Whether to show cancellation confirmation dialog
  @override
  @JsonKey()
  final bool showConfirmationDialog;

  /// Whether to show reason selection dialog
  @override
  @JsonKey()
  final bool showReasonDialog;

  /// Whether cancellation was successful
  @override
  @JsonKey()
  final bool cancellationSuccessful;

  /// Error message if cancellation failed
  @override
  final String? errorMessage;

  /// Cancellation policy information
  @override
  final CancellationPolicy? cancellationPolicy;

  /// Whether the user has confirmed they understand the policy
  @override
  @JsonKey()
  final bool policyConfirmed;

  /// Estimated refund amount (if applicable)
  @override
  final double? estimatedRefund;

  /// Processing fee for cancellation (if applicable)
  @override
  final double? cancellationFee;

  @override
  String toString() {
    return 'RideCancellationState(rideToCancel: $rideToCancel, selectedReason: $selectedReason, customReason: $customReason, isCancelling: $isCancelling, showConfirmationDialog: $showConfirmationDialog, showReasonDialog: $showReasonDialog, cancellationSuccessful: $cancellationSuccessful, errorMessage: $errorMessage, cancellationPolicy: $cancellationPolicy, policyConfirmed: $policyConfirmed, estimatedRefund: $estimatedRefund, cancellationFee: $cancellationFee)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RideCancellationStateImpl &&
            (identical(other.rideToCancel, rideToCancel) ||
                other.rideToCancel == rideToCancel) &&
            (identical(other.selectedReason, selectedReason) ||
                other.selectedReason == selectedReason) &&
            (identical(other.customReason, customReason) ||
                other.customReason == customReason) &&
            (identical(other.isCancelling, isCancelling) ||
                other.isCancelling == isCancelling) &&
            (identical(other.showConfirmationDialog, showConfirmationDialog) ||
                other.showConfirmationDialog == showConfirmationDialog) &&
            (identical(other.showReasonDialog, showReasonDialog) ||
                other.showReasonDialog == showReasonDialog) &&
            (identical(other.cancellationSuccessful, cancellationSuccessful) ||
                other.cancellationSuccessful == cancellationSuccessful) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.cancellationPolicy, cancellationPolicy) ||
                other.cancellationPolicy == cancellationPolicy) &&
            (identical(other.policyConfirmed, policyConfirmed) ||
                other.policyConfirmed == policyConfirmed) &&
            (identical(other.estimatedRefund, estimatedRefund) ||
                other.estimatedRefund == estimatedRefund) &&
            (identical(other.cancellationFee, cancellationFee) ||
                other.cancellationFee == cancellationFee));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      rideToCancel,
      selectedReason,
      customReason,
      isCancelling,
      showConfirmationDialog,
      showReasonDialog,
      cancellationSuccessful,
      errorMessage,
      cancellationPolicy,
      policyConfirmed,
      estimatedRefund,
      cancellationFee);

  /// Create a copy of RideCancellationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RideCancellationStateImplCopyWith<_$RideCancellationStateImpl>
      get copyWith => __$$RideCancellationStateImplCopyWithImpl<
          _$RideCancellationStateImpl>(this, _$identity);
}

abstract class _RideCancellationState extends RideCancellationState {
  const factory _RideCancellationState(
      {final Ride? rideToCancel,
      final CancellationReason? selectedReason,
      final String? customReason,
      final bool isCancelling,
      final bool showConfirmationDialog,
      final bool showReasonDialog,
      final bool cancellationSuccessful,
      final String? errorMessage,
      final CancellationPolicy? cancellationPolicy,
      final bool policyConfirmed,
      final double? estimatedRefund,
      final double? cancellationFee}) = _$RideCancellationStateImpl;
  const _RideCancellationState._() : super._();

  /// The ride being cancelled
  @override
  Ride? get rideToCancel;

  /// Selected cancellation reason
  @override
  CancellationReason? get selectedReason;

  /// Custom reason text (when "other" is selected)
  @override
  String? get customReason;

  /// Whether cancellation is in progress
  @override
  bool get isCancelling;

  /// Whether to show cancellation confirmation dialog
  @override
  bool get showConfirmationDialog;

  /// Whether to show reason selection dialog
  @override
  bool get showReasonDialog;

  /// Whether cancellation was successful
  @override
  bool get cancellationSuccessful;

  /// Error message if cancellation failed
  @override
  String? get errorMessage;

  /// Cancellation policy information
  @override
  CancellationPolicy? get cancellationPolicy;

  /// Whether the user has confirmed they understand the policy
  @override
  bool get policyConfirmed;

  /// Estimated refund amount (if applicable)
  @override
  double? get estimatedRefund;

  /// Processing fee for cancellation (if applicable)
  @override
  double? get cancellationFee;

  /// Create a copy of RideCancellationState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RideCancellationStateImplCopyWith<_$RideCancellationStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$CancellationPolicy {
  /// Whether free cancellation is allowed
  bool get freeCancellationAllowed => throw _privateConstructorUsedError;

  /// Time limit for free cancellation in minutes
  int? get freeCancellationTimeLimit => throw _privateConstructorUsedError;

  /// Cancellation fee amount
  double? get cancellationFee => throw _privateConstructorUsedError;

  /// Refund percentage (0.0 to 1.0)
  double get refundPercentage => throw _privateConstructorUsedError;

  /// Policy description text
  String get policyText => throw _privateConstructorUsedError;

  /// Additional terms and conditions
  List<String>? get additionalTerms => throw _privateConstructorUsedError;

  /// Create a copy of CancellationPolicy
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CancellationPolicyCopyWith<CancellationPolicy> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CancellationPolicyCopyWith<$Res> {
  factory $CancellationPolicyCopyWith(
          CancellationPolicy value, $Res Function(CancellationPolicy) then) =
      _$CancellationPolicyCopyWithImpl<$Res, CancellationPolicy>;
  @useResult
  $Res call(
      {bool freeCancellationAllowed,
      int? freeCancellationTimeLimit,
      double? cancellationFee,
      double refundPercentage,
      String policyText,
      List<String>? additionalTerms});
}

/// @nodoc
class _$CancellationPolicyCopyWithImpl<$Res, $Val extends CancellationPolicy>
    implements $CancellationPolicyCopyWith<$Res> {
  _$CancellationPolicyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CancellationPolicy
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? freeCancellationAllowed = null,
    Object? freeCancellationTimeLimit = freezed,
    Object? cancellationFee = freezed,
    Object? refundPercentage = null,
    Object? policyText = null,
    Object? additionalTerms = freezed,
  }) {
    return _then(_value.copyWith(
      freeCancellationAllowed: null == freeCancellationAllowed
          ? _value.freeCancellationAllowed
          : freeCancellationAllowed // ignore: cast_nullable_to_non_nullable
              as bool,
      freeCancellationTimeLimit: freezed == freeCancellationTimeLimit
          ? _value.freeCancellationTimeLimit
          : freeCancellationTimeLimit // ignore: cast_nullable_to_non_nullable
              as int?,
      cancellationFee: freezed == cancellationFee
          ? _value.cancellationFee
          : cancellationFee // ignore: cast_nullable_to_non_nullable
              as double?,
      refundPercentage: null == refundPercentage
          ? _value.refundPercentage
          : refundPercentage // ignore: cast_nullable_to_non_nullable
              as double,
      policyText: null == policyText
          ? _value.policyText
          : policyText // ignore: cast_nullable_to_non_nullable
              as String,
      additionalTerms: freezed == additionalTerms
          ? _value.additionalTerms
          : additionalTerms // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CancellationPolicyImplCopyWith<$Res>
    implements $CancellationPolicyCopyWith<$Res> {
  factory _$$CancellationPolicyImplCopyWith(_$CancellationPolicyImpl value,
          $Res Function(_$CancellationPolicyImpl) then) =
      __$$CancellationPolicyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool freeCancellationAllowed,
      int? freeCancellationTimeLimit,
      double? cancellationFee,
      double refundPercentage,
      String policyText,
      List<String>? additionalTerms});
}

/// @nodoc
class __$$CancellationPolicyImplCopyWithImpl<$Res>
    extends _$CancellationPolicyCopyWithImpl<$Res, _$CancellationPolicyImpl>
    implements _$$CancellationPolicyImplCopyWith<$Res> {
  __$$CancellationPolicyImplCopyWithImpl(_$CancellationPolicyImpl _value,
      $Res Function(_$CancellationPolicyImpl) _then)
      : super(_value, _then);

  /// Create a copy of CancellationPolicy
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? freeCancellationAllowed = null,
    Object? freeCancellationTimeLimit = freezed,
    Object? cancellationFee = freezed,
    Object? refundPercentage = null,
    Object? policyText = null,
    Object? additionalTerms = freezed,
  }) {
    return _then(_$CancellationPolicyImpl(
      freeCancellationAllowed: null == freeCancellationAllowed
          ? _value.freeCancellationAllowed
          : freeCancellationAllowed // ignore: cast_nullable_to_non_nullable
              as bool,
      freeCancellationTimeLimit: freezed == freeCancellationTimeLimit
          ? _value.freeCancellationTimeLimit
          : freeCancellationTimeLimit // ignore: cast_nullable_to_non_nullable
              as int?,
      cancellationFee: freezed == cancellationFee
          ? _value.cancellationFee
          : cancellationFee // ignore: cast_nullable_to_non_nullable
              as double?,
      refundPercentage: null == refundPercentage
          ? _value.refundPercentage
          : refundPercentage // ignore: cast_nullable_to_non_nullable
              as double,
      policyText: null == policyText
          ? _value.policyText
          : policyText // ignore: cast_nullable_to_non_nullable
              as String,
      additionalTerms: freezed == additionalTerms
          ? _value._additionalTerms
          : additionalTerms // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }
}

/// @nodoc

class _$CancellationPolicyImpl extends _CancellationPolicy {
  const _$CancellationPolicyImpl(
      {required this.freeCancellationAllowed,
      this.freeCancellationTimeLimit,
      this.cancellationFee,
      required this.refundPercentage,
      required this.policyText,
      final List<String>? additionalTerms})
      : _additionalTerms = additionalTerms,
        super._();

  /// Whether free cancellation is allowed
  @override
  final bool freeCancellationAllowed;

  /// Time limit for free cancellation in minutes
  @override
  final int? freeCancellationTimeLimit;

  /// Cancellation fee amount
  @override
  final double? cancellationFee;

  /// Refund percentage (0.0 to 1.0)
  @override
  final double refundPercentage;

  /// Policy description text
  @override
  final String policyText;

  /// Additional terms and conditions
  final List<String>? _additionalTerms;

  /// Additional terms and conditions
  @override
  List<String>? get additionalTerms {
    final value = _additionalTerms;
    if (value == null) return null;
    if (_additionalTerms is EqualUnmodifiableListView) return _additionalTerms;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'CancellationPolicy(freeCancellationAllowed: $freeCancellationAllowed, freeCancellationTimeLimit: $freeCancellationTimeLimit, cancellationFee: $cancellationFee, refundPercentage: $refundPercentage, policyText: $policyText, additionalTerms: $additionalTerms)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CancellationPolicyImpl &&
            (identical(
                    other.freeCancellationAllowed, freeCancellationAllowed) ||
                other.freeCancellationAllowed == freeCancellationAllowed) &&
            (identical(other.freeCancellationTimeLimit,
                    freeCancellationTimeLimit) ||
                other.freeCancellationTimeLimit == freeCancellationTimeLimit) &&
            (identical(other.cancellationFee, cancellationFee) ||
                other.cancellationFee == cancellationFee) &&
            (identical(other.refundPercentage, refundPercentage) ||
                other.refundPercentage == refundPercentage) &&
            (identical(other.policyText, policyText) ||
                other.policyText == policyText) &&
            const DeepCollectionEquality()
                .equals(other._additionalTerms, _additionalTerms));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      freeCancellationAllowed,
      freeCancellationTimeLimit,
      cancellationFee,
      refundPercentage,
      policyText,
      const DeepCollectionEquality().hash(_additionalTerms));

  /// Create a copy of CancellationPolicy
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CancellationPolicyImplCopyWith<_$CancellationPolicyImpl> get copyWith =>
      __$$CancellationPolicyImplCopyWithImpl<_$CancellationPolicyImpl>(
          this, _$identity);
}

abstract class _CancellationPolicy extends CancellationPolicy {
  const factory _CancellationPolicy(
      {required final bool freeCancellationAllowed,
      final int? freeCancellationTimeLimit,
      final double? cancellationFee,
      required final double refundPercentage,
      required final String policyText,
      final List<String>? additionalTerms}) = _$CancellationPolicyImpl;
  const _CancellationPolicy._() : super._();

  /// Whether free cancellation is allowed
  @override
  bool get freeCancellationAllowed;

  /// Time limit for free cancellation in minutes
  @override
  int? get freeCancellationTimeLimit;

  /// Cancellation fee amount
  @override
  double? get cancellationFee;

  /// Refund percentage (0.0 to 1.0)
  @override
  double get refundPercentage;

  /// Policy description text
  @override
  String get policyText;

  /// Additional terms and conditions
  @override
  List<String>? get additionalTerms;

  /// Create a copy of CancellationPolicy
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CancellationPolicyImplCopyWith<_$CancellationPolicyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
