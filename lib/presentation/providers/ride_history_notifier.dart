import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../core/di/service_locator.dart';
import '../../domain/repositories/ride_repository.dart';
import '../../domain/entities/ride.dart';
import 'ride_history_state.dart';

part 'ride_history_notifier.freezed.dart';

/// StateNotifier for managing ride history functionality
class RideHistoryNotifier extends StateNotifier<RideHistoryState> {
  final RideRepository _rideRepository;

  RideHistoryNotifier(this._rideRepository) : super(const RideHistoryState());

  /// Load initial ride history
  Future<void> loadRideHistory() async {
    if (state.isLoading) return;

    state = state.copyWith(
      isLoadingHistory: true,
      errorMessage: null,
      currentPage: 0,
    );

    final result = await _rideRepository.getRideHistory(
      limit: state.itemsPerPage,
      offset: 0,
    );

    result.fold(
      (error) => state = state.copyWith(
        isLoadingHistory: false,
        errorMessage: error.message,
      ),
      (rides) => state = state.copyWith(
        isLoadingHistory: false,
        rides: rides,
        hasMoreRides: rides.length >= state.itemsPerPage,
        lastUpdated: DateTime.now(),
        errorMessage: null,
      ),
    );
  }

  /// Load more rides (pagination)
  Future<void> loadMoreRides() async {
    if (state.isLoadingMore || !state.hasMoreRides) return;

    state = state.copyWith(isLoadingMore: true);

    final nextPage = state.currentPage + 1;
    final offset = nextPage * state.itemsPerPage;

    final result = await _rideRepository.getRideHistory(
      limit: state.itemsPerPage,
      offset: offset,
    );

    result.fold(
      (error) => state = state.copyWith(
        isLoadingMore: false,
        errorMessage: error.message,
      ),
      (newRides) {
        final allRides = [...state.rides, ...newRides];
        state = state.copyWith(
          isLoadingMore: false,
          rides: allRides,
          currentPage: nextPage,
          hasMoreRides: newRides.length >= state.itemsPerPage,
          lastUpdated: DateTime.now(),
          errorMessage: null,
        );
      },
    );
  }

  /// Refresh ride history
  Future<void> refreshRideHistory() async {
    if (state.isRefreshing) return;

    state = state.copyWith(isRefreshing: true, errorMessage: null);

    final result = await _rideRepository.getRideHistory(
      limit: state.itemsPerPage,
      offset: 0,
    );

    result.fold(
      (error) => state = state.copyWith(
        isRefreshing: false,
        errorMessage: error.message,
      ),
      (rides) => state = state.copyWith(
        isRefreshing: false,
        rides: rides,
        currentPage: 0,
        hasMoreRides: rides.length >= state.itemsPerPage,
        lastUpdated: DateTime.now(),
        errorMessage: null,
      ),
    );
  }

  /// Set search query and filter results
  void setSearchQuery(String query) {
    state = state.copyWith(searchQuery: query, errorMessage: null);
  }

  /// Set status filter
  void setStatusFilter(RideStatus? status) {
    state = state.copyWith(statusFilter: status, errorMessage: null);
  }

  /// Set date filter
  void setDateFilter(DateRange? dateRange) {
    state = state.copyWith(dateFilter: dateRange, errorMessage: null);
  }

  /// Apply multiple filters at once
  void applyFilters(RideHistoryFilters filters) {
    state = state.copyWith(
      searchQuery: filters.searchQuery,
      statusFilter: filters.statusFilter,
      dateFilter: filters.dateFilter,
      errorMessage: null,
    );
  }

  /// Clear all filters
  void clearFilters() {
    state = state.copyWith(
      searchQuery: '',
      statusFilter: null,
      dateFilter: null,
      errorMessage: null,
    );
  }

  /// Select a ride for details view
  void selectRide(String rideId) {
    state = state.copyWith(selectedRideId: rideId);
  }

  /// Clear selected ride
  void clearSelectedRide() {
    state = state.copyWith(selectedRideId: null);
  }

  /// Search rides with debouncing
  Future<void> searchRides(String query) async {
    setSearchQuery(query);

    // In a real implementation, you might want to add debouncing here
    // to avoid making too many API calls while the user is typing

    if (query.isNotEmpty) {
      // For now, we'll just filter the existing rides
      // In a full implementation, you might want to make a new API call
      // with the search query to get server-side filtered results
    }
  }

  /// Get ride by ID from current list
  RideHistory? getRideById(String rideId) {
    try {
      return state.rides.firstWhere((ride) => ride.id == rideId);
    } catch (_) {
      return null;
    }
  }

  /// Check if more rides can be loaded
  bool canLoadMore() {
    return state.hasMoreRides && !state.isLoading;
  }

  /// Get statistics from current rides
  RideHistoryStats getStats() {
    final rides = state.rides;

    if (rides.isEmpty) {
      return const RideHistoryStats(
        totalRides: 0,
        completedRides: 0,
        cancelledRides: 0,
        totalSpent: 0.0,
        averageFare: 0.0,
      );
    }

    final completedRides = rides
        .where((r) => r.status == RideStatus.completed)
        .length;
    final cancelledRides = rides
        .where((r) => r.status == RideStatus.cancelled)
        .length;
    final totalSpent = rides
        .where((r) => r.status == RideStatus.completed)
        .fold(0.0, (sum, ride) => sum + ride.fare);
    final averageFare = completedRides > 0 ? totalSpent / completedRides : 0.0;

    return RideHistoryStats(
      totalRides: rides.length,
      completedRides: completedRides,
      cancelledRides: cancelledRides,
      totalSpent: totalSpent,
      averageFare: averageFare,
    );
  }

  /// Clear errors
  void clearErrors() {
    state = state.copyWith(errorMessage: null, fieldErrors: {});
  }
}

/// StateNotifier for managing individual ride details
class RideDetailsNotifier extends StateNotifier<RideDetailsState> {
  final RideRepository _rideRepository;

  RideDetailsNotifier(this._rideRepository) : super(const RideDetailsState());

  /// Load full ride details
  Future<void> loadRideDetails(String rideId) async {
    if (state.isLoadingDetails) return;

    state = state.copyWith(isLoadingDetails: true, errorMessage: null);

    final result = await _rideRepository.getRideStatus(rideId);

    result.fold(
      (error) => state = state.copyWith(
        isLoadingDetails: false,
        errorMessage: error.message,
      ),
      (ride) => state = state.copyWith(
        isLoadingDetails: false,
        ride: ride,
        lastUpdated: DateTime.now(),
        errorMessage: null,
      ),
    );
  }

  /// Load ride receipt
  Future<void> loadRideReceipt(String rideId) async {
    if (state.isLoadingReceipt) return;

    state = state.copyWith(isLoadingReceipt: true, errorMessage: null);

    final result = await _rideRepository.getRideReceipt(rideId);

    result.fold(
      (error) => state = state.copyWith(
        isLoadingReceipt: false,
        errorMessage: error.message,
      ),
      (receipt) => state = state.copyWith(
        isLoadingReceipt: false,
        receipt: receipt,
        errorMessage: null,
      ),
    );
  }

  /// Load both ride details and receipt
  Future<void> loadFullRideInfo(String rideId) async {
    await Future.wait([loadRideDetails(rideId), loadRideReceipt(rideId)]);
  }

  /// Clear current ride details
  void clearRideDetails() {
    state = const RideDetailsState();
  }

  /// Clear errors
  void clearErrors() {
    state = state.copyWith(errorMessage: null);
  }
}

/// Statistics for ride history
@freezed
class RideHistoryStats with _$RideHistoryStats {
  const RideHistoryStats._();

  /// Creates new ride history statistics
  const factory RideHistoryStats({
    /// Total number of rides
    required int totalRides,

    /// Number of completed rides
    required int completedRides,

    /// Number of cancelled rides
    required int cancelledRides,

    /// Total amount spent on completed rides
    required double totalSpent,

    /// Average fare per completed ride
    required double averageFare,
  }) = _RideHistoryStats;

  /// Get completion rate as percentage
  double get completionRate {
    if (totalRides == 0) return 0.0;
    return (completedRides / totalRides) * 100;
  }

  /// Get cancellation rate as percentage
  double get cancellationRate {
    if (totalRides == 0) return 0.0;
    return (cancelledRides / totalRides) * 100;
  }

  /// Get formatted total spent
  String get formattedTotalSpent => '\$${totalSpent.toStringAsFixed(2)}';

  /// Get formatted average fare
  String get formattedAverageFare => '\$${averageFare.toStringAsFixed(2)}';

  /// Get formatted completion rate
  String get formattedCompletionRate => '${completionRate.toStringAsFixed(1)}%';

  /// Get formatted cancellation rate
  String get formattedCancellationRate =>
      '${cancellationRate.toStringAsFixed(1)}%';
}

/// Providers for ride history state management

/// Provider for RideHistoryNotifier
final rideHistoryNotifierProvider =
    StateNotifierProvider<RideHistoryNotifier, RideHistoryState>((ref) {
      return RideHistoryNotifier(getIt<RideRepository>());
    });

/// Provider for RideDetailsNotifier
final rideDetailsNotifierProvider =
    StateNotifierProvider<RideDetailsNotifier, RideDetailsState>((ref) {
      return RideDetailsNotifier(getIt<RideRepository>());
    });

/// Auto-dispose provider for specific ride details
final rideDetailsProvider = StateNotifierProvider.family
    .autoDispose<RideDetailsNotifier, RideDetailsState, String>((ref, rideId) {
      final notifier = RideDetailsNotifier(getIt<RideRepository>());

      // Automatically load ride details when provider is created
      Future.microtask(() => notifier.loadFullRideInfo(rideId));

      return notifier;
    });

/// Computed providers for derived state

/// Provider for filtered rides
final filteredRidesProvider = Provider<List<RideHistory>>((ref) {
  final historyState = ref.watch(rideHistoryNotifierProvider);
  return historyState.filteredRides;
});

/// Provider for ride history statistics
final rideHistoryStatsProvider = Provider<RideHistoryStats>((ref) {
  final notifier = ref.read(rideHistoryNotifierProvider.notifier);
  return notifier.getStats();
});

/// Provider for checking if more rides can be loaded
final canLoadMoreRidesProvider = Provider<bool>((ref) {
  final notifier = ref.read(rideHistoryNotifierProvider.notifier);
  return notifier.canLoadMore();
});

/// Provider for checking if any filters are active
final hasActiveFiltersProvider = Provider<bool>((ref) {
  final historyState = ref.watch(rideHistoryNotifierProvider);
  return historyState.hasActiveSearch;
});

/// Provider for getting a specific ride by ID
final rideByIdProvider = Provider.family<RideHistory?, String>((ref, rideId) {
  final notifier = ref.read(rideHistoryNotifierProvider.notifier);
  return notifier.getRideById(rideId);
});

/// Provider for current selected ride
final selectedRideProvider = Provider<RideHistory?>((ref) {
  final historyState = ref.watch(rideHistoryNotifierProvider);
  if (historyState.selectedRideId == null) return null;

  final notifier = ref.read(rideHistoryNotifierProvider.notifier);
  return notifier.getRideById(historyState.selectedRideId!);
});
