// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ride_history_notifier.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$RideHistoryStats {
  /// Total number of rides
  int get totalRides => throw _privateConstructorUsedError;

  /// Number of completed rides
  int get completedRides => throw _privateConstructorUsedError;

  /// Number of cancelled rides
  int get cancelledRides => throw _privateConstructorUsedError;

  /// Total amount spent on completed rides
  double get totalSpent => throw _privateConstructorUsedError;

  /// Average fare per completed ride
  double get averageFare => throw _privateConstructorUsedError;

  /// Create a copy of RideHistoryStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RideHistoryStatsCopyWith<RideHistoryStats> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RideHistoryStatsCopyWith<$Res> {
  factory $RideHistoryStatsCopyWith(
          RideHistoryStats value, $Res Function(RideHistoryStats) then) =
      _$RideHistoryStatsCopyWithImpl<$Res, RideHistoryStats>;
  @useResult
  $Res call(
      {int totalRides,
      int completedRides,
      int cancelledRides,
      double totalSpent,
      double averageFare});
}

/// @nodoc
class _$RideHistoryStatsCopyWithImpl<$Res, $Val extends RideHistoryStats>
    implements $RideHistoryStatsCopyWith<$Res> {
  _$RideHistoryStatsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RideHistoryStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalRides = null,
    Object? completedRides = null,
    Object? cancelledRides = null,
    Object? totalSpent = null,
    Object? averageFare = null,
  }) {
    return _then(_value.copyWith(
      totalRides: null == totalRides
          ? _value.totalRides
          : totalRides // ignore: cast_nullable_to_non_nullable
              as int,
      completedRides: null == completedRides
          ? _value.completedRides
          : completedRides // ignore: cast_nullable_to_non_nullable
              as int,
      cancelledRides: null == cancelledRides
          ? _value.cancelledRides
          : cancelledRides // ignore: cast_nullable_to_non_nullable
              as int,
      totalSpent: null == totalSpent
          ? _value.totalSpent
          : totalSpent // ignore: cast_nullable_to_non_nullable
              as double,
      averageFare: null == averageFare
          ? _value.averageFare
          : averageFare // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RideHistoryStatsImplCopyWith<$Res>
    implements $RideHistoryStatsCopyWith<$Res> {
  factory _$$RideHistoryStatsImplCopyWith(_$RideHistoryStatsImpl value,
          $Res Function(_$RideHistoryStatsImpl) then) =
      __$$RideHistoryStatsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int totalRides,
      int completedRides,
      int cancelledRides,
      double totalSpent,
      double averageFare});
}

/// @nodoc
class __$$RideHistoryStatsImplCopyWithImpl<$Res>
    extends _$RideHistoryStatsCopyWithImpl<$Res, _$RideHistoryStatsImpl>
    implements _$$RideHistoryStatsImplCopyWith<$Res> {
  __$$RideHistoryStatsImplCopyWithImpl(_$RideHistoryStatsImpl _value,
      $Res Function(_$RideHistoryStatsImpl) _then)
      : super(_value, _then);

  /// Create a copy of RideHistoryStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalRides = null,
    Object? completedRides = null,
    Object? cancelledRides = null,
    Object? totalSpent = null,
    Object? averageFare = null,
  }) {
    return _then(_$RideHistoryStatsImpl(
      totalRides: null == totalRides
          ? _value.totalRides
          : totalRides // ignore: cast_nullable_to_non_nullable
              as int,
      completedRides: null == completedRides
          ? _value.completedRides
          : completedRides // ignore: cast_nullable_to_non_nullable
              as int,
      cancelledRides: null == cancelledRides
          ? _value.cancelledRides
          : cancelledRides // ignore: cast_nullable_to_non_nullable
              as int,
      totalSpent: null == totalSpent
          ? _value.totalSpent
          : totalSpent // ignore: cast_nullable_to_non_nullable
              as double,
      averageFare: null == averageFare
          ? _value.averageFare
          : averageFare // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc

class _$RideHistoryStatsImpl extends _RideHistoryStats {
  const _$RideHistoryStatsImpl(
      {required this.totalRides,
      required this.completedRides,
      required this.cancelledRides,
      required this.totalSpent,
      required this.averageFare})
      : super._();

  /// Total number of rides
  @override
  final int totalRides;

  /// Number of completed rides
  @override
  final int completedRides;

  /// Number of cancelled rides
  @override
  final int cancelledRides;

  /// Total amount spent on completed rides
  @override
  final double totalSpent;

  /// Average fare per completed ride
  @override
  final double averageFare;

  @override
  String toString() {
    return 'RideHistoryStats(totalRides: $totalRides, completedRides: $completedRides, cancelledRides: $cancelledRides, totalSpent: $totalSpent, averageFare: $averageFare)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RideHistoryStatsImpl &&
            (identical(other.totalRides, totalRides) ||
                other.totalRides == totalRides) &&
            (identical(other.completedRides, completedRides) ||
                other.completedRides == completedRides) &&
            (identical(other.cancelledRides, cancelledRides) ||
                other.cancelledRides == cancelledRides) &&
            (identical(other.totalSpent, totalSpent) ||
                other.totalSpent == totalSpent) &&
            (identical(other.averageFare, averageFare) ||
                other.averageFare == averageFare));
  }

  @override
  int get hashCode => Object.hash(runtimeType, totalRides, completedRides,
      cancelledRides, totalSpent, averageFare);

  /// Create a copy of RideHistoryStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RideHistoryStatsImplCopyWith<_$RideHistoryStatsImpl> get copyWith =>
      __$$RideHistoryStatsImplCopyWithImpl<_$RideHistoryStatsImpl>(
          this, _$identity);
}

abstract class _RideHistoryStats extends RideHistoryStats {
  const factory _RideHistoryStats(
      {required final int totalRides,
      required final int completedRides,
      required final int cancelledRides,
      required final double totalSpent,
      required final double averageFare}) = _$RideHistoryStatsImpl;
  const _RideHistoryStats._() : super._();

  /// Total number of rides
  @override
  int get totalRides;

  /// Number of completed rides
  @override
  int get completedRides;

  /// Number of cancelled rides
  @override
  int get cancelledRides;

  /// Total amount spent on completed rides
  @override
  double get totalSpent;

  /// Average fare per completed ride
  @override
  double get averageFare;

  /// Create a copy of RideHistoryStats
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RideHistoryStatsImplCopyWith<_$RideHistoryStatsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
