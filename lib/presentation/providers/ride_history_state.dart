import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/ride.dart';

part 'ride_history_state.freezed.dart';

/// State for ride history functionality
@freezed
class RideHistoryState with _$RideHistoryState {
  const RideHistoryState._();

  /// Creates a new ride history state
  const factory RideHistoryState({
    /// List of ride history items
    @Default([]) List<RideHistory> rides,

    /// Current page number (0-based)
    @Default(0) int currentPage,

    /// Number of items per page
    @Default(20) int itemsPerPage,

    /// Total number of rides available
    int? totalCount,

    /// Whether there are more rides to load
    @Default(true) bool hasMoreRides,

    /// Loading states
    @Default(false) bool isLoadingHistory,
    @Default(false) bool isLoadingMore,
    @Default(false) bool isRefreshing,

    /// Search and filter states
    @Default('') String searchQuery,
    RideStatus? statusFilter,
    DateRange? dateFilter,

    /// Error states
    String? errorMessage,
    @Default({}) Map<String, String> fieldErrors,

    /// Last update timestamp
    DateTime? lastUpdated,

    /// Selected ride for details view
    String? selectedRideId,
  }) = _RideHistoryState;

  /// Check if any loading operation is in progress
  bool get isLoading => isLoadingHistory || isLoadingMore || isRefreshing;

  /// Check if form has any errors
  bool get hasErrors => fieldErrors.isNotEmpty || errorMessage != null;

  /// Get error for specific field
  String? getFieldError(String field) => fieldErrors[field];

  /// Check if specific field has error
  bool hasFieldError(String field) => fieldErrors.containsKey(field);

  /// Check if history is empty
  bool get isEmpty => rides.isEmpty && !isLoading;

  /// Check if search is active
  bool get hasActiveSearch =>
      searchQuery.isNotEmpty || statusFilter != null || dateFilter != null;

  /// Get filtered rides based on current filters
  List<RideHistory> get filteredRides {
    var filtered = rides;

    // Apply search query filter
    if (searchQuery.isNotEmpty) {
      final query = searchQuery.toLowerCase();
      filtered = filtered.where((ride) {
        return ride.pickupName.toLowerCase().contains(query) ||
            ride.dropoffName.toLowerCase().contains(query) ||
            (ride.driverName?.toLowerCase().contains(query) ?? false);
      }).toList();
    }

    // Apply status filter
    if (statusFilter != null) {
      filtered = filtered.where((ride) => ride.status == statusFilter).toList();
    }

    // Apply date filter
    if (dateFilter != null) {
      filtered = filtered.where((ride) {
        return ride.createdAt.isAfter(dateFilter!.startDate) &&
            ride.createdAt.isBefore(
              dateFilter!.endDate.add(const Duration(days: 1)),
            );
      }).toList();
    }

    return filtered;
  }

  /// Get total number of filtered rides
  int get filteredCount => filteredRides.length;

  /// Check if pagination is needed
  bool get needsPagination => totalCount != null && totalCount! > itemsPerPage;

  /// Get current page info for display
  String get pageInfo {
    if (totalCount == null || totalCount == 0) return 'No rides';

    final start = (currentPage * itemsPerPage) + 1;
    final end = ((currentPage + 1) * itemsPerPage).clamp(0, totalCount!);

    return 'Showing $start-$end of $totalCount rides';
  }
}

/// State for individual ride details
@freezed
class RideDetailsState with _$RideDetailsState {
  const RideDetailsState._();

  /// Creates a new ride details state
  const factory RideDetailsState({
    /// Full ride details
    Ride? ride,

    /// Ride receipt information
    RideReceipt? receipt,

    /// Loading states
    @Default(false) bool isLoadingDetails,
    @Default(false) bool isLoadingReceipt,

    /// Error states
    String? errorMessage,

    /// Last update timestamp
    DateTime? lastUpdated,
  }) = _RideDetailsState;

  /// Check if any loading operation is in progress
  bool get isLoading => isLoadingDetails || isLoadingReceipt;

  /// Check if ride details are available
  bool get hasRideDetails => ride != null;

  /// Check if receipt is available
  bool get hasReceipt => receipt != null;

  /// Check if ride can be rated (completed rides only)
  bool get canBeRated => ride?.status == RideStatus.completed;

  /// Get ride duration if available
  String? get formattedDuration {
    final duration = ride?.rideDurationMinutes();
    if (duration == null) return null;

    if (duration < 60) {
      return '$duration min';
    } else {
      final hours = duration ~/ 60;
      final minutes = duration % 60;
      return '${hours}h ${minutes}min';
    }
  }

  /// Get formatted fare amount
  String get formattedFare {
    if (ride == null) return '\$0.00';
    return '\$${ride!.fixedFare.toStringAsFixed(2)}';
  }
}

/// Date range for filtering rides
@freezed
class DateRange with _$DateRange {
  const DateRange._();

  /// Creates a new date range
  const factory DateRange({
    /// Start date (inclusive)
    required DateTime startDate,

    /// End date (inclusive)
    required DateTime endDate,

    /// Display label for the range
    String? label,
  }) = _DateRange;

  /// Check if date range is valid
  bool get isValid => !endDate.isBefore(startDate);

  /// Get duration of the date range in days
  int get durationInDays => endDate.difference(startDate).inDays + 1;

  /// Get formatted date range string
  String get formattedRange {
    if (label != null) return label!;

    final formatter = _getDateFormatter();
    return '${formatter.format(startDate)} - ${formatter.format(endDate)}';
  }

  /// Helper to get appropriate date formatter
  dynamic _getDateFormatter() {
    // This would typically use intl package's DateFormat
    // For now, returning a simple format
    return _SimpleDateFormatter();
  }
}

/// Simple date formatter helper
class _SimpleDateFormatter {
  String format(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// Predefined date ranges for quick filtering
class DateRanges {
  static DateRange get today => DateRange(
    startDate: DateTime.now(),
    endDate: DateTime.now(),
    label: 'Today',
  );

  static DateRange get yesterday => DateRange(
    startDate: DateTime.now().subtract(const Duration(days: 1)),
    endDate: DateTime.now().subtract(const Duration(days: 1)),
    label: 'Yesterday',
  );

  static DateRange get thisWeek => DateRange(
    startDate: DateTime.now().subtract(
      Duration(days: DateTime.now().weekday - 1),
    ),
    endDate: DateTime.now(),
    label: 'This Week',
  );

  static DateRange get lastWeek {
    final now = DateTime.now();
    final startOfThisWeek = now.subtract(Duration(days: now.weekday - 1));
    return DateRange(
      startDate: startOfThisWeek.subtract(const Duration(days: 7)),
      endDate: startOfThisWeek.subtract(const Duration(days: 1)),
      label: 'Last Week',
    );
  }

  static DateRange get thisMonth => DateRange(
    startDate: DateTime(DateTime.now().year, DateTime.now().month, 1),
    endDate: DateTime.now(),
    label: 'This Month',
  );

  static DateRange get lastMonth {
    final now = DateTime.now();
    final lastMonth = DateTime(now.year, now.month - 1, 1);
    final endOfLastMonth = DateTime(now.year, now.month, 0);
    return DateRange(
      startDate: lastMonth,
      endDate: endOfLastMonth,
      label: 'Last Month',
    );
  }

  static DateRange get last30Days => DateRange(
    startDate: DateTime.now().subtract(const Duration(days: 30)),
    endDate: DateTime.now(),
    label: 'Last 30 Days',
  );

  static List<DateRange> get predefinedRanges => [
    today,
    yesterday,
    thisWeek,
    lastWeek,
    thisMonth,
    lastMonth,
    last30Days,
  ];
}

/// Search filters for ride history
@freezed
class RideHistoryFilters with _$RideHistoryFilters {
  const RideHistoryFilters._();

  /// Creates new ride history filters
  const factory RideHistoryFilters({
    /// Search query for locations or driver names
    @Default('') String searchQuery,

    /// Filter by ride status
    RideStatus? statusFilter,

    /// Filter by date range
    DateRange? dateFilter,

    /// Sort order for results
    @Default(RideHistorySortOrder.dateDescending)
    RideHistorySortOrder sortOrder,

    /// Minimum fare filter
    double? minFare,

    /// Maximum fare filter
    double? maxFare,
  }) = _RideHistoryFilters;

  /// Check if any filters are active
  bool get hasActiveFilters =>
      searchQuery.isNotEmpty ||
      statusFilter != null ||
      dateFilter != null ||
      minFare != null ||
      maxFare != null;

  /// Get count of active filters
  int get activeFilterCount {
    int count = 0;
    if (searchQuery.isNotEmpty) count++;
    if (statusFilter != null) count++;
    if (dateFilter != null) count++;
    if (minFare != null) count++;
    if (maxFare != null) count++;
    return count;
  }

  /// Clear all filters
  RideHistoryFilters clearAll() => const RideHistoryFilters();

  /// Apply filters to a list of rides
  List<RideHistory> applyTo(List<RideHistory> rides) {
    var filtered = rides;

    // Apply search query
    if (searchQuery.isNotEmpty) {
      final query = searchQuery.toLowerCase();
      filtered = filtered.where((ride) {
        return ride.pickupName.toLowerCase().contains(query) ||
            ride.dropoffName.toLowerCase().contains(query) ||
            (ride.driverName?.toLowerCase().contains(query) ?? false);
      }).toList();
    }

    // Apply status filter
    if (statusFilter != null) {
      filtered = filtered.where((ride) => ride.status == statusFilter).toList();
    }

    // Apply date filter
    if (dateFilter != null) {
      filtered = filtered.where((ride) {
        return ride.createdAt.isAfter(dateFilter!.startDate) &&
            ride.createdAt.isBefore(
              dateFilter!.endDate.add(const Duration(days: 1)),
            );
      }).toList();
    }

    // Apply fare filters
    if (minFare != null) {
      filtered = filtered.where((ride) => ride.fare >= minFare!).toList();
    }

    if (maxFare != null) {
      filtered = filtered.where((ride) => ride.fare <= maxFare!).toList();
    }

    // Apply sorting
    switch (sortOrder) {
      case RideHistorySortOrder.dateAscending:
        filtered.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
      case RideHistorySortOrder.dateDescending:
        filtered.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case RideHistorySortOrder.fareAscending:
        filtered.sort((a, b) => a.fare.compareTo(b.fare));
        break;
      case RideHistorySortOrder.fareDescending:
        filtered.sort((a, b) => b.fare.compareTo(a.fare));
        break;
    }

    return filtered;
  }
}

/// Sort order options for ride history
enum RideHistorySortOrder {
  /// Sort by date, newest first
  dateDescending,

  /// Sort by date, oldest first
  dateAscending,

  /// Sort by fare, lowest first
  fareAscending,

  /// Sort by fare, highest first
  fareDescending,
}

/// Extension to get display names for sort orders
extension RideHistorySortOrderExtension on RideHistorySortOrder {
  String get displayName {
    switch (this) {
      case RideHistorySortOrder.dateDescending:
        return 'Newest First';
      case RideHistorySortOrder.dateAscending:
        return 'Oldest First';
      case RideHistorySortOrder.fareAscending:
        return 'Lowest Fare';
      case RideHistorySortOrder.fareDescending:
        return 'Highest Fare';
    }
  }
}
