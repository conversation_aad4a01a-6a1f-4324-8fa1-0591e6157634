// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ride_history_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$RideHistoryState {
  /// List of ride history items
  List<RideHistory> get rides => throw _privateConstructorUsedError;

  /// Current page number (0-based)
  int get currentPage => throw _privateConstructorUsedError;

  /// Number of items per page
  int get itemsPerPage => throw _privateConstructorUsedError;

  /// Total number of rides available
  int? get totalCount => throw _privateConstructorUsedError;

  /// Whether there are more rides to load
  bool get hasMoreRides => throw _privateConstructorUsedError;

  /// Loading states
  bool get isLoadingHistory => throw _privateConstructorUsedError;
  bool get isLoadingMore => throw _privateConstructorUsedError;
  bool get isRefreshing => throw _privateConstructorUsedError;

  /// Search and filter states
  String get searchQuery => throw _privateConstructorUsedError;
  RideStatus? get statusFilter => throw _privateConstructorUsedError;
  DateRange? get dateFilter => throw _privateConstructorUsedError;

  /// Error states
  String? get errorMessage => throw _privateConstructorUsedError;
  Map<String, String> get fieldErrors => throw _privateConstructorUsedError;

  /// Last update timestamp
  DateTime? get lastUpdated => throw _privateConstructorUsedError;

  /// Selected ride for details view
  String? get selectedRideId => throw _privateConstructorUsedError;

  /// Create a copy of RideHistoryState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RideHistoryStateCopyWith<RideHistoryState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RideHistoryStateCopyWith<$Res> {
  factory $RideHistoryStateCopyWith(
          RideHistoryState value, $Res Function(RideHistoryState) then) =
      _$RideHistoryStateCopyWithImpl<$Res, RideHistoryState>;
  @useResult
  $Res call(
      {List<RideHistory> rides,
      int currentPage,
      int itemsPerPage,
      int? totalCount,
      bool hasMoreRides,
      bool isLoadingHistory,
      bool isLoadingMore,
      bool isRefreshing,
      String searchQuery,
      RideStatus? statusFilter,
      DateRange? dateFilter,
      String? errorMessage,
      Map<String, String> fieldErrors,
      DateTime? lastUpdated,
      String? selectedRideId});

  $DateRangeCopyWith<$Res>? get dateFilter;
}

/// @nodoc
class _$RideHistoryStateCopyWithImpl<$Res, $Val extends RideHistoryState>
    implements $RideHistoryStateCopyWith<$Res> {
  _$RideHistoryStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RideHistoryState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? rides = null,
    Object? currentPage = null,
    Object? itemsPerPage = null,
    Object? totalCount = freezed,
    Object? hasMoreRides = null,
    Object? isLoadingHistory = null,
    Object? isLoadingMore = null,
    Object? isRefreshing = null,
    Object? searchQuery = null,
    Object? statusFilter = freezed,
    Object? dateFilter = freezed,
    Object? errorMessage = freezed,
    Object? fieldErrors = null,
    Object? lastUpdated = freezed,
    Object? selectedRideId = freezed,
  }) {
    return _then(_value.copyWith(
      rides: null == rides
          ? _value.rides
          : rides // ignore: cast_nullable_to_non_nullable
              as List<RideHistory>,
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      itemsPerPage: null == itemsPerPage
          ? _value.itemsPerPage
          : itemsPerPage // ignore: cast_nullable_to_non_nullable
              as int,
      totalCount: freezed == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int?,
      hasMoreRides: null == hasMoreRides
          ? _value.hasMoreRides
          : hasMoreRides // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingHistory: null == isLoadingHistory
          ? _value.isLoadingHistory
          : isLoadingHistory // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingMore: null == isLoadingMore
          ? _value.isLoadingMore
          : isLoadingMore // ignore: cast_nullable_to_non_nullable
              as bool,
      isRefreshing: null == isRefreshing
          ? _value.isRefreshing
          : isRefreshing // ignore: cast_nullable_to_non_nullable
              as bool,
      searchQuery: null == searchQuery
          ? _value.searchQuery
          : searchQuery // ignore: cast_nullable_to_non_nullable
              as String,
      statusFilter: freezed == statusFilter
          ? _value.statusFilter
          : statusFilter // ignore: cast_nullable_to_non_nullable
              as RideStatus?,
      dateFilter: freezed == dateFilter
          ? _value.dateFilter
          : dateFilter // ignore: cast_nullable_to_non_nullable
              as DateRange?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      fieldErrors: null == fieldErrors
          ? _value.fieldErrors
          : fieldErrors // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      lastUpdated: freezed == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      selectedRideId: freezed == selectedRideId
          ? _value.selectedRideId
          : selectedRideId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of RideHistoryState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DateRangeCopyWith<$Res>? get dateFilter {
    if (_value.dateFilter == null) {
      return null;
    }

    return $DateRangeCopyWith<$Res>(_value.dateFilter!, (value) {
      return _then(_value.copyWith(dateFilter: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$RideHistoryStateImplCopyWith<$Res>
    implements $RideHistoryStateCopyWith<$Res> {
  factory _$$RideHistoryStateImplCopyWith(_$RideHistoryStateImpl value,
          $Res Function(_$RideHistoryStateImpl) then) =
      __$$RideHistoryStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<RideHistory> rides,
      int currentPage,
      int itemsPerPage,
      int? totalCount,
      bool hasMoreRides,
      bool isLoadingHistory,
      bool isLoadingMore,
      bool isRefreshing,
      String searchQuery,
      RideStatus? statusFilter,
      DateRange? dateFilter,
      String? errorMessage,
      Map<String, String> fieldErrors,
      DateTime? lastUpdated,
      String? selectedRideId});

  @override
  $DateRangeCopyWith<$Res>? get dateFilter;
}

/// @nodoc
class __$$RideHistoryStateImplCopyWithImpl<$Res>
    extends _$RideHistoryStateCopyWithImpl<$Res, _$RideHistoryStateImpl>
    implements _$$RideHistoryStateImplCopyWith<$Res> {
  __$$RideHistoryStateImplCopyWithImpl(_$RideHistoryStateImpl _value,
      $Res Function(_$RideHistoryStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of RideHistoryState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? rides = null,
    Object? currentPage = null,
    Object? itemsPerPage = null,
    Object? totalCount = freezed,
    Object? hasMoreRides = null,
    Object? isLoadingHistory = null,
    Object? isLoadingMore = null,
    Object? isRefreshing = null,
    Object? searchQuery = null,
    Object? statusFilter = freezed,
    Object? dateFilter = freezed,
    Object? errorMessage = freezed,
    Object? fieldErrors = null,
    Object? lastUpdated = freezed,
    Object? selectedRideId = freezed,
  }) {
    return _then(_$RideHistoryStateImpl(
      rides: null == rides
          ? _value._rides
          : rides // ignore: cast_nullable_to_non_nullable
              as List<RideHistory>,
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      itemsPerPage: null == itemsPerPage
          ? _value.itemsPerPage
          : itemsPerPage // ignore: cast_nullable_to_non_nullable
              as int,
      totalCount: freezed == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int?,
      hasMoreRides: null == hasMoreRides
          ? _value.hasMoreRides
          : hasMoreRides // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingHistory: null == isLoadingHistory
          ? _value.isLoadingHistory
          : isLoadingHistory // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingMore: null == isLoadingMore
          ? _value.isLoadingMore
          : isLoadingMore // ignore: cast_nullable_to_non_nullable
              as bool,
      isRefreshing: null == isRefreshing
          ? _value.isRefreshing
          : isRefreshing // ignore: cast_nullable_to_non_nullable
              as bool,
      searchQuery: null == searchQuery
          ? _value.searchQuery
          : searchQuery // ignore: cast_nullable_to_non_nullable
              as String,
      statusFilter: freezed == statusFilter
          ? _value.statusFilter
          : statusFilter // ignore: cast_nullable_to_non_nullable
              as RideStatus?,
      dateFilter: freezed == dateFilter
          ? _value.dateFilter
          : dateFilter // ignore: cast_nullable_to_non_nullable
              as DateRange?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      fieldErrors: null == fieldErrors
          ? _value._fieldErrors
          : fieldErrors // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      lastUpdated: freezed == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      selectedRideId: freezed == selectedRideId
          ? _value.selectedRideId
          : selectedRideId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$RideHistoryStateImpl extends _RideHistoryState {
  const _$RideHistoryStateImpl(
      {final List<RideHistory> rides = const [],
      this.currentPage = 0,
      this.itemsPerPage = 20,
      this.totalCount,
      this.hasMoreRides = true,
      this.isLoadingHistory = false,
      this.isLoadingMore = false,
      this.isRefreshing = false,
      this.searchQuery = '',
      this.statusFilter,
      this.dateFilter,
      this.errorMessage,
      final Map<String, String> fieldErrors = const {},
      this.lastUpdated,
      this.selectedRideId})
      : _rides = rides,
        _fieldErrors = fieldErrors,
        super._();

  /// List of ride history items
  final List<RideHistory> _rides;

  /// List of ride history items
  @override
  @JsonKey()
  List<RideHistory> get rides {
    if (_rides is EqualUnmodifiableListView) return _rides;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_rides);
  }

  /// Current page number (0-based)
  @override
  @JsonKey()
  final int currentPage;

  /// Number of items per page
  @override
  @JsonKey()
  final int itemsPerPage;

  /// Total number of rides available
  @override
  final int? totalCount;

  /// Whether there are more rides to load
  @override
  @JsonKey()
  final bool hasMoreRides;

  /// Loading states
  @override
  @JsonKey()
  final bool isLoadingHistory;
  @override
  @JsonKey()
  final bool isLoadingMore;
  @override
  @JsonKey()
  final bool isRefreshing;

  /// Search and filter states
  @override
  @JsonKey()
  final String searchQuery;
  @override
  final RideStatus? statusFilter;
  @override
  final DateRange? dateFilter;

  /// Error states
  @override
  final String? errorMessage;
  final Map<String, String> _fieldErrors;
  @override
  @JsonKey()
  Map<String, String> get fieldErrors {
    if (_fieldErrors is EqualUnmodifiableMapView) return _fieldErrors;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_fieldErrors);
  }

  /// Last update timestamp
  @override
  final DateTime? lastUpdated;

  /// Selected ride for details view
  @override
  final String? selectedRideId;

  @override
  String toString() {
    return 'RideHistoryState(rides: $rides, currentPage: $currentPage, itemsPerPage: $itemsPerPage, totalCount: $totalCount, hasMoreRides: $hasMoreRides, isLoadingHistory: $isLoadingHistory, isLoadingMore: $isLoadingMore, isRefreshing: $isRefreshing, searchQuery: $searchQuery, statusFilter: $statusFilter, dateFilter: $dateFilter, errorMessage: $errorMessage, fieldErrors: $fieldErrors, lastUpdated: $lastUpdated, selectedRideId: $selectedRideId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RideHistoryStateImpl &&
            const DeepCollectionEquality().equals(other._rides, _rides) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage) &&
            (identical(other.itemsPerPage, itemsPerPage) ||
                other.itemsPerPage == itemsPerPage) &&
            (identical(other.totalCount, totalCount) ||
                other.totalCount == totalCount) &&
            (identical(other.hasMoreRides, hasMoreRides) ||
                other.hasMoreRides == hasMoreRides) &&
            (identical(other.isLoadingHistory, isLoadingHistory) ||
                other.isLoadingHistory == isLoadingHistory) &&
            (identical(other.isLoadingMore, isLoadingMore) ||
                other.isLoadingMore == isLoadingMore) &&
            (identical(other.isRefreshing, isRefreshing) ||
                other.isRefreshing == isRefreshing) &&
            (identical(other.searchQuery, searchQuery) ||
                other.searchQuery == searchQuery) &&
            (identical(other.statusFilter, statusFilter) ||
                other.statusFilter == statusFilter) &&
            (identical(other.dateFilter, dateFilter) ||
                other.dateFilter == dateFilter) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            const DeepCollectionEquality()
                .equals(other._fieldErrors, _fieldErrors) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated) &&
            (identical(other.selectedRideId, selectedRideId) ||
                other.selectedRideId == selectedRideId));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_rides),
      currentPage,
      itemsPerPage,
      totalCount,
      hasMoreRides,
      isLoadingHistory,
      isLoadingMore,
      isRefreshing,
      searchQuery,
      statusFilter,
      dateFilter,
      errorMessage,
      const DeepCollectionEquality().hash(_fieldErrors),
      lastUpdated,
      selectedRideId);

  /// Create a copy of RideHistoryState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RideHistoryStateImplCopyWith<_$RideHistoryStateImpl> get copyWith =>
      __$$RideHistoryStateImplCopyWithImpl<_$RideHistoryStateImpl>(
          this, _$identity);
}

abstract class _RideHistoryState extends RideHistoryState {
  const factory _RideHistoryState(
      {final List<RideHistory> rides,
      final int currentPage,
      final int itemsPerPage,
      final int? totalCount,
      final bool hasMoreRides,
      final bool isLoadingHistory,
      final bool isLoadingMore,
      final bool isRefreshing,
      final String searchQuery,
      final RideStatus? statusFilter,
      final DateRange? dateFilter,
      final String? errorMessage,
      final Map<String, String> fieldErrors,
      final DateTime? lastUpdated,
      final String? selectedRideId}) = _$RideHistoryStateImpl;
  const _RideHistoryState._() : super._();

  /// List of ride history items
  @override
  List<RideHistory> get rides;

  /// Current page number (0-based)
  @override
  int get currentPage;

  /// Number of items per page
  @override
  int get itemsPerPage;

  /// Total number of rides available
  @override
  int? get totalCount;

  /// Whether there are more rides to load
  @override
  bool get hasMoreRides;

  /// Loading states
  @override
  bool get isLoadingHistory;
  @override
  bool get isLoadingMore;
  @override
  bool get isRefreshing;

  /// Search and filter states
  @override
  String get searchQuery;
  @override
  RideStatus? get statusFilter;
  @override
  DateRange? get dateFilter;

  /// Error states
  @override
  String? get errorMessage;
  @override
  Map<String, String> get fieldErrors;

  /// Last update timestamp
  @override
  DateTime? get lastUpdated;

  /// Selected ride for details view
  @override
  String? get selectedRideId;

  /// Create a copy of RideHistoryState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RideHistoryStateImplCopyWith<_$RideHistoryStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$RideDetailsState {
  /// Full ride details
  Ride? get ride => throw _privateConstructorUsedError;

  /// Ride receipt information
  RideReceipt? get receipt => throw _privateConstructorUsedError;

  /// Loading states
  bool get isLoadingDetails => throw _privateConstructorUsedError;
  bool get isLoadingReceipt => throw _privateConstructorUsedError;

  /// Error states
  String? get errorMessage => throw _privateConstructorUsedError;

  /// Last update timestamp
  DateTime? get lastUpdated => throw _privateConstructorUsedError;

  /// Create a copy of RideDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RideDetailsStateCopyWith<RideDetailsState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RideDetailsStateCopyWith<$Res> {
  factory $RideDetailsStateCopyWith(
          RideDetailsState value, $Res Function(RideDetailsState) then) =
      _$RideDetailsStateCopyWithImpl<$Res, RideDetailsState>;
  @useResult
  $Res call(
      {Ride? ride,
      RideReceipt? receipt,
      bool isLoadingDetails,
      bool isLoadingReceipt,
      String? errorMessage,
      DateTime? lastUpdated});

  $RideCopyWith<$Res>? get ride;
  $RideReceiptCopyWith<$Res>? get receipt;
}

/// @nodoc
class _$RideDetailsStateCopyWithImpl<$Res, $Val extends RideDetailsState>
    implements $RideDetailsStateCopyWith<$Res> {
  _$RideDetailsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RideDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? ride = freezed,
    Object? receipt = freezed,
    Object? isLoadingDetails = null,
    Object? isLoadingReceipt = null,
    Object? errorMessage = freezed,
    Object? lastUpdated = freezed,
  }) {
    return _then(_value.copyWith(
      ride: freezed == ride
          ? _value.ride
          : ride // ignore: cast_nullable_to_non_nullable
              as Ride?,
      receipt: freezed == receipt
          ? _value.receipt
          : receipt // ignore: cast_nullable_to_non_nullable
              as RideReceipt?,
      isLoadingDetails: null == isLoadingDetails
          ? _value.isLoadingDetails
          : isLoadingDetails // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingReceipt: null == isLoadingReceipt
          ? _value.isLoadingReceipt
          : isLoadingReceipt // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      lastUpdated: freezed == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }

  /// Create a copy of RideDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RideCopyWith<$Res>? get ride {
    if (_value.ride == null) {
      return null;
    }

    return $RideCopyWith<$Res>(_value.ride!, (value) {
      return _then(_value.copyWith(ride: value) as $Val);
    });
  }

  /// Create a copy of RideDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RideReceiptCopyWith<$Res>? get receipt {
    if (_value.receipt == null) {
      return null;
    }

    return $RideReceiptCopyWith<$Res>(_value.receipt!, (value) {
      return _then(_value.copyWith(receipt: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$RideDetailsStateImplCopyWith<$Res>
    implements $RideDetailsStateCopyWith<$Res> {
  factory _$$RideDetailsStateImplCopyWith(_$RideDetailsStateImpl value,
          $Res Function(_$RideDetailsStateImpl) then) =
      __$$RideDetailsStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Ride? ride,
      RideReceipt? receipt,
      bool isLoadingDetails,
      bool isLoadingReceipt,
      String? errorMessage,
      DateTime? lastUpdated});

  @override
  $RideCopyWith<$Res>? get ride;
  @override
  $RideReceiptCopyWith<$Res>? get receipt;
}

/// @nodoc
class __$$RideDetailsStateImplCopyWithImpl<$Res>
    extends _$RideDetailsStateCopyWithImpl<$Res, _$RideDetailsStateImpl>
    implements _$$RideDetailsStateImplCopyWith<$Res> {
  __$$RideDetailsStateImplCopyWithImpl(_$RideDetailsStateImpl _value,
      $Res Function(_$RideDetailsStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of RideDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? ride = freezed,
    Object? receipt = freezed,
    Object? isLoadingDetails = null,
    Object? isLoadingReceipt = null,
    Object? errorMessage = freezed,
    Object? lastUpdated = freezed,
  }) {
    return _then(_$RideDetailsStateImpl(
      ride: freezed == ride
          ? _value.ride
          : ride // ignore: cast_nullable_to_non_nullable
              as Ride?,
      receipt: freezed == receipt
          ? _value.receipt
          : receipt // ignore: cast_nullable_to_non_nullable
              as RideReceipt?,
      isLoadingDetails: null == isLoadingDetails
          ? _value.isLoadingDetails
          : isLoadingDetails // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingReceipt: null == isLoadingReceipt
          ? _value.isLoadingReceipt
          : isLoadingReceipt // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      lastUpdated: freezed == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc

class _$RideDetailsStateImpl extends _RideDetailsState {
  const _$RideDetailsStateImpl(
      {this.ride,
      this.receipt,
      this.isLoadingDetails = false,
      this.isLoadingReceipt = false,
      this.errorMessage,
      this.lastUpdated})
      : super._();

  /// Full ride details
  @override
  final Ride? ride;

  /// Ride receipt information
  @override
  final RideReceipt? receipt;

  /// Loading states
  @override
  @JsonKey()
  final bool isLoadingDetails;
  @override
  @JsonKey()
  final bool isLoadingReceipt;

  /// Error states
  @override
  final String? errorMessage;

  /// Last update timestamp
  @override
  final DateTime? lastUpdated;

  @override
  String toString() {
    return 'RideDetailsState(ride: $ride, receipt: $receipt, isLoadingDetails: $isLoadingDetails, isLoadingReceipt: $isLoadingReceipt, errorMessage: $errorMessage, lastUpdated: $lastUpdated)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RideDetailsStateImpl &&
            (identical(other.ride, ride) || other.ride == ride) &&
            (identical(other.receipt, receipt) || other.receipt == receipt) &&
            (identical(other.isLoadingDetails, isLoadingDetails) ||
                other.isLoadingDetails == isLoadingDetails) &&
            (identical(other.isLoadingReceipt, isLoadingReceipt) ||
                other.isLoadingReceipt == isLoadingReceipt) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated));
  }

  @override
  int get hashCode => Object.hash(runtimeType, ride, receipt, isLoadingDetails,
      isLoadingReceipt, errorMessage, lastUpdated);

  /// Create a copy of RideDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RideDetailsStateImplCopyWith<_$RideDetailsStateImpl> get copyWith =>
      __$$RideDetailsStateImplCopyWithImpl<_$RideDetailsStateImpl>(
          this, _$identity);
}

abstract class _RideDetailsState extends RideDetailsState {
  const factory _RideDetailsState(
      {final Ride? ride,
      final RideReceipt? receipt,
      final bool isLoadingDetails,
      final bool isLoadingReceipt,
      final String? errorMessage,
      final DateTime? lastUpdated}) = _$RideDetailsStateImpl;
  const _RideDetailsState._() : super._();

  /// Full ride details
  @override
  Ride? get ride;

  /// Ride receipt information
  @override
  RideReceipt? get receipt;

  /// Loading states
  @override
  bool get isLoadingDetails;
  @override
  bool get isLoadingReceipt;

  /// Error states
  @override
  String? get errorMessage;

  /// Last update timestamp
  @override
  DateTime? get lastUpdated;

  /// Create a copy of RideDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RideDetailsStateImplCopyWith<_$RideDetailsStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$DateRange {
  /// Start date (inclusive)
  DateTime get startDate => throw _privateConstructorUsedError;

  /// End date (inclusive)
  DateTime get endDate => throw _privateConstructorUsedError;

  /// Display label for the range
  String? get label => throw _privateConstructorUsedError;

  /// Create a copy of DateRange
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DateRangeCopyWith<DateRange> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DateRangeCopyWith<$Res> {
  factory $DateRangeCopyWith(DateRange value, $Res Function(DateRange) then) =
      _$DateRangeCopyWithImpl<$Res, DateRange>;
  @useResult
  $Res call({DateTime startDate, DateTime endDate, String? label});
}

/// @nodoc
class _$DateRangeCopyWithImpl<$Res, $Val extends DateRange>
    implements $DateRangeCopyWith<$Res> {
  _$DateRangeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DateRange
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? startDate = null,
    Object? endDate = null,
    Object? label = freezed,
  }) {
    return _then(_value.copyWith(
      startDate: null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endDate: null == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      label: freezed == label
          ? _value.label
          : label // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DateRangeImplCopyWith<$Res>
    implements $DateRangeCopyWith<$Res> {
  factory _$$DateRangeImplCopyWith(
          _$DateRangeImpl value, $Res Function(_$DateRangeImpl) then) =
      __$$DateRangeImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({DateTime startDate, DateTime endDate, String? label});
}

/// @nodoc
class __$$DateRangeImplCopyWithImpl<$Res>
    extends _$DateRangeCopyWithImpl<$Res, _$DateRangeImpl>
    implements _$$DateRangeImplCopyWith<$Res> {
  __$$DateRangeImplCopyWithImpl(
      _$DateRangeImpl _value, $Res Function(_$DateRangeImpl) _then)
      : super(_value, _then);

  /// Create a copy of DateRange
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? startDate = null,
    Object? endDate = null,
    Object? label = freezed,
  }) {
    return _then(_$DateRangeImpl(
      startDate: null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endDate: null == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      label: freezed == label
          ? _value.label
          : label // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$DateRangeImpl extends _DateRange {
  const _$DateRangeImpl(
      {required this.startDate, required this.endDate, this.label})
      : super._();

  /// Start date (inclusive)
  @override
  final DateTime startDate;

  /// End date (inclusive)
  @override
  final DateTime endDate;

  /// Display label for the range
  @override
  final String? label;

  @override
  String toString() {
    return 'DateRange(startDate: $startDate, endDate: $endDate, label: $label)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DateRangeImpl &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.label, label) || other.label == label));
  }

  @override
  int get hashCode => Object.hash(runtimeType, startDate, endDate, label);

  /// Create a copy of DateRange
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DateRangeImplCopyWith<_$DateRangeImpl> get copyWith =>
      __$$DateRangeImplCopyWithImpl<_$DateRangeImpl>(this, _$identity);
}

abstract class _DateRange extends DateRange {
  const factory _DateRange(
      {required final DateTime startDate,
      required final DateTime endDate,
      final String? label}) = _$DateRangeImpl;
  const _DateRange._() : super._();

  /// Start date (inclusive)
  @override
  DateTime get startDate;

  /// End date (inclusive)
  @override
  DateTime get endDate;

  /// Display label for the range
  @override
  String? get label;

  /// Create a copy of DateRange
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DateRangeImplCopyWith<_$DateRangeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$RideHistoryFilters {
  /// Search query for locations or driver names
  String get searchQuery => throw _privateConstructorUsedError;

  /// Filter by ride status
  RideStatus? get statusFilter => throw _privateConstructorUsedError;

  /// Filter by date range
  DateRange? get dateFilter => throw _privateConstructorUsedError;

  /// Sort order for results
  RideHistorySortOrder get sortOrder => throw _privateConstructorUsedError;

  /// Minimum fare filter
  double? get minFare => throw _privateConstructorUsedError;

  /// Maximum fare filter
  double? get maxFare => throw _privateConstructorUsedError;

  /// Create a copy of RideHistoryFilters
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RideHistoryFiltersCopyWith<RideHistoryFilters> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RideHistoryFiltersCopyWith<$Res> {
  factory $RideHistoryFiltersCopyWith(
          RideHistoryFilters value, $Res Function(RideHistoryFilters) then) =
      _$RideHistoryFiltersCopyWithImpl<$Res, RideHistoryFilters>;
  @useResult
  $Res call(
      {String searchQuery,
      RideStatus? statusFilter,
      DateRange? dateFilter,
      RideHistorySortOrder sortOrder,
      double? minFare,
      double? maxFare});

  $DateRangeCopyWith<$Res>? get dateFilter;
}

/// @nodoc
class _$RideHistoryFiltersCopyWithImpl<$Res, $Val extends RideHistoryFilters>
    implements $RideHistoryFiltersCopyWith<$Res> {
  _$RideHistoryFiltersCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RideHistoryFilters
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? searchQuery = null,
    Object? statusFilter = freezed,
    Object? dateFilter = freezed,
    Object? sortOrder = null,
    Object? minFare = freezed,
    Object? maxFare = freezed,
  }) {
    return _then(_value.copyWith(
      searchQuery: null == searchQuery
          ? _value.searchQuery
          : searchQuery // ignore: cast_nullable_to_non_nullable
              as String,
      statusFilter: freezed == statusFilter
          ? _value.statusFilter
          : statusFilter // ignore: cast_nullable_to_non_nullable
              as RideStatus?,
      dateFilter: freezed == dateFilter
          ? _value.dateFilter
          : dateFilter // ignore: cast_nullable_to_non_nullable
              as DateRange?,
      sortOrder: null == sortOrder
          ? _value.sortOrder
          : sortOrder // ignore: cast_nullable_to_non_nullable
              as RideHistorySortOrder,
      minFare: freezed == minFare
          ? _value.minFare
          : minFare // ignore: cast_nullable_to_non_nullable
              as double?,
      maxFare: freezed == maxFare
          ? _value.maxFare
          : maxFare // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }

  /// Create a copy of RideHistoryFilters
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DateRangeCopyWith<$Res>? get dateFilter {
    if (_value.dateFilter == null) {
      return null;
    }

    return $DateRangeCopyWith<$Res>(_value.dateFilter!, (value) {
      return _then(_value.copyWith(dateFilter: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$RideHistoryFiltersImplCopyWith<$Res>
    implements $RideHistoryFiltersCopyWith<$Res> {
  factory _$$RideHistoryFiltersImplCopyWith(_$RideHistoryFiltersImpl value,
          $Res Function(_$RideHistoryFiltersImpl) then) =
      __$$RideHistoryFiltersImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String searchQuery,
      RideStatus? statusFilter,
      DateRange? dateFilter,
      RideHistorySortOrder sortOrder,
      double? minFare,
      double? maxFare});

  @override
  $DateRangeCopyWith<$Res>? get dateFilter;
}

/// @nodoc
class __$$RideHistoryFiltersImplCopyWithImpl<$Res>
    extends _$RideHistoryFiltersCopyWithImpl<$Res, _$RideHistoryFiltersImpl>
    implements _$$RideHistoryFiltersImplCopyWith<$Res> {
  __$$RideHistoryFiltersImplCopyWithImpl(_$RideHistoryFiltersImpl _value,
      $Res Function(_$RideHistoryFiltersImpl) _then)
      : super(_value, _then);

  /// Create a copy of RideHistoryFilters
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? searchQuery = null,
    Object? statusFilter = freezed,
    Object? dateFilter = freezed,
    Object? sortOrder = null,
    Object? minFare = freezed,
    Object? maxFare = freezed,
  }) {
    return _then(_$RideHistoryFiltersImpl(
      searchQuery: null == searchQuery
          ? _value.searchQuery
          : searchQuery // ignore: cast_nullable_to_non_nullable
              as String,
      statusFilter: freezed == statusFilter
          ? _value.statusFilter
          : statusFilter // ignore: cast_nullable_to_non_nullable
              as RideStatus?,
      dateFilter: freezed == dateFilter
          ? _value.dateFilter
          : dateFilter // ignore: cast_nullable_to_non_nullable
              as DateRange?,
      sortOrder: null == sortOrder
          ? _value.sortOrder
          : sortOrder // ignore: cast_nullable_to_non_nullable
              as RideHistorySortOrder,
      minFare: freezed == minFare
          ? _value.minFare
          : minFare // ignore: cast_nullable_to_non_nullable
              as double?,
      maxFare: freezed == maxFare
          ? _value.maxFare
          : maxFare // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc

class _$RideHistoryFiltersImpl extends _RideHistoryFilters {
  const _$RideHistoryFiltersImpl(
      {this.searchQuery = '',
      this.statusFilter,
      this.dateFilter,
      this.sortOrder = RideHistorySortOrder.dateDescending,
      this.minFare,
      this.maxFare})
      : super._();

  /// Search query for locations or driver names
  @override
  @JsonKey()
  final String searchQuery;

  /// Filter by ride status
  @override
  final RideStatus? statusFilter;

  /// Filter by date range
  @override
  final DateRange? dateFilter;

  /// Sort order for results
  @override
  @JsonKey()
  final RideHistorySortOrder sortOrder;

  /// Minimum fare filter
  @override
  final double? minFare;

  /// Maximum fare filter
  @override
  final double? maxFare;

  @override
  String toString() {
    return 'RideHistoryFilters(searchQuery: $searchQuery, statusFilter: $statusFilter, dateFilter: $dateFilter, sortOrder: $sortOrder, minFare: $minFare, maxFare: $maxFare)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RideHistoryFiltersImpl &&
            (identical(other.searchQuery, searchQuery) ||
                other.searchQuery == searchQuery) &&
            (identical(other.statusFilter, statusFilter) ||
                other.statusFilter == statusFilter) &&
            (identical(other.dateFilter, dateFilter) ||
                other.dateFilter == dateFilter) &&
            (identical(other.sortOrder, sortOrder) ||
                other.sortOrder == sortOrder) &&
            (identical(other.minFare, minFare) || other.minFare == minFare) &&
            (identical(other.maxFare, maxFare) || other.maxFare == maxFare));
  }

  @override
  int get hashCode => Object.hash(runtimeType, searchQuery, statusFilter,
      dateFilter, sortOrder, minFare, maxFare);

  /// Create a copy of RideHistoryFilters
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RideHistoryFiltersImplCopyWith<_$RideHistoryFiltersImpl> get copyWith =>
      __$$RideHistoryFiltersImplCopyWithImpl<_$RideHistoryFiltersImpl>(
          this, _$identity);
}

abstract class _RideHistoryFilters extends RideHistoryFilters {
  const factory _RideHistoryFilters(
      {final String searchQuery,
      final RideStatus? statusFilter,
      final DateRange? dateFilter,
      final RideHistorySortOrder sortOrder,
      final double? minFare,
      final double? maxFare}) = _$RideHistoryFiltersImpl;
  const _RideHistoryFilters._() : super._();

  /// Search query for locations or driver names
  @override
  String get searchQuery;

  /// Filter by ride status
  @override
  RideStatus? get statusFilter;

  /// Filter by date range
  @override
  DateRange? get dateFilter;

  /// Sort order for results
  @override
  RideHistorySortOrder get sortOrder;

  /// Minimum fare filter
  @override
  double? get minFare;

  /// Maximum fare filter
  @override
  double? get maxFare;

  /// Create a copy of RideHistoryFilters
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RideHistoryFiltersImplCopyWith<_$RideHistoryFiltersImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
