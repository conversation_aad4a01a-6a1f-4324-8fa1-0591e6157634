import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dartz/dartz.dart';

import '../../core/di/service_locator.dart';
import '../../domain/repositories/ride_repository.dart';
import '../../domain/entities/ride.dart';
import '../../domain/entities/location_models.dart';
import '../../core/errors/app_error.dart';
import 'ride_tracking_state.dart';

/// StateNotifier for managing ride tracking functionality
class RideTrackingNotifier extends StateNotifier<RideTrackingState> {
  final RideRepository _rideRepository;

  // Timers for periodic updates
  Timer? _statusUpdateTimer;
  Timer? _etaUpdateTimer;

  // Stream subscriptions
  StreamSubscription<Either<AppError, LocationCoordinates>>?
  _driverLocationSubscription;

  RideTrackingNotifier(this._rideRepository) : super(const RideTrackingState());

  /// Initialize ride tracking for a specific ride
  Future<void> initializeTracking(String rideId) async {
    state = state.copyWith(
      isLoadingRide: true,
      errorMessage: null,
      connectionStatus: ConnectionStatus.connecting,
    );

    // Load the ride details
    final rideResult = await _rideRepository.getRideStatus(rideId);

    rideResult.fold(
      (error) => state = state.copyWith(
        isLoadingRide: false,
        errorMessage: error.message,
        connectionStatus: ConnectionStatus.failed,
      ),
      (ride) async {
        state = state.copyWith(
          isLoadingRide: false,
          activeRide: ride,
          connectionStatus: ConnectionStatus.connected,
          lastUpdated: DateTime.now(),
        );

        // Initialize driver info if ride is accepted
        if (ride.status == RideStatus.accepted ||
            ride.status == RideStatus.inProgress) {
          await _loadDriverInfo(ride.driverId!);
          await _startRealTimeUpdates(rideId);
        }
      },
    );
  }

  /// Load driver information
  Future<void> _loadDriverInfo(String driverId) async {
    // For now, create mock driver info since we don't have a driver endpoint
    // In a real implementation, this would call the driver API
    final driverInfo = DriverInfo(
      id: driverId,
      name: 'John Doe', // Mock data
      phoneNumber: '******-555-0123',
      rating: 4.8,
      totalTrips: 150,
      vehicleInfo: const VehicleInfo(
        make: 'Toyota',
        model: 'Camry',
        year: 2020,
        color: 'Silver',
        licensePlate: 'SLU-1234',
        vehicleType: 'Sedan',
      ),
      photoUrl: null,
    );

    state = state.copyWith(driverInfo: driverInfo);
  }

  /// Start real-time updates for ride tracking
  Future<void> _startRealTimeUpdates(String rideId) async {
    // Start periodic status updates
    _statusUpdateTimer = Timer.periodic(
      const Duration(seconds: 10),
      (_) => _updateRideStatus(rideId),
    );

    // Start periodic ETA updates
    _etaUpdateTimer = Timer.periodic(
      const Duration(seconds: 30),
      (_) => _updateETA(),
    );

    // Start driver location updates (mock implementation)
    _startDriverLocationUpdates();
  }

  /// Update ride status periodically
  Future<void> _updateRideStatus(String rideId) async {
    if (state.isUpdatingStatus) return;

    state = state.copyWith(isUpdatingStatus: true);

    final result = await _rideRepository.getRideStatus(rideId);

    result.fold(
      (error) => state = state.copyWith(
        isUpdatingStatus: false,
        errorMessage: error.message,
        connectionStatus: ConnectionStatus.reconnecting,
      ),
      (ride) {
        state = state.copyWith(
          isUpdatingStatus: false,
          activeRide: ride,
          lastUpdated: DateTime.now(),
          connectionStatus: ConnectionStatus.connected,
        );

        // Stop tracking if ride is completed or cancelled
        if (ride.status == RideStatus.completed ||
            ride.status == RideStatus.cancelled) {
          _stopRealTimeUpdates();
        }
      },
    );
  }

  /// Start mock driver location updates
  void _startDriverLocationUpdates() {
    // In a real implementation, this would connect to a WebSocket or SSE stream
    // For now, we'll simulate location updates
    Timer.periodic(const Duration(seconds: 5), (timer) {
      if (state.activeRide == null ||
          state.activeRide!.status == RideStatus.completed ||
          state.activeRide!.status == RideStatus.cancelled) {
        timer.cancel();
        return;
      }

      _simulateDriverLocationUpdate();
    });
  }

  /// Simulate driver location updates (for development/testing)
  void _simulateDriverLocationUpdate() {
    if (state.activeRide == null) return;

    // Mock location update - in reality this would come from the backend
    final mockLocation = LocationCoordinates(
      latitude: 14.0 + (DateTime.now().millisecondsSinceEpoch % 1000) / 10000,
      longitude: -61.0 + (DateTime.now().millisecondsSinceEpoch % 1000) / 10000,
      accuracy: 10.0,
      timestamp: DateTime.now(),
    );

    state = state.copyWith(
      driverLocation: mockLocation,
      lastUpdated: DateTime.now(),
    );

    // Update ETA based on new location
    _calculateETA();
  }

  /// Update ETA calculation
  Future<void> _updateETA() async {
    if (state.activeRide == null || state.driverLocation == null) return;

    _calculateETA();
  }

  /// Calculate ETA based on current driver location and destination
  void _calculateETA() {
    if (state.activeRide == null || state.driverLocation == null) return;

    final ride = state.activeRide!;
    final driverLocation = state.driverLocation!;

    // Determine destination based on ride status
    LocationCoordinates destination;
    if (ride.status == RideStatus.accepted) {
      // Driver is heading to pickup
      destination = LocationCoordinates(
        latitude: ride.pickupLocation.latitude,
        longitude: ride.pickupLocation.longitude,
      );
    } else {
      // Driver is heading to dropoff
      destination = LocationCoordinates(
        latitude: ride.dropoffLocation.latitude,
        longitude: ride.dropoffLocation.longitude,
      );
    }

    // Calculate distance
    final distanceKm = driverLocation.distanceTo(destination);

    // Estimate time based on average speed (30 km/h in city traffic)
    const averageSpeedKmh = 30.0;
    final estimatedMinutes = ((distanceKm / averageSpeedKmh) * 60).round();

    state = state.copyWith(
      estimatedArrivalMinutes: estimatedMinutes,
      distanceKm: distanceKm,
    );
  }

  /// Cancel the current ride (deprecated - use RideCancellationNotifier instead)
  @Deprecated('Use RideCancellationNotifier for ride cancellation')
  Future<void> cancelRide(CancellationReason reason) async {
    if (state.activeRide == null || !state.canCancelRide) return;

    state = state.copyWith(isUpdatingStatus: true);

    final result = await _rideRepository.cancelRide(
      state.activeRide!.id,
      reason,
    );

    result.fold(
      (error) => state = state.copyWith(
        isUpdatingStatus: false,
        errorMessage: error.message,
      ),
      (_) {
        state = state.copyWith(
          isUpdatingStatus: false,
          activeRide: state.activeRide!.copyWith(
            status: RideStatus.cancelled,
            cancelledAt: DateTime.now(),
            cancellationReason: reason,
          ),
        );
        _stopRealTimeUpdates();
      },
    );
  }

  /// Update ride status after cancellation (called by cancellation notifier)
  void updateRideAfterCancellation(Ride cancelledRide) {
    state = state.copyWith(activeRide: cancelledRide);
    _stopRealTimeUpdates();
  }

  /// Stop all real-time updates
  void _stopRealTimeUpdates() {
    _statusUpdateTimer?.cancel();
    _etaUpdateTimer?.cancel();
    _driverLocationSubscription?.cancel();

    state = state.copyWith(connectionStatus: ConnectionStatus.disconnected);
  }

  /// Refresh ride data manually
  Future<void> refreshRideData() async {
    if (state.activeRide == null) return;

    await _updateRideStatus(state.activeRide!.id);
  }

  /// Clear tracking state
  void clearTracking() {
    _stopRealTimeUpdates();
    state = const RideTrackingState();
  }

  /// Clear errors
  void clearErrors() {
    state = state.copyWith(errorMessage: null, fieldErrors: {});
  }

  @override
  void dispose() {
    _stopRealTimeUpdates();
    super.dispose();
  }
}

/// Provider for RideTrackingNotifier
final rideTrackingNotifierProvider =
    StateNotifierProvider<RideTrackingNotifier, RideTrackingState>((ref) {
      return RideTrackingNotifier(getIt<RideRepository>());
    });

/// Provider for active ride
final activeRideProvider = Provider<Ride?>((ref) {
  final trackingState = ref.watch(rideTrackingNotifierProvider);
  return trackingState.activeRide;
});

/// Provider for driver information
final driverInfoProvider = Provider<DriverInfo?>((ref) {
  final trackingState = ref.watch(rideTrackingNotifierProvider);
  return trackingState.driverInfo;
});

/// Provider for driver location
final driverLocationProvider = Provider<LocationCoordinates?>((ref) {
  final trackingState = ref.watch(rideTrackingNotifierProvider);
  return trackingState.driverLocation;
});

/// Provider for ETA information
final etaProvider = Provider<String?>((ref) {
  final trackingState = ref.watch(rideTrackingNotifierProvider);
  return trackingState.formattedETA;
});

/// Provider for trip progress
final tripProgressProvider = Provider<TripProgress?>((ref) {
  final trackingState = ref.watch(rideTrackingNotifierProvider);
  return trackingState.tripProgress;
});

/// Provider for ride status display text
final rideStatusDisplayProvider = Provider<String>((ref) {
  final trackingState = ref.watch(rideTrackingNotifierProvider);
  return trackingState.statusDisplayText;
});

/// Provider for checking if ride can be cancelled
final canCancelRideProvider = Provider<bool>((ref) {
  final trackingState = ref.watch(rideTrackingNotifierProvider);
  return trackingState.canCancelRide;
});

/// Provider for connection status
final connectionStatusProvider = Provider<ConnectionStatus>((ref) {
  final trackingState = ref.watch(rideTrackingNotifierProvider);
  return trackingState.connectionStatus;
});

/// Provider for checking if tracking is loading
final isTrackingLoadingProvider = Provider<bool>((ref) {
  final trackingState = ref.watch(rideTrackingNotifierProvider);
  return trackingState.isLoading;
});

/// Stream provider for real-time driver location updates
final driverLocationStreamProvider =
    StreamProvider.family<DriverLocationUpdate?, String>((ref, rideId) {
      // In a real implementation, this would connect to a WebSocket or SSE stream
      // For now, return an empty stream
      return Stream.empty();
    });

/// Stream provider for real-time ride status updates
final rideStatusStreamProvider = StreamProvider.family<Ride?, String>((
  ref,
  rideId,
) {
  // In a real implementation, this would connect to a WebSocket or SSE stream
  // For now, return an empty stream
  return Stream.empty();
});

/// Provider for ETA updates
final etaUpdateProvider = StreamProvider.family<ETAUpdate?, String>((
  ref,
  rideId,
) {
  // In a real implementation, this would provide real-time ETA updates
  // For now, return an empty stream
  return Stream.empty();
});
