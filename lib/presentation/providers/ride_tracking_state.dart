import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/ride.dart';
import '../../domain/entities/location_models.dart';

part 'ride_tracking_state.freezed.dart';

/// State for ride tracking functionality
@freezed
class RideTrackingState with _$RideTrackingState {
  const RideTrackingState._();

  /// Creates a new ride tracking state
  const factory RideTrackingState({
    /// Current active ride being tracked
    Ride? activeRide,

    /// Driver's current location
    LocationCoordinates? driverLocation,

    /// Driver information
    DriverInfo? driverInfo,

    /// Estimated time of arrival in minutes
    int? estimatedArrivalMinutes,

    /// Distance to pickup/destination in kilometers
    double? distanceKm,

    /// Trip progress information
    TripProgress? tripProgress,

    /// Loading states
    @Default(false) bool isLoadingRide,
    @Default(false) bool isLoadingDriverLocation,
    @Default(false) bool isUpdatingStatus,

    /// Error states
    String? errorMessage,
    @Default({}) Map<String, String> fieldErrors,

    /// Last update timestamp
    DateTime? lastUpdated,

    /// Connection status for real-time updates
    @Default(ConnectionStatus.disconnected) ConnectionStatus connectionStatus,
  }) = _RideTrackingState;

  /// Check if ride is currently being tracked
  bool get hasActiveRide => activeRide != null;

  /// Check if driver information is available
  bool get hasDriverInfo => driverInfo != null;

  /// Check if driver location is available
  bool get hasDriverLocation => driverLocation != null;

  /// Check if ETA is available
  bool get hasETA => estimatedArrivalMinutes != null;

  /// Check if any loading operation is in progress
  bool get isLoading =>
      isLoadingRide || isLoadingDriverLocation || isUpdatingStatus;

  /// Check if form has any errors
  bool get hasErrors => fieldErrors.isNotEmpty || errorMessage != null;

  /// Get error for specific field
  String? getFieldError(String field) => fieldErrors[field];

  /// Check if specific field has error
  bool hasFieldError(String field) => fieldErrors.containsKey(field);

  /// Check if ride can be cancelled
  bool get canCancelRide => activeRide?.canBeCancelledByRider() ?? false;

  /// Check if real-time updates are connected
  bool get isConnected => connectionStatus == ConnectionStatus.connected;

  /// Get formatted ETA string
  String? get formattedETA {
    if (estimatedArrivalMinutes == null) return null;

    if (estimatedArrivalMinutes! < 1) {
      return 'Arriving now';
    } else if (estimatedArrivalMinutes! == 1) {
      return '1 minute';
    } else {
      return '$estimatedArrivalMinutes minutes';
    }
  }

  /// Get current ride status display text
  String get statusDisplayText {
    if (activeRide == null) return 'No active ride';

    switch (activeRide!.status) {
      case RideStatus.requested:
        return 'Finding driver...';
      case RideStatus.accepted:
        return 'Driver on the way';
      case RideStatus.inProgress:
        return 'Trip in progress';
      case RideStatus.completed:
        return 'Trip completed';
      case RideStatus.cancelled:
        return 'Trip cancelled';
    }
  }
}

/// Information about the assigned driver
@freezed
class DriverInfo with _$DriverInfo {
  const DriverInfo._();

  /// Creates a new driver info object
  const factory DriverInfo({
    /// Driver's unique identifier
    required String id,

    /// Driver's name
    required String name,

    /// Driver's phone number (optional)
    String? phoneNumber,

    /// Driver's rating (1-5 stars)
    double? rating,

    /// Number of completed trips
    int? totalTrips,

    /// Vehicle information
    VehicleInfo? vehicleInfo,

    /// Driver's profile photo URL (optional)
    String? photoUrl,
  }) = _DriverInfo;

  /// Get formatted rating string
  String get formattedRating {
    if (rating == null) return 'No rating';
    return '${rating!.toStringAsFixed(1)} ⭐';
  }

  /// Get driver display name with fallback
  String get displayName => name.isNotEmpty ? name : 'Driver';
}

/// Information about the driver's vehicle
@freezed
class VehicleInfo with _$VehicleInfo {
  const VehicleInfo._();

  /// Creates a new vehicle info object
  const factory VehicleInfo({
    /// Vehicle make (e.g., Toyota)
    required String make,

    /// Vehicle model (e.g., Camry)
    required String model,

    /// Vehicle year
    int? year,

    /// Vehicle color
    required String color,

    /// License plate number
    required String licensePlate,

    /// Vehicle type (sedan, suv, etc.)
    String? vehicleType,
  }) = _VehicleInfo;

  /// Get formatted vehicle description
  String get description {
    final parts = <String>[];

    if (year != null) parts.add(year.toString());
    parts.add(color);
    parts.add(make);
    parts.add(model);

    return parts.join(' ');
  }

  /// Get short vehicle description for display
  String get shortDescription => '$color $make $model';
}

/// Trip progress information
@freezed
class TripProgress with _$TripProgress {
  const TripProgress._();

  /// Creates a new trip progress object
  const factory TripProgress({
    /// Total distance of the trip in kilometers
    required double totalDistanceKm,

    /// Distance completed so far in kilometers
    required double completedDistanceKm,

    /// Estimated total duration in minutes
    required int estimatedDurationMinutes,

    /// Time elapsed since trip started in minutes
    required int elapsedMinutes,

    /// Current phase of the trip
    required TripPhase currentPhase,

    /// Percentage of trip completed (0-100)
    @Default(0.0) double progressPercentage,
  }) = _TripProgress;

  /// Get remaining distance in kilometers
  double get remainingDistanceKm => totalDistanceKm - completedDistanceKm;

  /// Get remaining time in minutes
  int get remainingMinutes => estimatedDurationMinutes - elapsedMinutes;

  /// Get formatted progress string
  String get formattedProgress {
    return '${progressPercentage.toStringAsFixed(0)}% complete';
  }

  /// Get formatted remaining time
  String get formattedRemainingTime {
    if (remainingMinutes <= 0) return 'Arriving soon';
    if (remainingMinutes == 1) return '1 minute remaining';
    return '$remainingMinutes minutes remaining';
  }
}

/// Current phase of the trip
enum TripPhase {
  /// Driver is heading to pickup location
  driverEnRoute,

  /// Driver has arrived at pickup location
  driverArrived,

  /// Trip is in progress to destination
  inProgress,

  /// Trip is completed
  completed,
}

/// Connection status for real-time updates
enum ConnectionStatus {
  /// Not connected to real-time updates
  disconnected,

  /// Connecting to real-time updates
  connecting,

  /// Connected and receiving real-time updates
  connected,

  /// Connection lost, attempting to reconnect
  reconnecting,

  /// Connection failed
  failed,
}

/// Location update from driver
@freezed
class DriverLocationUpdate with _$DriverLocationUpdate {
  const DriverLocationUpdate._();

  /// Creates a new driver location update
  const factory DriverLocationUpdate({
    /// Driver's current location
    required LocationCoordinates location,

    /// Timestamp of the location update
    required DateTime timestamp,

    /// Driver's current heading/bearing in degrees (0-360)
    double? bearing,

    /// Driver's current speed in km/h
    double? speedKmh,

    /// Accuracy of the location in meters
    double? accuracy,
  }) = _DriverLocationUpdate;

  /// Check if location update is recent (within last 30 seconds)
  bool get isRecent {
    final now = DateTime.now();
    final diff = now.difference(timestamp);
    return diff.inSeconds <= 30;
  }

  /// Check if location is accurate enough for tracking
  bool get isAccurate => accuracy == null || accuracy! <= 50; // 50 meters
}

/// ETA update information
@freezed
class ETAUpdate with _$ETAUpdate {
  const ETAUpdate._();

  /// Creates a new ETA update
  const factory ETAUpdate({
    /// Estimated arrival time in minutes
    required int estimatedMinutes,

    /// Distance to destination in kilometers
    required double distanceKm,

    /// Timestamp of the ETA calculation
    required DateTime timestamp,

    /// Traffic conditions affecting ETA
    TrafficCondition? trafficCondition,
  }) = _ETAUpdate;

  /// Check if ETA update is recent (within last 60 seconds)
  bool get isRecent {
    final now = DateTime.now();
    final diff = now.difference(timestamp);
    return diff.inSeconds <= 60;
  }
}

/// Traffic conditions that may affect ETA
enum TrafficCondition {
  /// Light traffic, no delays expected
  light,

  /// Moderate traffic, minor delays possible
  moderate,

  /// Heavy traffic, significant delays expected
  heavy,

  /// Unknown traffic conditions
  unknown,
}
