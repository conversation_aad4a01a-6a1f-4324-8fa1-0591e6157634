// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ride_tracking_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$RideTrackingState {
  /// Current active ride being tracked
  Ride? get activeRide => throw _privateConstructorUsedError;

  /// Driver's current location
  LocationCoordinates? get driverLocation => throw _privateConstructorUsedError;

  /// Driver information
  DriverInfo? get driverInfo => throw _privateConstructorUsedError;

  /// Estimated time of arrival in minutes
  int? get estimatedArrivalMinutes => throw _privateConstructorUsedError;

  /// Distance to pickup/destination in kilometers
  double? get distanceKm => throw _privateConstructorUsedError;

  /// Trip progress information
  TripProgress? get tripProgress => throw _privateConstructorUsedError;

  /// Loading states
  bool get isLoadingRide => throw _privateConstructorUsedError;
  bool get isLoadingDriverLocation => throw _privateConstructorUsedError;
  bool get isUpdatingStatus => throw _privateConstructorUsedError;

  /// Error states
  String? get errorMessage => throw _privateConstructorUsedError;
  Map<String, String> get fieldErrors => throw _privateConstructorUsedError;

  /// Last update timestamp
  DateTime? get lastUpdated => throw _privateConstructorUsedError;

  /// Connection status for real-time updates
  ConnectionStatus get connectionStatus => throw _privateConstructorUsedError;

  /// Create a copy of RideTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RideTrackingStateCopyWith<RideTrackingState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RideTrackingStateCopyWith<$Res> {
  factory $RideTrackingStateCopyWith(
          RideTrackingState value, $Res Function(RideTrackingState) then) =
      _$RideTrackingStateCopyWithImpl<$Res, RideTrackingState>;
  @useResult
  $Res call(
      {Ride? activeRide,
      LocationCoordinates? driverLocation,
      DriverInfo? driverInfo,
      int? estimatedArrivalMinutes,
      double? distanceKm,
      TripProgress? tripProgress,
      bool isLoadingRide,
      bool isLoadingDriverLocation,
      bool isUpdatingStatus,
      String? errorMessage,
      Map<String, String> fieldErrors,
      DateTime? lastUpdated,
      ConnectionStatus connectionStatus});

  $RideCopyWith<$Res>? get activeRide;
  $LocationCoordinatesCopyWith<$Res>? get driverLocation;
  $DriverInfoCopyWith<$Res>? get driverInfo;
  $TripProgressCopyWith<$Res>? get tripProgress;
}

/// @nodoc
class _$RideTrackingStateCopyWithImpl<$Res, $Val extends RideTrackingState>
    implements $RideTrackingStateCopyWith<$Res> {
  _$RideTrackingStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RideTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? activeRide = freezed,
    Object? driverLocation = freezed,
    Object? driverInfo = freezed,
    Object? estimatedArrivalMinutes = freezed,
    Object? distanceKm = freezed,
    Object? tripProgress = freezed,
    Object? isLoadingRide = null,
    Object? isLoadingDriverLocation = null,
    Object? isUpdatingStatus = null,
    Object? errorMessage = freezed,
    Object? fieldErrors = null,
    Object? lastUpdated = freezed,
    Object? connectionStatus = null,
  }) {
    return _then(_value.copyWith(
      activeRide: freezed == activeRide
          ? _value.activeRide
          : activeRide // ignore: cast_nullable_to_non_nullable
              as Ride?,
      driverLocation: freezed == driverLocation
          ? _value.driverLocation
          : driverLocation // ignore: cast_nullable_to_non_nullable
              as LocationCoordinates?,
      driverInfo: freezed == driverInfo
          ? _value.driverInfo
          : driverInfo // ignore: cast_nullable_to_non_nullable
              as DriverInfo?,
      estimatedArrivalMinutes: freezed == estimatedArrivalMinutes
          ? _value.estimatedArrivalMinutes
          : estimatedArrivalMinutes // ignore: cast_nullable_to_non_nullable
              as int?,
      distanceKm: freezed == distanceKm
          ? _value.distanceKm
          : distanceKm // ignore: cast_nullable_to_non_nullable
              as double?,
      tripProgress: freezed == tripProgress
          ? _value.tripProgress
          : tripProgress // ignore: cast_nullable_to_non_nullable
              as TripProgress?,
      isLoadingRide: null == isLoadingRide
          ? _value.isLoadingRide
          : isLoadingRide // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingDriverLocation: null == isLoadingDriverLocation
          ? _value.isLoadingDriverLocation
          : isLoadingDriverLocation // ignore: cast_nullable_to_non_nullable
              as bool,
      isUpdatingStatus: null == isUpdatingStatus
          ? _value.isUpdatingStatus
          : isUpdatingStatus // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      fieldErrors: null == fieldErrors
          ? _value.fieldErrors
          : fieldErrors // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      lastUpdated: freezed == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      connectionStatus: null == connectionStatus
          ? _value.connectionStatus
          : connectionStatus // ignore: cast_nullable_to_non_nullable
              as ConnectionStatus,
    ) as $Val);
  }

  /// Create a copy of RideTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RideCopyWith<$Res>? get activeRide {
    if (_value.activeRide == null) {
      return null;
    }

    return $RideCopyWith<$Res>(_value.activeRide!, (value) {
      return _then(_value.copyWith(activeRide: value) as $Val);
    });
  }

  /// Create a copy of RideTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LocationCoordinatesCopyWith<$Res>? get driverLocation {
    if (_value.driverLocation == null) {
      return null;
    }

    return $LocationCoordinatesCopyWith<$Res>(_value.driverLocation!, (value) {
      return _then(_value.copyWith(driverLocation: value) as $Val);
    });
  }

  /// Create a copy of RideTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DriverInfoCopyWith<$Res>? get driverInfo {
    if (_value.driverInfo == null) {
      return null;
    }

    return $DriverInfoCopyWith<$Res>(_value.driverInfo!, (value) {
      return _then(_value.copyWith(driverInfo: value) as $Val);
    });
  }

  /// Create a copy of RideTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TripProgressCopyWith<$Res>? get tripProgress {
    if (_value.tripProgress == null) {
      return null;
    }

    return $TripProgressCopyWith<$Res>(_value.tripProgress!, (value) {
      return _then(_value.copyWith(tripProgress: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$RideTrackingStateImplCopyWith<$Res>
    implements $RideTrackingStateCopyWith<$Res> {
  factory _$$RideTrackingStateImplCopyWith(_$RideTrackingStateImpl value,
          $Res Function(_$RideTrackingStateImpl) then) =
      __$$RideTrackingStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Ride? activeRide,
      LocationCoordinates? driverLocation,
      DriverInfo? driverInfo,
      int? estimatedArrivalMinutes,
      double? distanceKm,
      TripProgress? tripProgress,
      bool isLoadingRide,
      bool isLoadingDriverLocation,
      bool isUpdatingStatus,
      String? errorMessage,
      Map<String, String> fieldErrors,
      DateTime? lastUpdated,
      ConnectionStatus connectionStatus});

  @override
  $RideCopyWith<$Res>? get activeRide;
  @override
  $LocationCoordinatesCopyWith<$Res>? get driverLocation;
  @override
  $DriverInfoCopyWith<$Res>? get driverInfo;
  @override
  $TripProgressCopyWith<$Res>? get tripProgress;
}

/// @nodoc
class __$$RideTrackingStateImplCopyWithImpl<$Res>
    extends _$RideTrackingStateCopyWithImpl<$Res, _$RideTrackingStateImpl>
    implements _$$RideTrackingStateImplCopyWith<$Res> {
  __$$RideTrackingStateImplCopyWithImpl(_$RideTrackingStateImpl _value,
      $Res Function(_$RideTrackingStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of RideTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? activeRide = freezed,
    Object? driverLocation = freezed,
    Object? driverInfo = freezed,
    Object? estimatedArrivalMinutes = freezed,
    Object? distanceKm = freezed,
    Object? tripProgress = freezed,
    Object? isLoadingRide = null,
    Object? isLoadingDriverLocation = null,
    Object? isUpdatingStatus = null,
    Object? errorMessage = freezed,
    Object? fieldErrors = null,
    Object? lastUpdated = freezed,
    Object? connectionStatus = null,
  }) {
    return _then(_$RideTrackingStateImpl(
      activeRide: freezed == activeRide
          ? _value.activeRide
          : activeRide // ignore: cast_nullable_to_non_nullable
              as Ride?,
      driverLocation: freezed == driverLocation
          ? _value.driverLocation
          : driverLocation // ignore: cast_nullable_to_non_nullable
              as LocationCoordinates?,
      driverInfo: freezed == driverInfo
          ? _value.driverInfo
          : driverInfo // ignore: cast_nullable_to_non_nullable
              as DriverInfo?,
      estimatedArrivalMinutes: freezed == estimatedArrivalMinutes
          ? _value.estimatedArrivalMinutes
          : estimatedArrivalMinutes // ignore: cast_nullable_to_non_nullable
              as int?,
      distanceKm: freezed == distanceKm
          ? _value.distanceKm
          : distanceKm // ignore: cast_nullable_to_non_nullable
              as double?,
      tripProgress: freezed == tripProgress
          ? _value.tripProgress
          : tripProgress // ignore: cast_nullable_to_non_nullable
              as TripProgress?,
      isLoadingRide: null == isLoadingRide
          ? _value.isLoadingRide
          : isLoadingRide // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingDriverLocation: null == isLoadingDriverLocation
          ? _value.isLoadingDriverLocation
          : isLoadingDriverLocation // ignore: cast_nullable_to_non_nullable
              as bool,
      isUpdatingStatus: null == isUpdatingStatus
          ? _value.isUpdatingStatus
          : isUpdatingStatus // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      fieldErrors: null == fieldErrors
          ? _value._fieldErrors
          : fieldErrors // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      lastUpdated: freezed == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      connectionStatus: null == connectionStatus
          ? _value.connectionStatus
          : connectionStatus // ignore: cast_nullable_to_non_nullable
              as ConnectionStatus,
    ));
  }
}

/// @nodoc

class _$RideTrackingStateImpl extends _RideTrackingState {
  const _$RideTrackingStateImpl(
      {this.activeRide,
      this.driverLocation,
      this.driverInfo,
      this.estimatedArrivalMinutes,
      this.distanceKm,
      this.tripProgress,
      this.isLoadingRide = false,
      this.isLoadingDriverLocation = false,
      this.isUpdatingStatus = false,
      this.errorMessage,
      final Map<String, String> fieldErrors = const {},
      this.lastUpdated,
      this.connectionStatus = ConnectionStatus.disconnected})
      : _fieldErrors = fieldErrors,
        super._();

  /// Current active ride being tracked
  @override
  final Ride? activeRide;

  /// Driver's current location
  @override
  final LocationCoordinates? driverLocation;

  /// Driver information
  @override
  final DriverInfo? driverInfo;

  /// Estimated time of arrival in minutes
  @override
  final int? estimatedArrivalMinutes;

  /// Distance to pickup/destination in kilometers
  @override
  final double? distanceKm;

  /// Trip progress information
  @override
  final TripProgress? tripProgress;

  /// Loading states
  @override
  @JsonKey()
  final bool isLoadingRide;
  @override
  @JsonKey()
  final bool isLoadingDriverLocation;
  @override
  @JsonKey()
  final bool isUpdatingStatus;

  /// Error states
  @override
  final String? errorMessage;
  final Map<String, String> _fieldErrors;
  @override
  @JsonKey()
  Map<String, String> get fieldErrors {
    if (_fieldErrors is EqualUnmodifiableMapView) return _fieldErrors;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_fieldErrors);
  }

  /// Last update timestamp
  @override
  final DateTime? lastUpdated;

  /// Connection status for real-time updates
  @override
  @JsonKey()
  final ConnectionStatus connectionStatus;

  @override
  String toString() {
    return 'RideTrackingState(activeRide: $activeRide, driverLocation: $driverLocation, driverInfo: $driverInfo, estimatedArrivalMinutes: $estimatedArrivalMinutes, distanceKm: $distanceKm, tripProgress: $tripProgress, isLoadingRide: $isLoadingRide, isLoadingDriverLocation: $isLoadingDriverLocation, isUpdatingStatus: $isUpdatingStatus, errorMessage: $errorMessage, fieldErrors: $fieldErrors, lastUpdated: $lastUpdated, connectionStatus: $connectionStatus)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RideTrackingStateImpl &&
            (identical(other.activeRide, activeRide) ||
                other.activeRide == activeRide) &&
            (identical(other.driverLocation, driverLocation) ||
                other.driverLocation == driverLocation) &&
            (identical(other.driverInfo, driverInfo) ||
                other.driverInfo == driverInfo) &&
            (identical(
                    other.estimatedArrivalMinutes, estimatedArrivalMinutes) ||
                other.estimatedArrivalMinutes == estimatedArrivalMinutes) &&
            (identical(other.distanceKm, distanceKm) ||
                other.distanceKm == distanceKm) &&
            (identical(other.tripProgress, tripProgress) ||
                other.tripProgress == tripProgress) &&
            (identical(other.isLoadingRide, isLoadingRide) ||
                other.isLoadingRide == isLoadingRide) &&
            (identical(
                    other.isLoadingDriverLocation, isLoadingDriverLocation) ||
                other.isLoadingDriverLocation == isLoadingDriverLocation) &&
            (identical(other.isUpdatingStatus, isUpdatingStatus) ||
                other.isUpdatingStatus == isUpdatingStatus) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            const DeepCollectionEquality()
                .equals(other._fieldErrors, _fieldErrors) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated) &&
            (identical(other.connectionStatus, connectionStatus) ||
                other.connectionStatus == connectionStatus));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      activeRide,
      driverLocation,
      driverInfo,
      estimatedArrivalMinutes,
      distanceKm,
      tripProgress,
      isLoadingRide,
      isLoadingDriverLocation,
      isUpdatingStatus,
      errorMessage,
      const DeepCollectionEquality().hash(_fieldErrors),
      lastUpdated,
      connectionStatus);

  /// Create a copy of RideTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RideTrackingStateImplCopyWith<_$RideTrackingStateImpl> get copyWith =>
      __$$RideTrackingStateImplCopyWithImpl<_$RideTrackingStateImpl>(
          this, _$identity);
}

abstract class _RideTrackingState extends RideTrackingState {
  const factory _RideTrackingState(
      {final Ride? activeRide,
      final LocationCoordinates? driverLocation,
      final DriverInfo? driverInfo,
      final int? estimatedArrivalMinutes,
      final double? distanceKm,
      final TripProgress? tripProgress,
      final bool isLoadingRide,
      final bool isLoadingDriverLocation,
      final bool isUpdatingStatus,
      final String? errorMessage,
      final Map<String, String> fieldErrors,
      final DateTime? lastUpdated,
      final ConnectionStatus connectionStatus}) = _$RideTrackingStateImpl;
  const _RideTrackingState._() : super._();

  /// Current active ride being tracked
  @override
  Ride? get activeRide;

  /// Driver's current location
  @override
  LocationCoordinates? get driverLocation;

  /// Driver information
  @override
  DriverInfo? get driverInfo;

  /// Estimated time of arrival in minutes
  @override
  int? get estimatedArrivalMinutes;

  /// Distance to pickup/destination in kilometers
  @override
  double? get distanceKm;

  /// Trip progress information
  @override
  TripProgress? get tripProgress;

  /// Loading states
  @override
  bool get isLoadingRide;
  @override
  bool get isLoadingDriverLocation;
  @override
  bool get isUpdatingStatus;

  /// Error states
  @override
  String? get errorMessage;
  @override
  Map<String, String> get fieldErrors;

  /// Last update timestamp
  @override
  DateTime? get lastUpdated;

  /// Connection status for real-time updates
  @override
  ConnectionStatus get connectionStatus;

  /// Create a copy of RideTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RideTrackingStateImplCopyWith<_$RideTrackingStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$DriverInfo {
  /// Driver's unique identifier
  String get id => throw _privateConstructorUsedError;

  /// Driver's name
  String get name => throw _privateConstructorUsedError;

  /// Driver's phone number (optional)
  String? get phoneNumber => throw _privateConstructorUsedError;

  /// Driver's rating (1-5 stars)
  double? get rating => throw _privateConstructorUsedError;

  /// Number of completed trips
  int? get totalTrips => throw _privateConstructorUsedError;

  /// Vehicle information
  VehicleInfo? get vehicleInfo => throw _privateConstructorUsedError;

  /// Driver's profile photo URL (optional)
  String? get photoUrl => throw _privateConstructorUsedError;

  /// Create a copy of DriverInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DriverInfoCopyWith<DriverInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DriverInfoCopyWith<$Res> {
  factory $DriverInfoCopyWith(
          DriverInfo value, $Res Function(DriverInfo) then) =
      _$DriverInfoCopyWithImpl<$Res, DriverInfo>;
  @useResult
  $Res call(
      {String id,
      String name,
      String? phoneNumber,
      double? rating,
      int? totalTrips,
      VehicleInfo? vehicleInfo,
      String? photoUrl});

  $VehicleInfoCopyWith<$Res>? get vehicleInfo;
}

/// @nodoc
class _$DriverInfoCopyWithImpl<$Res, $Val extends DriverInfo>
    implements $DriverInfoCopyWith<$Res> {
  _$DriverInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DriverInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? phoneNumber = freezed,
    Object? rating = freezed,
    Object? totalTrips = freezed,
    Object? vehicleInfo = freezed,
    Object? photoUrl = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      rating: freezed == rating
          ? _value.rating
          : rating // ignore: cast_nullable_to_non_nullable
              as double?,
      totalTrips: freezed == totalTrips
          ? _value.totalTrips
          : totalTrips // ignore: cast_nullable_to_non_nullable
              as int?,
      vehicleInfo: freezed == vehicleInfo
          ? _value.vehicleInfo
          : vehicleInfo // ignore: cast_nullable_to_non_nullable
              as VehicleInfo?,
      photoUrl: freezed == photoUrl
          ? _value.photoUrl
          : photoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of DriverInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $VehicleInfoCopyWith<$Res>? get vehicleInfo {
    if (_value.vehicleInfo == null) {
      return null;
    }

    return $VehicleInfoCopyWith<$Res>(_value.vehicleInfo!, (value) {
      return _then(_value.copyWith(vehicleInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$DriverInfoImplCopyWith<$Res>
    implements $DriverInfoCopyWith<$Res> {
  factory _$$DriverInfoImplCopyWith(
          _$DriverInfoImpl value, $Res Function(_$DriverInfoImpl) then) =
      __$$DriverInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      String? phoneNumber,
      double? rating,
      int? totalTrips,
      VehicleInfo? vehicleInfo,
      String? photoUrl});

  @override
  $VehicleInfoCopyWith<$Res>? get vehicleInfo;
}

/// @nodoc
class __$$DriverInfoImplCopyWithImpl<$Res>
    extends _$DriverInfoCopyWithImpl<$Res, _$DriverInfoImpl>
    implements _$$DriverInfoImplCopyWith<$Res> {
  __$$DriverInfoImplCopyWithImpl(
      _$DriverInfoImpl _value, $Res Function(_$DriverInfoImpl) _then)
      : super(_value, _then);

  /// Create a copy of DriverInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? phoneNumber = freezed,
    Object? rating = freezed,
    Object? totalTrips = freezed,
    Object? vehicleInfo = freezed,
    Object? photoUrl = freezed,
  }) {
    return _then(_$DriverInfoImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      rating: freezed == rating
          ? _value.rating
          : rating // ignore: cast_nullable_to_non_nullable
              as double?,
      totalTrips: freezed == totalTrips
          ? _value.totalTrips
          : totalTrips // ignore: cast_nullable_to_non_nullable
              as int?,
      vehicleInfo: freezed == vehicleInfo
          ? _value.vehicleInfo
          : vehicleInfo // ignore: cast_nullable_to_non_nullable
              as VehicleInfo?,
      photoUrl: freezed == photoUrl
          ? _value.photoUrl
          : photoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$DriverInfoImpl extends _DriverInfo {
  const _$DriverInfoImpl(
      {required this.id,
      required this.name,
      this.phoneNumber,
      this.rating,
      this.totalTrips,
      this.vehicleInfo,
      this.photoUrl})
      : super._();

  /// Driver's unique identifier
  @override
  final String id;

  /// Driver's name
  @override
  final String name;

  /// Driver's phone number (optional)
  @override
  final String? phoneNumber;

  /// Driver's rating (1-5 stars)
  @override
  final double? rating;

  /// Number of completed trips
  @override
  final int? totalTrips;

  /// Vehicle information
  @override
  final VehicleInfo? vehicleInfo;

  /// Driver's profile photo URL (optional)
  @override
  final String? photoUrl;

  @override
  String toString() {
    return 'DriverInfo(id: $id, name: $name, phoneNumber: $phoneNumber, rating: $rating, totalTrips: $totalTrips, vehicleInfo: $vehicleInfo, photoUrl: $photoUrl)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DriverInfoImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.rating, rating) || other.rating == rating) &&
            (identical(other.totalTrips, totalTrips) ||
                other.totalTrips == totalTrips) &&
            (identical(other.vehicleInfo, vehicleInfo) ||
                other.vehicleInfo == vehicleInfo) &&
            (identical(other.photoUrl, photoUrl) ||
                other.photoUrl == photoUrl));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id, name, phoneNumber, rating,
      totalTrips, vehicleInfo, photoUrl);

  /// Create a copy of DriverInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DriverInfoImplCopyWith<_$DriverInfoImpl> get copyWith =>
      __$$DriverInfoImplCopyWithImpl<_$DriverInfoImpl>(this, _$identity);
}

abstract class _DriverInfo extends DriverInfo {
  const factory _DriverInfo(
      {required final String id,
      required final String name,
      final String? phoneNumber,
      final double? rating,
      final int? totalTrips,
      final VehicleInfo? vehicleInfo,
      final String? photoUrl}) = _$DriverInfoImpl;
  const _DriverInfo._() : super._();

  /// Driver's unique identifier
  @override
  String get id;

  /// Driver's name
  @override
  String get name;

  /// Driver's phone number (optional)
  @override
  String? get phoneNumber;

  /// Driver's rating (1-5 stars)
  @override
  double? get rating;

  /// Number of completed trips
  @override
  int? get totalTrips;

  /// Vehicle information
  @override
  VehicleInfo? get vehicleInfo;

  /// Driver's profile photo URL (optional)
  @override
  String? get photoUrl;

  /// Create a copy of DriverInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DriverInfoImplCopyWith<_$DriverInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$VehicleInfo {
  /// Vehicle make (e.g., Toyota)
  String get make => throw _privateConstructorUsedError;

  /// Vehicle model (e.g., Camry)
  String get model => throw _privateConstructorUsedError;

  /// Vehicle year
  int? get year => throw _privateConstructorUsedError;

  /// Vehicle color
  String get color => throw _privateConstructorUsedError;

  /// License plate number
  String get licensePlate => throw _privateConstructorUsedError;

  /// Vehicle type (sedan, suv, etc.)
  String? get vehicleType => throw _privateConstructorUsedError;

  /// Create a copy of VehicleInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VehicleInfoCopyWith<VehicleInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VehicleInfoCopyWith<$Res> {
  factory $VehicleInfoCopyWith(
          VehicleInfo value, $Res Function(VehicleInfo) then) =
      _$VehicleInfoCopyWithImpl<$Res, VehicleInfo>;
  @useResult
  $Res call(
      {String make,
      String model,
      int? year,
      String color,
      String licensePlate,
      String? vehicleType});
}

/// @nodoc
class _$VehicleInfoCopyWithImpl<$Res, $Val extends VehicleInfo>
    implements $VehicleInfoCopyWith<$Res> {
  _$VehicleInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VehicleInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? make = null,
    Object? model = null,
    Object? year = freezed,
    Object? color = null,
    Object? licensePlate = null,
    Object? vehicleType = freezed,
  }) {
    return _then(_value.copyWith(
      make: null == make
          ? _value.make
          : make // ignore: cast_nullable_to_non_nullable
              as String,
      model: null == model
          ? _value.model
          : model // ignore: cast_nullable_to_non_nullable
              as String,
      year: freezed == year
          ? _value.year
          : year // ignore: cast_nullable_to_non_nullable
              as int?,
      color: null == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as String,
      licensePlate: null == licensePlate
          ? _value.licensePlate
          : licensePlate // ignore: cast_nullable_to_non_nullable
              as String,
      vehicleType: freezed == vehicleType
          ? _value.vehicleType
          : vehicleType // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VehicleInfoImplCopyWith<$Res>
    implements $VehicleInfoCopyWith<$Res> {
  factory _$$VehicleInfoImplCopyWith(
          _$VehicleInfoImpl value, $Res Function(_$VehicleInfoImpl) then) =
      __$$VehicleInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String make,
      String model,
      int? year,
      String color,
      String licensePlate,
      String? vehicleType});
}

/// @nodoc
class __$$VehicleInfoImplCopyWithImpl<$Res>
    extends _$VehicleInfoCopyWithImpl<$Res, _$VehicleInfoImpl>
    implements _$$VehicleInfoImplCopyWith<$Res> {
  __$$VehicleInfoImplCopyWithImpl(
      _$VehicleInfoImpl _value, $Res Function(_$VehicleInfoImpl) _then)
      : super(_value, _then);

  /// Create a copy of VehicleInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? make = null,
    Object? model = null,
    Object? year = freezed,
    Object? color = null,
    Object? licensePlate = null,
    Object? vehicleType = freezed,
  }) {
    return _then(_$VehicleInfoImpl(
      make: null == make
          ? _value.make
          : make // ignore: cast_nullable_to_non_nullable
              as String,
      model: null == model
          ? _value.model
          : model // ignore: cast_nullable_to_non_nullable
              as String,
      year: freezed == year
          ? _value.year
          : year // ignore: cast_nullable_to_non_nullable
              as int?,
      color: null == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as String,
      licensePlate: null == licensePlate
          ? _value.licensePlate
          : licensePlate // ignore: cast_nullable_to_non_nullable
              as String,
      vehicleType: freezed == vehicleType
          ? _value.vehicleType
          : vehicleType // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$VehicleInfoImpl extends _VehicleInfo {
  const _$VehicleInfoImpl(
      {required this.make,
      required this.model,
      this.year,
      required this.color,
      required this.licensePlate,
      this.vehicleType})
      : super._();

  /// Vehicle make (e.g., Toyota)
  @override
  final String make;

  /// Vehicle model (e.g., Camry)
  @override
  final String model;

  /// Vehicle year
  @override
  final int? year;

  /// Vehicle color
  @override
  final String color;

  /// License plate number
  @override
  final String licensePlate;

  /// Vehicle type (sedan, suv, etc.)
  @override
  final String? vehicleType;

  @override
  String toString() {
    return 'VehicleInfo(make: $make, model: $model, year: $year, color: $color, licensePlate: $licensePlate, vehicleType: $vehicleType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VehicleInfoImpl &&
            (identical(other.make, make) || other.make == make) &&
            (identical(other.model, model) || other.model == model) &&
            (identical(other.year, year) || other.year == year) &&
            (identical(other.color, color) || other.color == color) &&
            (identical(other.licensePlate, licensePlate) ||
                other.licensePlate == licensePlate) &&
            (identical(other.vehicleType, vehicleType) ||
                other.vehicleType == vehicleType));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, make, model, year, color, licensePlate, vehicleType);

  /// Create a copy of VehicleInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VehicleInfoImplCopyWith<_$VehicleInfoImpl> get copyWith =>
      __$$VehicleInfoImplCopyWithImpl<_$VehicleInfoImpl>(this, _$identity);
}

abstract class _VehicleInfo extends VehicleInfo {
  const factory _VehicleInfo(
      {required final String make,
      required final String model,
      final int? year,
      required final String color,
      required final String licensePlate,
      final String? vehicleType}) = _$VehicleInfoImpl;
  const _VehicleInfo._() : super._();

  /// Vehicle make (e.g., Toyota)
  @override
  String get make;

  /// Vehicle model (e.g., Camry)
  @override
  String get model;

  /// Vehicle year
  @override
  int? get year;

  /// Vehicle color
  @override
  String get color;

  /// License plate number
  @override
  String get licensePlate;

  /// Vehicle type (sedan, suv, etc.)
  @override
  String? get vehicleType;

  /// Create a copy of VehicleInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VehicleInfoImplCopyWith<_$VehicleInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$TripProgress {
  /// Total distance of the trip in kilometers
  double get totalDistanceKm => throw _privateConstructorUsedError;

  /// Distance completed so far in kilometers
  double get completedDistanceKm => throw _privateConstructorUsedError;

  /// Estimated total duration in minutes
  int get estimatedDurationMinutes => throw _privateConstructorUsedError;

  /// Time elapsed since trip started in minutes
  int get elapsedMinutes => throw _privateConstructorUsedError;

  /// Current phase of the trip
  TripPhase get currentPhase => throw _privateConstructorUsedError;

  /// Percentage of trip completed (0-100)
  double get progressPercentage => throw _privateConstructorUsedError;

  /// Create a copy of TripProgress
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TripProgressCopyWith<TripProgress> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TripProgressCopyWith<$Res> {
  factory $TripProgressCopyWith(
          TripProgress value, $Res Function(TripProgress) then) =
      _$TripProgressCopyWithImpl<$Res, TripProgress>;
  @useResult
  $Res call(
      {double totalDistanceKm,
      double completedDistanceKm,
      int estimatedDurationMinutes,
      int elapsedMinutes,
      TripPhase currentPhase,
      double progressPercentage});
}

/// @nodoc
class _$TripProgressCopyWithImpl<$Res, $Val extends TripProgress>
    implements $TripProgressCopyWith<$Res> {
  _$TripProgressCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TripProgress
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalDistanceKm = null,
    Object? completedDistanceKm = null,
    Object? estimatedDurationMinutes = null,
    Object? elapsedMinutes = null,
    Object? currentPhase = null,
    Object? progressPercentage = null,
  }) {
    return _then(_value.copyWith(
      totalDistanceKm: null == totalDistanceKm
          ? _value.totalDistanceKm
          : totalDistanceKm // ignore: cast_nullable_to_non_nullable
              as double,
      completedDistanceKm: null == completedDistanceKm
          ? _value.completedDistanceKm
          : completedDistanceKm // ignore: cast_nullable_to_non_nullable
              as double,
      estimatedDurationMinutes: null == estimatedDurationMinutes
          ? _value.estimatedDurationMinutes
          : estimatedDurationMinutes // ignore: cast_nullable_to_non_nullable
              as int,
      elapsedMinutes: null == elapsedMinutes
          ? _value.elapsedMinutes
          : elapsedMinutes // ignore: cast_nullable_to_non_nullable
              as int,
      currentPhase: null == currentPhase
          ? _value.currentPhase
          : currentPhase // ignore: cast_nullable_to_non_nullable
              as TripPhase,
      progressPercentage: null == progressPercentage
          ? _value.progressPercentage
          : progressPercentage // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TripProgressImplCopyWith<$Res>
    implements $TripProgressCopyWith<$Res> {
  factory _$$TripProgressImplCopyWith(
          _$TripProgressImpl value, $Res Function(_$TripProgressImpl) then) =
      __$$TripProgressImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double totalDistanceKm,
      double completedDistanceKm,
      int estimatedDurationMinutes,
      int elapsedMinutes,
      TripPhase currentPhase,
      double progressPercentage});
}

/// @nodoc
class __$$TripProgressImplCopyWithImpl<$Res>
    extends _$TripProgressCopyWithImpl<$Res, _$TripProgressImpl>
    implements _$$TripProgressImplCopyWith<$Res> {
  __$$TripProgressImplCopyWithImpl(
      _$TripProgressImpl _value, $Res Function(_$TripProgressImpl) _then)
      : super(_value, _then);

  /// Create a copy of TripProgress
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalDistanceKm = null,
    Object? completedDistanceKm = null,
    Object? estimatedDurationMinutes = null,
    Object? elapsedMinutes = null,
    Object? currentPhase = null,
    Object? progressPercentage = null,
  }) {
    return _then(_$TripProgressImpl(
      totalDistanceKm: null == totalDistanceKm
          ? _value.totalDistanceKm
          : totalDistanceKm // ignore: cast_nullable_to_non_nullable
              as double,
      completedDistanceKm: null == completedDistanceKm
          ? _value.completedDistanceKm
          : completedDistanceKm // ignore: cast_nullable_to_non_nullable
              as double,
      estimatedDurationMinutes: null == estimatedDurationMinutes
          ? _value.estimatedDurationMinutes
          : estimatedDurationMinutes // ignore: cast_nullable_to_non_nullable
              as int,
      elapsedMinutes: null == elapsedMinutes
          ? _value.elapsedMinutes
          : elapsedMinutes // ignore: cast_nullable_to_non_nullable
              as int,
      currentPhase: null == currentPhase
          ? _value.currentPhase
          : currentPhase // ignore: cast_nullable_to_non_nullable
              as TripPhase,
      progressPercentage: null == progressPercentage
          ? _value.progressPercentage
          : progressPercentage // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc

class _$TripProgressImpl extends _TripProgress {
  const _$TripProgressImpl(
      {required this.totalDistanceKm,
      required this.completedDistanceKm,
      required this.estimatedDurationMinutes,
      required this.elapsedMinutes,
      required this.currentPhase,
      this.progressPercentage = 0.0})
      : super._();

  /// Total distance of the trip in kilometers
  @override
  final double totalDistanceKm;

  /// Distance completed so far in kilometers
  @override
  final double completedDistanceKm;

  /// Estimated total duration in minutes
  @override
  final int estimatedDurationMinutes;

  /// Time elapsed since trip started in minutes
  @override
  final int elapsedMinutes;

  /// Current phase of the trip
  @override
  final TripPhase currentPhase;

  /// Percentage of trip completed (0-100)
  @override
  @JsonKey()
  final double progressPercentage;

  @override
  String toString() {
    return 'TripProgress(totalDistanceKm: $totalDistanceKm, completedDistanceKm: $completedDistanceKm, estimatedDurationMinutes: $estimatedDurationMinutes, elapsedMinutes: $elapsedMinutes, currentPhase: $currentPhase, progressPercentage: $progressPercentage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TripProgressImpl &&
            (identical(other.totalDistanceKm, totalDistanceKm) ||
                other.totalDistanceKm == totalDistanceKm) &&
            (identical(other.completedDistanceKm, completedDistanceKm) ||
                other.completedDistanceKm == completedDistanceKm) &&
            (identical(
                    other.estimatedDurationMinutes, estimatedDurationMinutes) ||
                other.estimatedDurationMinutes == estimatedDurationMinutes) &&
            (identical(other.elapsedMinutes, elapsedMinutes) ||
                other.elapsedMinutes == elapsedMinutes) &&
            (identical(other.currentPhase, currentPhase) ||
                other.currentPhase == currentPhase) &&
            (identical(other.progressPercentage, progressPercentage) ||
                other.progressPercentage == progressPercentage));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      totalDistanceKm,
      completedDistanceKm,
      estimatedDurationMinutes,
      elapsedMinutes,
      currentPhase,
      progressPercentage);

  /// Create a copy of TripProgress
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TripProgressImplCopyWith<_$TripProgressImpl> get copyWith =>
      __$$TripProgressImplCopyWithImpl<_$TripProgressImpl>(this, _$identity);
}

abstract class _TripProgress extends TripProgress {
  const factory _TripProgress(
      {required final double totalDistanceKm,
      required final double completedDistanceKm,
      required final int estimatedDurationMinutes,
      required final int elapsedMinutes,
      required final TripPhase currentPhase,
      final double progressPercentage}) = _$TripProgressImpl;
  const _TripProgress._() : super._();

  /// Total distance of the trip in kilometers
  @override
  double get totalDistanceKm;

  /// Distance completed so far in kilometers
  @override
  double get completedDistanceKm;

  /// Estimated total duration in minutes
  @override
  int get estimatedDurationMinutes;

  /// Time elapsed since trip started in minutes
  @override
  int get elapsedMinutes;

  /// Current phase of the trip
  @override
  TripPhase get currentPhase;

  /// Percentage of trip completed (0-100)
  @override
  double get progressPercentage;

  /// Create a copy of TripProgress
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TripProgressImplCopyWith<_$TripProgressImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$DriverLocationUpdate {
  /// Driver's current location
  LocationCoordinates get location => throw _privateConstructorUsedError;

  /// Timestamp of the location update
  DateTime get timestamp => throw _privateConstructorUsedError;

  /// Driver's current heading/bearing in degrees (0-360)
  double? get bearing => throw _privateConstructorUsedError;

  /// Driver's current speed in km/h
  double? get speedKmh => throw _privateConstructorUsedError;

  /// Accuracy of the location in meters
  double? get accuracy => throw _privateConstructorUsedError;

  /// Create a copy of DriverLocationUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DriverLocationUpdateCopyWith<DriverLocationUpdate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DriverLocationUpdateCopyWith<$Res> {
  factory $DriverLocationUpdateCopyWith(DriverLocationUpdate value,
          $Res Function(DriverLocationUpdate) then) =
      _$DriverLocationUpdateCopyWithImpl<$Res, DriverLocationUpdate>;
  @useResult
  $Res call(
      {LocationCoordinates location,
      DateTime timestamp,
      double? bearing,
      double? speedKmh,
      double? accuracy});

  $LocationCoordinatesCopyWith<$Res> get location;
}

/// @nodoc
class _$DriverLocationUpdateCopyWithImpl<$Res,
        $Val extends DriverLocationUpdate>
    implements $DriverLocationUpdateCopyWith<$Res> {
  _$DriverLocationUpdateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DriverLocationUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? location = null,
    Object? timestamp = null,
    Object? bearing = freezed,
    Object? speedKmh = freezed,
    Object? accuracy = freezed,
  }) {
    return _then(_value.copyWith(
      location: null == location
          ? _value.location
          : location // ignore: cast_nullable_to_non_nullable
              as LocationCoordinates,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime,
      bearing: freezed == bearing
          ? _value.bearing
          : bearing // ignore: cast_nullable_to_non_nullable
              as double?,
      speedKmh: freezed == speedKmh
          ? _value.speedKmh
          : speedKmh // ignore: cast_nullable_to_non_nullable
              as double?,
      accuracy: freezed == accuracy
          ? _value.accuracy
          : accuracy // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }

  /// Create a copy of DriverLocationUpdate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LocationCoordinatesCopyWith<$Res> get location {
    return $LocationCoordinatesCopyWith<$Res>(_value.location, (value) {
      return _then(_value.copyWith(location: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$DriverLocationUpdateImplCopyWith<$Res>
    implements $DriverLocationUpdateCopyWith<$Res> {
  factory _$$DriverLocationUpdateImplCopyWith(_$DriverLocationUpdateImpl value,
          $Res Function(_$DriverLocationUpdateImpl) then) =
      __$$DriverLocationUpdateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {LocationCoordinates location,
      DateTime timestamp,
      double? bearing,
      double? speedKmh,
      double? accuracy});

  @override
  $LocationCoordinatesCopyWith<$Res> get location;
}

/// @nodoc
class __$$DriverLocationUpdateImplCopyWithImpl<$Res>
    extends _$DriverLocationUpdateCopyWithImpl<$Res, _$DriverLocationUpdateImpl>
    implements _$$DriverLocationUpdateImplCopyWith<$Res> {
  __$$DriverLocationUpdateImplCopyWithImpl(_$DriverLocationUpdateImpl _value,
      $Res Function(_$DriverLocationUpdateImpl) _then)
      : super(_value, _then);

  /// Create a copy of DriverLocationUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? location = null,
    Object? timestamp = null,
    Object? bearing = freezed,
    Object? speedKmh = freezed,
    Object? accuracy = freezed,
  }) {
    return _then(_$DriverLocationUpdateImpl(
      location: null == location
          ? _value.location
          : location // ignore: cast_nullable_to_non_nullable
              as LocationCoordinates,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime,
      bearing: freezed == bearing
          ? _value.bearing
          : bearing // ignore: cast_nullable_to_non_nullable
              as double?,
      speedKmh: freezed == speedKmh
          ? _value.speedKmh
          : speedKmh // ignore: cast_nullable_to_non_nullable
              as double?,
      accuracy: freezed == accuracy
          ? _value.accuracy
          : accuracy // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc

class _$DriverLocationUpdateImpl extends _DriverLocationUpdate {
  const _$DriverLocationUpdateImpl(
      {required this.location,
      required this.timestamp,
      this.bearing,
      this.speedKmh,
      this.accuracy})
      : super._();

  /// Driver's current location
  @override
  final LocationCoordinates location;

  /// Timestamp of the location update
  @override
  final DateTime timestamp;

  /// Driver's current heading/bearing in degrees (0-360)
  @override
  final double? bearing;

  /// Driver's current speed in km/h
  @override
  final double? speedKmh;

  /// Accuracy of the location in meters
  @override
  final double? accuracy;

  @override
  String toString() {
    return 'DriverLocationUpdate(location: $location, timestamp: $timestamp, bearing: $bearing, speedKmh: $speedKmh, accuracy: $accuracy)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DriverLocationUpdateImpl &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.bearing, bearing) || other.bearing == bearing) &&
            (identical(other.speedKmh, speedKmh) ||
                other.speedKmh == speedKmh) &&
            (identical(other.accuracy, accuracy) ||
                other.accuracy == accuracy));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, location, timestamp, bearing, speedKmh, accuracy);

  /// Create a copy of DriverLocationUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DriverLocationUpdateImplCopyWith<_$DriverLocationUpdateImpl>
      get copyWith =>
          __$$DriverLocationUpdateImplCopyWithImpl<_$DriverLocationUpdateImpl>(
              this, _$identity);
}

abstract class _DriverLocationUpdate extends DriverLocationUpdate {
  const factory _DriverLocationUpdate(
      {required final LocationCoordinates location,
      required final DateTime timestamp,
      final double? bearing,
      final double? speedKmh,
      final double? accuracy}) = _$DriverLocationUpdateImpl;
  const _DriverLocationUpdate._() : super._();

  /// Driver's current location
  @override
  LocationCoordinates get location;

  /// Timestamp of the location update
  @override
  DateTime get timestamp;

  /// Driver's current heading/bearing in degrees (0-360)
  @override
  double? get bearing;

  /// Driver's current speed in km/h
  @override
  double? get speedKmh;

  /// Accuracy of the location in meters
  @override
  double? get accuracy;

  /// Create a copy of DriverLocationUpdate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DriverLocationUpdateImplCopyWith<_$DriverLocationUpdateImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$ETAUpdate {
  /// Estimated arrival time in minutes
  int get estimatedMinutes => throw _privateConstructorUsedError;

  /// Distance to destination in kilometers
  double get distanceKm => throw _privateConstructorUsedError;

  /// Timestamp of the ETA calculation
  DateTime get timestamp => throw _privateConstructorUsedError;

  /// Traffic conditions affecting ETA
  TrafficCondition? get trafficCondition => throw _privateConstructorUsedError;

  /// Create a copy of ETAUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ETAUpdateCopyWith<ETAUpdate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ETAUpdateCopyWith<$Res> {
  factory $ETAUpdateCopyWith(ETAUpdate value, $Res Function(ETAUpdate) then) =
      _$ETAUpdateCopyWithImpl<$Res, ETAUpdate>;
  @useResult
  $Res call(
      {int estimatedMinutes,
      double distanceKm,
      DateTime timestamp,
      TrafficCondition? trafficCondition});
}

/// @nodoc
class _$ETAUpdateCopyWithImpl<$Res, $Val extends ETAUpdate>
    implements $ETAUpdateCopyWith<$Res> {
  _$ETAUpdateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ETAUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? estimatedMinutes = null,
    Object? distanceKm = null,
    Object? timestamp = null,
    Object? trafficCondition = freezed,
  }) {
    return _then(_value.copyWith(
      estimatedMinutes: null == estimatedMinutes
          ? _value.estimatedMinutes
          : estimatedMinutes // ignore: cast_nullable_to_non_nullable
              as int,
      distanceKm: null == distanceKm
          ? _value.distanceKm
          : distanceKm // ignore: cast_nullable_to_non_nullable
              as double,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime,
      trafficCondition: freezed == trafficCondition
          ? _value.trafficCondition
          : trafficCondition // ignore: cast_nullable_to_non_nullable
              as TrafficCondition?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ETAUpdateImplCopyWith<$Res>
    implements $ETAUpdateCopyWith<$Res> {
  factory _$$ETAUpdateImplCopyWith(
          _$ETAUpdateImpl value, $Res Function(_$ETAUpdateImpl) then) =
      __$$ETAUpdateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int estimatedMinutes,
      double distanceKm,
      DateTime timestamp,
      TrafficCondition? trafficCondition});
}

/// @nodoc
class __$$ETAUpdateImplCopyWithImpl<$Res>
    extends _$ETAUpdateCopyWithImpl<$Res, _$ETAUpdateImpl>
    implements _$$ETAUpdateImplCopyWith<$Res> {
  __$$ETAUpdateImplCopyWithImpl(
      _$ETAUpdateImpl _value, $Res Function(_$ETAUpdateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ETAUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? estimatedMinutes = null,
    Object? distanceKm = null,
    Object? timestamp = null,
    Object? trafficCondition = freezed,
  }) {
    return _then(_$ETAUpdateImpl(
      estimatedMinutes: null == estimatedMinutes
          ? _value.estimatedMinutes
          : estimatedMinutes // ignore: cast_nullable_to_non_nullable
              as int,
      distanceKm: null == distanceKm
          ? _value.distanceKm
          : distanceKm // ignore: cast_nullable_to_non_nullable
              as double,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime,
      trafficCondition: freezed == trafficCondition
          ? _value.trafficCondition
          : trafficCondition // ignore: cast_nullable_to_non_nullable
              as TrafficCondition?,
    ));
  }
}

/// @nodoc

class _$ETAUpdateImpl extends _ETAUpdate {
  const _$ETAUpdateImpl(
      {required this.estimatedMinutes,
      required this.distanceKm,
      required this.timestamp,
      this.trafficCondition})
      : super._();

  /// Estimated arrival time in minutes
  @override
  final int estimatedMinutes;

  /// Distance to destination in kilometers
  @override
  final double distanceKm;

  /// Timestamp of the ETA calculation
  @override
  final DateTime timestamp;

  /// Traffic conditions affecting ETA
  @override
  final TrafficCondition? trafficCondition;

  @override
  String toString() {
    return 'ETAUpdate(estimatedMinutes: $estimatedMinutes, distanceKm: $distanceKm, timestamp: $timestamp, trafficCondition: $trafficCondition)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ETAUpdateImpl &&
            (identical(other.estimatedMinutes, estimatedMinutes) ||
                other.estimatedMinutes == estimatedMinutes) &&
            (identical(other.distanceKm, distanceKm) ||
                other.distanceKm == distanceKm) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.trafficCondition, trafficCondition) ||
                other.trafficCondition == trafficCondition));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, estimatedMinutes, distanceKm, timestamp, trafficCondition);

  /// Create a copy of ETAUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ETAUpdateImplCopyWith<_$ETAUpdateImpl> get copyWith =>
      __$$ETAUpdateImplCopyWithImpl<_$ETAUpdateImpl>(this, _$identity);
}

abstract class _ETAUpdate extends ETAUpdate {
  const factory _ETAUpdate(
      {required final int estimatedMinutes,
      required final double distanceKm,
      required final DateTime timestamp,
      final TrafficCondition? trafficCondition}) = _$ETAUpdateImpl;
  const _ETAUpdate._() : super._();

  /// Estimated arrival time in minutes
  @override
  int get estimatedMinutes;

  /// Distance to destination in kilometers
  @override
  double get distanceKm;

  /// Timestamp of the ETA calculation
  @override
  DateTime get timestamp;

  /// Traffic conditions affecting ETA
  @override
  TrafficCondition? get trafficCondition;

  /// Create a copy of ETAUpdate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ETAUpdateImplCopyWith<_$ETAUpdateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
