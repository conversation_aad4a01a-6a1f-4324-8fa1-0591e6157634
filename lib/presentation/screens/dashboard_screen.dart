import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../core/navigation/app_router.dart';
import '../../core/navigation/navigation_helper.dart';
import '../../core/utils/haptic_feedback_utils.dart';
import '../../domain/entities/user.dart';
import '../providers/auth_notifier.dart';
import '../providers/ride_tracking_notifier.dart';

/// Main dashboard screen with bottom navigation
/// This will be fully implemented in subtask 5.2
@RoutePage()
class DashboardScreen extends ConsumerWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authStateProvider);

    return authState.when(
      initial: () => const _LoadingScreen(),
      loading: () => const _LoadingScreen(),
      authenticated: (user) => _buildDashboard(context, ref, user),
      unauthenticated: () => const _UnauthorizedScreen(),
      error: (message, errorCode) => _ErrorScreen(message: message),
    );
  }

  /// Build the main dashboard for authenticated users
  Widget _buildDashboard(BuildContext context, WidgetRef ref, user) {
    return _MainAppNavigation(user: user);
  }
}

/// Loading screen widget
class _LoadingScreen extends StatelessWidget {
  const _LoadingScreen();

  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Center(child: CircularProgressIndicator()));
  }
}

/// Unauthorized access screen
class _UnauthorizedScreen extends StatelessWidget {
  const _UnauthorizedScreen();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.lock, size: 64),
            const SizedBox(height: 16),
            Text(
              'Unauthorized Access',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            const Text('Please log in to access this screen.'),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                context.router.pushAndPopUntil(
                  const WelcomeRoute(),
                  predicate: (_) => false,
                );
              },
              child: const Text('Go to Login'),
            ),
          ],
        ),
      ),
    );
  }
}

/// Error screen widget
class _ErrorScreen extends StatelessWidget {
  final String message;

  const _ErrorScreen({required this.message});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text('Error', style: Theme.of(context).textTheme.headlineSmall),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(message, textAlign: TextAlign.center),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                context.router.pushAndPopUntil(
                  const WelcomeRoute(),
                  predicate: (_) => false,
                );
              },
              child: const Text('Go to Welcome'),
            ),
          ],
        ),
      ),
    );
  }
}

/// Main app navigation with bottom navigation tabs
/// Provides different navigation structure based on user type
class _MainAppNavigation extends ConsumerStatefulWidget {
  final User user;

  const _MainAppNavigation({required this.user});

  @override
  ConsumerState<_MainAppNavigation> createState() => _MainAppNavigationState();
}

class _MainAppNavigationState extends ConsumerState<_MainAppNavigation> {
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    // Get navigation items based on user type
    final navigationItems = _getNavigationItems(widget.user.userType);

    // Watch for active ride status to show in app bar
    final rideStatusIndicator = ref.watch(rideStatusIndicatorProvider);
    final hasActiveRide = ref.watch(hasActiveRideProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Lucian Rides'),
        // Show ride status indicator if there's an active ride
        bottom: hasActiveRide && rideStatusIndicator != null
            ? PreferredSize(
                preferredSize: const Size.fromHeight(48),
                child: _RideStatusBar(
                  indicator: rideStatusIndicator,
                  onTap: () => NavigationHelper.navigateToActiveRide(
                    context.router,
                    ref,
                  ),
                ),
              )
            : null,
        actions: [
          // Show active ride button for riders if there's an active ride
          if (widget.user.userType == UserType.rider && hasActiveRide)
            IconButton(
              icon: Icon(
                _getRideStatusIcon(rideStatusIndicator?.icon),
                color: _getRideStatusColor(rideStatusIndicator?.color),
              ),
              onPressed: () =>
                  NavigationHelper.navigateToActiveRide(context.router, ref),
              tooltip: 'View Active Ride',
            ),
          // User profile button
          IconButton(
            icon: CircleAvatar(
              radius: 16,
              backgroundColor: Theme.of(context).primaryColor,
              child: Text(
                widget.user.name.isNotEmpty
                    ? widget.user.name[0].toUpperCase()
                    : 'U',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            onPressed: () => _showUserMenu(context),
          ),
        ],
      ),
      body: AnimatedSwitcher(
        duration: const Duration(milliseconds: 150),
        child: Container(
          key: ValueKey<int>(_currentIndex),
          child: navigationItems[_currentIndex].widget,
        ),
      ),
      bottomNavigationBar: Theme(
        data: Theme.of(context).copyWith(
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
        ),
        child: BottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: (index) {
            HapticFeedbackUtils.navigation();
            setState(() {
              _currentIndex = index;
            });
          },
          type: BottomNavigationBarType.fixed,
          elevation: 8,
          backgroundColor: Colors.white,
          selectedItemColor: Theme.of(context).primaryColor,
          unselectedItemColor: Colors.grey[600],
          selectedFontSize: 12,
          unselectedFontSize: 12,
          items: navigationItems
              .map(
                (item) => BottomNavigationBarItem(
                  icon: Icon(item.icon),
                  label: item.label,
                ),
              )
              .toList(),
        ),
      ),
    );
  }

  /// Get navigation items based on user type
  List<_NavigationItem> _getNavigationItems(UserType userType) {
    switch (userType) {
      case UserType.rider:
        return [
          _NavigationItem(
            icon: Icons.home,
            label: 'Home',
            widget: _RiderHomeTab(user: widget.user),
          ),
          _NavigationItem(
            icon: Icons.history,
            label: 'Rides',
            widget: _RiderRidesTab(user: widget.user),
          ),
          _NavigationItem(
            icon: Icons.account_balance_wallet,
            label: 'Wallet',
            widget: _WalletTab(user: widget.user),
          ),
          _NavigationItem(
            icon: Icons.person,
            label: 'Profile',
            widget: _ProfileTab(user: widget.user),
          ),
        ];
      case UserType.driver:
        return [
          _NavigationItem(
            icon: Icons.dashboard,
            label: 'Dashboard',
            widget: _DriverDashboardTab(user: widget.user),
          ),
          _NavigationItem(
            icon: Icons.local_taxi,
            label: 'Rides',
            widget: _DriverRidesTab(user: widget.user),
          ),
          _NavigationItem(
            icon: Icons.attach_money,
            label: 'Earnings',
            widget: _EarningsTab(user: widget.user),
          ),
          _NavigationItem(
            icon: Icons.person,
            label: 'Profile',
            widget: _ProfileTab(user: widget.user),
          ),
        ];
    }
  }

  /// Show user menu with logout option
  void _showUserMenu(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: CircleAvatar(
                backgroundColor: Theme.of(context).primaryColor,
                child: Text(
                  widget.user.name.isNotEmpty
                      ? widget.user.name[0].toUpperCase()
                      : 'U',
                  style: const TextStyle(color: Colors.white),
                ),
              ),
              title: Text(widget.user.name),
              subtitle: Text(widget.user.email),
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.settings),
              title: const Text('Settings'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Navigate to settings screen
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Settings screen coming soon')),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.help),
              title: const Text('Help & Support'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Navigate to help screen
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Help screen coming soon')),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.logout, color: Colors.red),
              title: const Text('Logout', style: TextStyle(color: Colors.red)),
              onTap: () {
                Navigator.pop(context);
                _showLogoutConfirmation(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  /// Show logout confirmation dialog
  void _showLogoutConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ref.read(authNotifierProvider.notifier).logout();
            },
            child: const Text('Logout', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}

/// Navigation item data class
class _NavigationItem {
  final IconData icon;
  final String label;
  final Widget widget;

  const _NavigationItem({
    required this.icon,
    required this.label,
    required this.widget,
  });
}

/// Rider Home Tab - Main screen for booking rides with enhanced integration
class _RiderHomeTab extends ConsumerWidget {
  final User user;

  const _RiderHomeTab({required this.user});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch for active ride status
    final hasActiveRide = ref.watch(hasActiveRideProvider);
    final activeRide = ref.watch(activeRideProvider);
    final rideStatusIndicator = ref.watch(rideStatusIndicatorProvider);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome message
          Text(
            'Welcome back, ${user.name.split(' ').first}!',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            hasActiveRide
                ? 'You have an active ride'
                : 'Where would you like to go today?',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: hasActiveRide
                  ? Theme.of(context).primaryColor
                  : Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),

          // Active ride card (if there's an active ride)
          if (hasActiveRide &&
              activeRide != null &&
              rideStatusIndicator != null) ...[
            Card(
              elevation: 6,
              color: Theme.of(context).primaryColor.withValues(alpha: 0.05),
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          _getRideStatusIcon(rideStatusIndicator.icon),
                          color: _getRideStatusColor(rideStatusIndicator.color),
                          size: 28,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Active Ride',
                                style: Theme.of(context).textTheme.titleLarge
                                    ?.copyWith(fontWeight: FontWeight.bold),
                              ),
                              Text(
                                rideStatusIndicator.status,
                                style: Theme.of(context).textTheme.bodyMedium
                                    ?.copyWith(
                                      color: _getRideStatusColor(
                                        rideStatusIndicator.color,
                                      ),
                                      fontWeight: FontWeight.w600,
                                    ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'From: ${activeRide.pickupLocation.name}',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'To: ${activeRide.dropoffLocation.name}',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 20),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () => NavigationHelper.navigateToActiveRide(
                          context.router,
                          ref,
                        ),
                        icon: const Icon(Icons.visibility),
                        label: const Text('View Ride'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: _getRideStatusColor(
                            rideStatusIndicator.color,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
          ] else ...[
            // Quick booking card (when no active ride)
            Card(
              elevation: 4,
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.location_on,
                          color: Theme.of(context).primaryColor,
                          size: 28,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Book a Ride',
                          style: Theme.of(context).textTheme.titleLarge
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Quick and reliable transportation around St. Lucia',
                    ),
                    const SizedBox(height: 20),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () => context.router.pushNamed('/ride/book'),
                        icon: const Icon(Icons.add),
                        label: const Text('Book Now'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
          ],

          // Quick actions row
          Row(
            children: [
              Expanded(
                child: _QuickActionCard(
                  icon: Icons.history,
                  title: 'Ride History',
                  subtitle: 'View past trips',
                  onTap: () =>
                      NavigationHelper.navigateToRideHistory(context.router),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _QuickActionCard(
                  icon: Icons.account_balance_wallet,
                  title: 'Wallet',
                  subtitle: 'Manage payments',
                  onTap: () {
                    // Navigate to wallet tab
                    // This would be implemented when we have proper tab navigation
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Wallet feature coming soon'),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Ride statistics summary
          Text(
            'Your Stats',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          _RideStatisticsSummary(user: user),
          const SizedBox(height: 24), // Add bottom padding for better scrolling
        ],
      ),
    );
  }
}

/// Rider Rides Tab - History of rides for riders with navigation integration
class _RiderRidesTab extends ConsumerWidget {
  final User user;

  const _RiderRidesTab({required this.user});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Check if user has an active ride
    final hasActiveRide = ref.watch(hasActiveRideProvider);
    final activeRide = ref.watch(activeRideProvider);

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with quick actions
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Your Rides',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton.icon(
                onPressed: () =>
                    NavigationHelper.navigateToRideHistory(context.router),
                icon: const Icon(Icons.history),
                label: const Text('View All'),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Active ride card if there's an active ride
          if (hasActiveRide && activeRide != null) ...[
            Card(
              elevation: 4,
              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.local_taxi,
                          color: Theme.of(context).primaryColor,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Active Ride',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).primaryColor,
                              ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'From: ${activeRide.pickupLocation.name}',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    Text(
                      'To: ${activeRide.dropoffLocation.name}',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 12),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () => NavigationHelper.navigateToActiveRide(
                          context.router,
                          ref,
                        ),
                        child: const Text('View Active Ride'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Quick actions
          Row(
            children: [
              Expanded(
                child: Card(
                  child: InkWell(
                    onTap: () => context.router.pushNamed('/ride/book'),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        children: [
                          Icon(
                            Icons.add_location,
                            size: 32,
                            color: Theme.of(context).primaryColor,
                          ),
                          const SizedBox(height: 8),
                          const Text('Book Ride'),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Card(
                  child: InkWell(
                    onTap: () =>
                        NavigationHelper.navigateToRideHistory(context.router),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        children: [
                          Icon(
                            Icons.history,
                            size: 32,
                            color: Theme.of(context).primaryColor,
                          ),
                          const SizedBox(height: 8),
                          const Text('History'),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Recent rides or empty state
          Expanded(
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.local_taxi, size: 48, color: Colors.grey[400]),
                      const SizedBox(height: 16),
                      Text(
                        'No recent rides',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(color: Colors.grey[600]),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Your ride history will appear here',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[500],
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton.icon(
                        onPressed: () => context.router.pushNamed('/ride/book'),
                        icon: const Icon(Icons.add),
                        label: const Text('Book Your First Ride'),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Driver Dashboard Tab - Main screen for drivers
class _DriverDashboardTab extends StatelessWidget {
  final User user;

  const _DriverDashboardTab({required this.user});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome message
          Text(
            'Welcome, ${user.name.split(' ').first}!',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            'Ready to start earning?',
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 32),

          // Driver status card
          Card(
            elevation: 4,
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.radio_button_off,
                        color: Colors.grey[600],
                        size: 28,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'Driver Status: Offline',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  const Text('Go online to start receiving ride requests'),
                  const SizedBox(height: 20),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () {
                        // TODO: Implement driver online/offline toggle
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                              'Driver status toggle will be implemented in the next phase',
                            ),
                          ),
                        );
                      },
                      icon: const Icon(Icons.power_settings_new),
                      label: const Text('Go Online'),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),

          // Today's summary
          Text(
            "Today's Summary",
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _SummaryCard(
                  title: 'Rides',
                  value: '0',
                  icon: Icons.local_taxi,
                  color: Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _SummaryCard(
                  title: 'Earnings',
                  value: '\$0.00',
                  icon: Icons.attach_money,
                  color: Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _SummaryCard(
                  title: 'Hours',
                  value: '0h',
                  icon: Icons.access_time,
                  color: Colors.orange,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _SummaryCard(
                  title: 'Rating',
                  value: '5.0',
                  icon: Icons.star,
                  color: Colors.amber,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// Driver Rides Tab - Active and completed rides for drivers
class _DriverRidesTab extends StatelessWidget {
  final User user;

  const _DriverRidesTab({required this.user});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Your Rides',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.directions_car, size: 64, color: Colors.grey[400]),
                  const SizedBox(height: 24),
                  Text(
                    'No rides yet',
                    style: Theme.of(
                      context,
                    ).textTheme.titleLarge?.copyWith(color: Colors.grey[600]),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Go online to start receiving ride requests',
                    style: Theme.of(
                      context,
                    ).textTheme.bodyLarge?.copyWith(color: Colors.grey[500]),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 32),
                  ElevatedButton.icon(
                    onPressed: () {
                      // TODO: Implement go online functionality
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text(
                            'Go online feature will be implemented in the next phase',
                          ),
                        ),
                      );
                    },
                    icon: const Icon(Icons.power_settings_new),
                    label: const Text('Go Online'),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Earnings Tab - Driver earnings and payout information
class _EarningsTab extends StatelessWidget {
  final User user;

  const _EarningsTab({required this.user});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Earnings',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          // Total earnings card
          Card(
            elevation: 4,
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Total Earnings',
                    style: Theme.of(
                      context,
                    ).textTheme.titleMedium?.copyWith(color: Colors.grey[600]),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '\$0.00',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.green[700],
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'This Week',
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(color: Colors.grey[600]),
                            ),
                            Text(
                              '\$0.00',
                              style: Theme.of(context).textTheme.titleMedium
                                  ?.copyWith(fontWeight: FontWeight.bold),
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'This Month',
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(color: Colors.grey[600]),
                            ),
                            Text(
                              '\$0.00',
                              style: Theme.of(context).textTheme.titleMedium
                                  ?.copyWith(fontWeight: FontWeight.bold),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),

          // Payout information
          Text(
            'Payout Information',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  ListTile(
                    leading: const Icon(Icons.schedule),
                    title: const Text('Next Payout'),
                    subtitle: const Text('Weekly payouts every Monday'),
                    trailing: Text(
                      'Pending',
                      style: TextStyle(
                        color: Colors.orange[700],
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const Divider(),
                  ListTile(
                    leading: const Icon(Icons.account_balance),
                    title: const Text('Payment Method'),
                    subtitle: const Text('Bank account not set up'),
                    trailing: SizedBox(
                      width: 70,
                      child: TextButton(
                        onPressed: () {
                          // TODO: Navigate to payment setup
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text(
                                'Payment setup will be implemented in the next phase',
                              ),
                            ),
                          );
                        },
                        child: const Text('Set Up'),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Wallet Tab - Payment methods and transaction history
class _WalletTab extends StatelessWidget {
  final User user;

  const _WalletTab({required this.user});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Wallet',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          // Balance card
          Card(
            elevation: 4,
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Wallet Balance',
                    style: Theme.of(
                      context,
                    ).textTheme.titleMedium?.copyWith(color: Colors.grey[600]),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '\$0.00',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.green[700],
                    ),
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () {
                        // TODO: Navigate to add funds screen
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                              'Add funds feature will be implemented in the next phase',
                            ),
                          ),
                        );
                      },
                      icon: const Icon(Icons.add),
                      label: const Text('Add Funds'),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),

          // Payment methods
          Text(
            'Payment Methods',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  ListTile(
                    leading: const Icon(Icons.credit_card),
                    title: const Text('Credit/Debit Cards'),
                    subtitle: const Text('No cards added'),
                    trailing: SizedBox(
                      width: 60,
                      child: TextButton(
                        onPressed: () {
                          // TODO: Navigate to add card screen
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text(
                                'Add card feature will be implemented in the next phase',
                              ),
                            ),
                          );
                        },
                        child: const Text('Add'),
                      ),
                    ),
                  ),
                  const Divider(),
                  ListTile(
                    leading: const Icon(Icons.account_balance),
                    title: const Text('Bank Account'),
                    subtitle: const Text('No account linked'),
                    trailing: SizedBox(
                      width: 60,
                      child: TextButton(
                        onPressed: () {
                          // TODO: Navigate to link bank account screen
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text(
                                'Link bank account feature will be implemented in the next phase',
                              ),
                            ),
                          );
                        },
                        child: const Text('Link'),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),

          // Transaction history
          Text(
            'Recent Transactions',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.receipt_long,
                        size: 48,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No transactions yet',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(color: Colors.grey[600]),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Your transaction history will appear here',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[500],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Profile Tab - User profile and settings
class _ProfileTab extends StatelessWidget {
  final User user;

  const _ProfileTab({required this.user});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Profile',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          // Profile header
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 40,
                    backgroundColor: Theme.of(context).primaryColor,
                    child: Text(
                      user.name.isNotEmpty ? user.name[0].toUpperCase() : 'U',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 20),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          user.name,
                          style: Theme.of(context).textTheme.titleLarge
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          user.email,
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(color: Colors.grey[600]),
                        ),
                        const SizedBox(height: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Theme.of(
                              context,
                            ).primaryColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            user.userType
                                .toString()
                                .split('.')
                                .last
                                .toUpperCase(),
                            style: TextStyle(
                              color: Theme.of(context).primaryColor,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),

          // Profile options
          Expanded(
            child: ListView(
              children: [
                _ProfileOption(
                  icon: Icons.person,
                  title: 'Edit Profile',
                  subtitle: 'Update your personal information',
                  onTap: () {
                    context.router.push(EditProfileRoute(user: user));
                  },
                ),
                _ProfileOption(
                  icon: Icons.security,
                  title: 'Privacy & Security',
                  subtitle: 'Manage your privacy settings',
                  onTap: () {
                    // TODO: Navigate to privacy settings screen
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text(
                          'Privacy settings will be implemented in the next phase',
                        ),
                      ),
                    );
                  },
                ),
                _ProfileOption(
                  icon: Icons.notifications,
                  title: 'Notifications',
                  subtitle: 'Configure notification preferences',
                  onTap: () {
                    // TODO: Navigate to notification settings screen
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text(
                          'Notification settings will be implemented in the next phase',
                        ),
                      ),
                    );
                  },
                ),
                _ProfileOption(
                  icon: Icons.help,
                  title: 'Help & Support',
                  subtitle: 'Get help and contact support',
                  onTap: () {
                    // TODO: Navigate to help screen
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text(
                          'Help & support will be implemented in the next phase',
                        ),
                      ),
                    );
                  },
                ),
                _ProfileOption(
                  icon: Icons.info,
                  title: 'About',
                  subtitle: 'App version and legal information',
                  onTap: () {
                    // TODO: Navigate to about screen
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text(
                          'About screen will be implemented in the next phase',
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// Summary card widget for driver dashboard
class _SummaryCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;

  const _SummaryCard({
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 20),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
          ],
        ),
      ),
    );
  }
}

/// Profile option widget
class _ProfileOption extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;

  const _ProfileOption({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(icon, color: Theme.of(context).primaryColor),
        title: Text(title),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.chevron_right),
        onTap: onTap,
      ),
    );
  }
}

/// Ride status bar widget for showing active ride status
class _RideStatusBar extends StatelessWidget {
  final RideStatusIndicator indicator;
  final VoidCallback onTap;

  const _RideStatusBar({required this.indicator, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: _getRideStatusColor(indicator.color).withValues(alpha: 0.1),
        border: Border(
          top: BorderSide(
            color: _getRideStatusColor(indicator.color),
            width: 2,
          ),
        ),
      ),
      child: InkWell(
        onTap: onTap,
        child: Row(
          children: [
            Icon(
              _getRideStatusIcon(indicator.icon),
              color: _getRideStatusColor(indicator.color),
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                indicator.status,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: _getRideStatusColor(indicator.color),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            Icon(
              Icons.chevron_right,
              color: _getRideStatusColor(indicator.color),
              size: 20,
            ),
          ],
        ),
      ),
    );
  }
}

/// Helper method to get icon for ride status
IconData _getRideStatusIcon(RideStatusIcon? icon) {
  switch (icon) {
    case RideStatusIcon.search:
      return Icons.search;
    case RideStatusIcon.car:
      return Icons.directions_car;
    case RideStatusIcon.navigation:
      return Icons.navigation;
    case RideStatusIcon.check:
      return Icons.check_circle;
    case RideStatusIcon.cancel:
      return Icons.cancel;
    case null:
      return Icons.local_taxi;
  }
}

/// Helper method to get color for ride status
Color _getRideStatusColor(RideStatusColor? color) {
  switch (color) {
    case RideStatusColor.pending:
      return Colors.orange;
    case RideStatusColor.active:
      return Colors.blue;
    case RideStatusColor.completed:
      return Colors.green;
    case RideStatusColor.cancelled:
      return Colors.red;
    case null:
      return Colors.grey;
  }
}

/// Quick action card widget for dashboard shortcuts
class _QuickActionCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;

  const _QuickActionCard({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(icon, size: 32, color: Theme.of(context).primaryColor),
              const SizedBox(height: 8),
              Text(
                title,
                style: Theme.of(
                  context,
                ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Ride statistics summary widget for displaying user ride stats
class _RideStatisticsSummary extends ConsumerWidget {
  final User user;

  const _RideStatisticsSummary({required this.user});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // In a real implementation, these would come from a ride statistics provider
    // For now, we'll show placeholder data
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Statistics grid
            Row(
              children: [
                Expanded(
                  child: _StatisticItem(
                    icon: Icons.local_taxi,
                    label: 'Total Rides',
                    value: '0',
                    color: Colors.blue,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _StatisticItem(
                    icon: Icons.attach_money,
                    label: 'Total Spent',
                    value: '\$0.00',
                    color: Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _StatisticItem(
                    icon: Icons.star,
                    label: 'Avg Rating',
                    value: '5.0',
                    color: Colors.amber,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _StatisticItem(
                    icon: Icons.schedule,
                    label: 'This Month',
                    value: '0 rides',
                    color: Colors.purple,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Achievement or tip section
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.lightbulb_outline,
                    color: Theme.of(context).primaryColor,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Book your first ride to start building your travel history!',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Individual statistic item widget
class _StatisticItem extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;
  final Color color;

  const _StatisticItem({
    required this.icon,
    required this.label,
    required this.value,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 8),
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
