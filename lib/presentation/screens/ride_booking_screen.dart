import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../core/theme/app_colors.dart';
import '../../core/theme/app_spacing.dart';
import '../../core/theme/app_typography.dart';
import '../../core/utils/haptic_feedback_utils.dart';
import '../../core/utils/accessibility_utils.dart';

import '../../domain/entities/ride.dart';
import '../providers/ride_booking_notifier.dart';
import '../providers/ride_booking_state.dart';
import '../widgets/interactive_map_widget.dart';
import '../widgets/custom_button.dart';

import '../widgets/enhanced_loading_states.dart';
import '../widgets/smooth_transitions.dart';

/// Main ride booking screen with map integration
/// Inspired by modern ride-sharing app design patterns
@RoutePage()
class RideBookingScreen extends ConsumerStatefulWidget {
  const RideBookingScreen({super.key});

  @override
  ConsumerState<RideBookingScreen> createState() => _RideBookingScreenState();
}

class _RideBookingScreenState extends ConsumerState<RideBookingScreen>
    with TickerProviderStateMixin {
  // Animation controllers for future use
  late AnimationController _slideController;
  late AnimationController _fadeController;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bookingState = ref.watch(rideBookingNotifierProvider);
    final bookingNotifier = ref.read(rideBookingNotifierProvider.notifier);

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: _buildAppBar(context),
      body: Stack(
        children: [
          // Map background
          _buildMapSection(bookingState, bookingNotifier),

          // Overlay content
          _buildOverlayContent(context, bookingState, bookingNotifier),
        ],
      ),
    );
  }

  /// Build the app bar with back button and title
  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white,
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => context.router.maybePop(),
        ),
      ),
      title: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Text(
          'Book a Ride',
          style: AppTypography.bodyLarge.copyWith(
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
      ),
      centerTitle: true,
    );
  }

  /// Build the map section
  Widget _buildMapSection(
    RideBookingState bookingState,
    RideBookingNotifier bookingNotifier,
  ) {
    return InteractiveMapWidget(
      currentLocation: null, // We'll get this from location provider later
      pickupLocation: bookingState.pickupLocation,
      dropoffLocation: bookingState.dropoffLocation,
      showRoute: bookingState.hasValidLocations,
      enableMarkerDragging: true,
      showCurrentLocation: true,
      showControls: true,
      expandToFill: true,
      onPickupLocationChanged: (location) {
        // Convert coordinates to RideLocation and set it
        // This is a simplified implementation - in a real app you'd geocode the coordinates
        final rideLocation = RideLocation(
          name: 'Selected Location',
          latitude: location.latitude,
          longitude: location.longitude,
        );
        bookingNotifier.setPickupLocation(rideLocation);
      },
      onDropoffLocationChanged: (location) {
        // Convert coordinates to RideLocation and set it
        final rideLocation = RideLocation(
          name: 'Selected Destination',
          latitude: location.latitude,
          longitude: location.longitude,
        );
        bookingNotifier.setDropoffLocation(rideLocation);
      },
      onMapTap: (position) {
        // Handle map tap for location selection
        final rideLocation = RideLocation(
          name: 'Selected Location',
          latitude: position.latitude,
          longitude: position.longitude,
        );

        if (bookingState.pickupLocation == null) {
          bookingNotifier.setPickupLocation(rideLocation);
        } else if (bookingState.dropoffLocation == null) {
          bookingNotifier.setDropoffLocation(rideLocation);
        }
      },
    );
  }

  /// Build the overlay content with location selection and booking interface
  Widget _buildOverlayContent(
    BuildContext context,
    RideBookingState bookingState,
    RideBookingNotifier bookingNotifier,
  ) {
    return Column(
      children: [
        const Spacer(),
        // Main booking interface
        _buildBookingInterface(context, bookingState, bookingNotifier),
      ],
    );
  }

  /// Build the main booking interface
  Widget _buildBookingInterface(
    BuildContext context,
    RideBookingState bookingState,
    RideBookingNotifier bookingNotifier,
  ) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 20,
            offset: Offset(0, -4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(top: 12),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Content based on booking state
          if (!bookingState.hasValidLocations)
            _buildLocationSelectionInterface(
              context,
              bookingState,
              bookingNotifier,
            )
          else if (!bookingState.hasPricing)
            _buildLoadingPricing()
          else
            _buildPricingAndConfirmation(
              context,
              bookingState,
              bookingNotifier,
            ),
        ],
      ),
    );
  }

  /// Build location selection interface
  Widget _buildLocationSelectionInterface(
    BuildContext context,
    RideBookingState bookingState,
    RideBookingNotifier bookingNotifier,
  ) {
    return Padding(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Where to?',
            style: AppTypography.h3.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: AppSpacing.md),

          // Pickup location field
          _buildLocationField(
            context: context,
            label: 'Pickup Location',
            icon: Icons.radio_button_checked,
            iconColor: AppColors.pickupPin,
            location: bookingState.pickupLocation,
            onTap: () => _showLocationPicker(context, true, bookingNotifier),
            isCurrentLocation: false, // We'll implement this logic later
          ),

          const SizedBox(height: AppSpacing.sm),

          // Dropoff location field
          _buildLocationField(
            context: context,
            label: 'Where to?',
            icon: Icons.location_on,
            iconColor: AppColors.destinationPin,
            location: bookingState.dropoffLocation,
            onTap: () => _showLocationPicker(context, false, bookingNotifier),
          ),

          const SizedBox(height: AppSpacing.lg),

          // Recent locations - temporarily disabled until we implement location state
          // if (bookingState.recentLocations.isNotEmpty)
          //   _buildRecentLocations(bookingState, bookingNotifier),
        ],
      ),
    );
  }

  /// Build location field
  Widget _buildLocationField({
    required BuildContext context,
    required String label,
    required IconData icon,
    required Color iconColor,
    required RideLocation? location,
    required VoidCallback onTap,
    bool isCurrentLocation = false,
  }) {
    return InkWell(
      onTap: () {
        HapticFeedbackUtils.lightImpact();
        onTap();
      },
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(AppSpacing.md),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Icon(icon, color: iconColor, size: 24),
            const SizedBox(width: AppSpacing.md),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (location == null)
                    Text(
                      label,
                      style: AppTypography.bodyLarge.copyWith(
                        color: Colors.grey[600],
                      ),
                    )
                  else ...[
                    Text(
                      location.name,
                      style: AppTypography.bodyLarge.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (location.address != null)
                      Text(
                        location.address!,
                        style: AppTypography.bodySmall.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                  ],
                ],
              ),
            ),
            if (isCurrentLocation)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'Current',
                  style: AppTypography.bodySmall.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// Build loading pricing state
  Widget _buildLoadingPricing() {
    return const Padding(
      padding: EdgeInsets.all(AppSpacing.xl),
      child: RideBookingLoader(
        message: 'Calculating your fare...',
        showProgress: true,
      ),
    );
  }

  /// Build pricing and confirmation interface
  Widget _buildPricingAndConfirmation(
    BuildContext context,
    RideBookingState bookingState,
    RideBookingNotifier bookingNotifier,
  ) {
    return Padding(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Trip summary
          _buildTripSummary(bookingState),

          const SizedBox(height: AppSpacing.lg),

          // Vehicle selection and pricing
          _buildVehicleSelection(bookingState),

          const SizedBox(height: AppSpacing.lg),

          // Book ride button
          _buildBookRideButton(context, bookingState, bookingNotifier),

          const SizedBox(height: AppSpacing.md),
        ],
      ),
    );
  }

  /// Build trip summary
  Widget _buildTripSummary(RideBookingState bookingState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.radio_button_checked,
              color: AppColors.pickupPin,
              size: 16,
            ),
            const SizedBox(width: AppSpacing.sm),
            Expanded(
              child: Text(
                bookingState.pickupLocation?.name ?? 'Pickup location',
                style: AppTypography.bodyMedium,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppSpacing.xs),
        Row(
          children: [
            Icon(Icons.location_on, color: AppColors.destinationPin, size: 16),
            const SizedBox(width: AppSpacing.sm),
            Expanded(
              child: Text(
                bookingState.dropoffLocation?.name ?? 'Destination',
                style: AppTypography.bodyMedium,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        if (bookingState.pricingInfo?.estimatedDurationMinutes != null) ...[
          const SizedBox(height: AppSpacing.xs),
          Row(
            children: [
              Icon(Icons.access_time, color: Colors.grey[600], size: 16),
              const SizedBox(width: AppSpacing.sm),
              Text(
                '${bookingState.pricingInfo!.estimatedDurationMinutes} min trip',
                style: AppTypography.bodySmall.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  /// Build vehicle selection
  Widget _buildVehicleSelection(RideBookingState bookingState) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          // Car icon
          Container(
            width: 60,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.directions_car,
              color: AppColors.primary,
              size: 24,
            ),
          ),

          const SizedBox(width: AppSpacing.md),

          // Vehicle info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Economy',
                  style: AppTypography.labelLarge.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  'Sedan • 4 seats',
                  style: AppTypography.bodySmall.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                if (bookingState.pricingInfo?.estimatedDurationMinutes != null)
                  Text(
                    '${bookingState.pricingInfo!.estimatedDurationMinutes} min away',
                    style: AppTypography.bodySmall.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
              ],
            ),
          ),

          // Price
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '\$${bookingState.pricingInfo?.totalFare.toStringAsFixed(2) ?? '0.00'}',
                style: AppTypography.h4.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
              if (bookingState.pricingInfo?.baseFare != null)
                Text(
                  'Fixed fare',
                  style: AppTypography.bodySmall.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build book ride button
  Widget _buildBookRideButton(
    BuildContext context,
    RideBookingState bookingState,
    RideBookingNotifier bookingNotifier,
  ) {
    return SizedBox(
      width: double.infinity,
      child: CustomButton(
        onPressed: bookingState.canSubmitRide && !bookingState.isRequestingRide
            ? () => _handleBookRide(context, bookingNotifier)
            : null,
        isLoading: bookingState.isRequestingRide,
        variant: ButtonVariant.primary,
        icon: bookingState.isRequestingRide ? null : Icons.arrow_forward,
        loadingText: 'Booking Ride...',
        child: Text(
          bookingState.isRequestingRide ? 'Booking Ride...' : 'BOOK RIDE',
        ),
      ),
    );
  }

  /// Show location picker
  void _showLocationPicker(
    BuildContext context,
    bool isPickup,
    RideBookingNotifier bookingNotifier,
  ) {
    // TODO: Implement location picker modal
    // This will be implemented in subtask 4.2
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Location picker will be implemented in the next subtask',
        ),
      ),
    );
  }

  /// Handle book ride action
  void _handleBookRide(
    BuildContext context,
    RideBookingNotifier bookingNotifier,
  ) async {
    try {
      await bookingNotifier.requestRide();

      if (mounted) {
        // Success haptic feedback
        await HapticFeedbackUtils.rideBooked();

        // Announce booking confirmation to screen readers
        AccessibilityUtils.announceBookingConfirmation(context);

        // Show success message with smooth snackbar
        SmoothSnackBar.show(
          context: context,
          message: 'Ride booked successfully!',
          type: SnackBarType.success,
        );

        // Navigate to ride tracking screen with smooth transition
        // TODO: Navigate to ride tracking screen when implemented
        // For now, go back to dashboard with smooth transition
        await Future.delayed(const Duration(milliseconds: 500));
        if (mounted) {
          context.router.maybePop();
        }
      }
    } catch (error) {
      if (mounted) {
        // Error haptic feedback
        await HapticFeedbackUtils.error();

        // Announce error to screen readers
        AccessibilityUtils.announceToScreenReader(
          context,
          'Booking failed: $error',
        );

        // Show error message with smooth snackbar
        SmoothSnackBar.show(
          context: context,
          message: 'Failed to book ride: $error',
          type: SnackBarType.error,
        );
      }
    }
  }
}
