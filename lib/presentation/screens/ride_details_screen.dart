import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:auto_route/auto_route.dart';

import '../../core/theme/app_colors.dart';
import '../../core/theme/app_spacing.dart';
import '../../core/theme/app_typography.dart';
import '../../core/navigation/app_router.dart';
import '../../domain/entities/ride.dart';
import '../providers/ride_history_notifier.dart';
import '../providers/ride_history_state.dart';
import '../widgets/loading_components.dart';
import '../widgets/error_display_widget.dart';
import '../widgets/app_card.dart';

/// Screen for displaying detailed information about a specific ride
@RoutePage()
class RideDetailsScreen extends ConsumerWidget {
  final String rideId;

  const RideDetailsScreen({super.key, required this.rideId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final detailsState = ref.watch(rideDetailsProvider(rideId));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Ride Details'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.onPrimary,
        elevation: 0,
        actions: [
          if (detailsState.hasReceipt)
            IconButton(
              icon: const Icon(Icons.receipt),
              onPressed: () =>
                  context.router.push(RideReceiptRoute(rideId: rideId)),
              tooltip: 'View receipt',
            ),
        ],
      ),
      body: _buildContent(context, ref, detailsState),
    );
  }

  Widget _buildContent(
    BuildContext context,
    WidgetRef ref,
    RideDetailsState state,
  ) {
    if (state.isLoading) {
      return const Center(child: LoadingSpinner());
    }

    if (state.errorMessage != null) {
      return ErrorDisplayWidget(
        message: state.errorMessage!,
        onRetry: () => ref
            .read(rideDetailsProvider(rideId).notifier)
            .loadFullRideInfo(rideId),
      );
    }

    if (!state.hasRideDetails) {
      return const Center(child: Text('Ride details not found'));
    }

    final ride = state.ride!;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Status and basic info
          _buildStatusCard(ride),
          const SizedBox(height: AppSpacing.md),

          // Route information
          _buildRouteCard(ride),
          const SizedBox(height: AppSpacing.md),

          // Driver information (if available)
          if (ride.driverId != null) ...[
            _buildDriverCard(ride),
            const SizedBox(height: AppSpacing.md),
          ],

          // Fare and payment information
          _buildFareCard(ride),
          const SizedBox(height: AppSpacing.md),

          // Timeline
          _buildTimelineCard(ride),
          const SizedBox(height: AppSpacing.md),

          // Special instructions (if any)
          if (ride.specialInstructions != null &&
              ride.specialInstructions!.isNotEmpty) ...[
            _buildSpecialInstructionsCard(ride),
            const SizedBox(height: AppSpacing.md),
          ],

          // Action buttons
          _buildActionButtons(context, ride),
        ],
      ),
    );
  }

  Widget _buildStatusCard(Ride ride) {
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Ride Status', style: AppTypography.titleMedium),
                _buildStatusChip(ride.status),
              ],
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              'Ride ID: ${ride.id}',
              style: AppTypography.bodySmall.copyWith(
                color: AppColors.onSurfaceVariant,
              ),
            ),
            if (ride.rideDurationMinutes() != null) ...[
              const SizedBox(height: AppSpacing.xs),
              Text(
                'Duration: ${_formatDuration(ride.rideDurationMinutes()!)}',
                style: AppTypography.bodySmall.copyWith(
                  color: AppColors.onSurfaceVariant,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildRouteCard(Ride ride) {
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Route', style: AppTypography.titleMedium),
            const SizedBox(height: AppSpacing.md),

            // Pickup location
            Row(
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: AppSpacing.sm),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Pickup',
                        style: AppTypography.labelSmall.copyWith(
                          color: AppColors.onSurfaceVariant,
                        ),
                      ),
                      Text(
                        ride.pickupLocation.name,
                        style: AppTypography.bodyMedium.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      if (ride.pickupLocation.address != null)
                        Text(
                          ride.pickupLocation.address!,
                          style: AppTypography.bodySmall.copyWith(
                            color: AppColors.onSurfaceVariant,
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),

            // Connecting line
            Container(
              margin: const EdgeInsets.only(
                left: 6,
                top: AppSpacing.xs,
                bottom: AppSpacing.xs,
              ),
              width: 2,
              height: 24,
              color: AppColors.outline,
            ),

            // Dropoff location
            Row(
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: AppColors.error,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: AppSpacing.sm),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Dropoff',
                        style: AppTypography.labelSmall.copyWith(
                          color: AppColors.onSurfaceVariant,
                        ),
                      ),
                      Text(
                        ride.dropoffLocation.name,
                        style: AppTypography.bodyMedium.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      if (ride.dropoffLocation.address != null)
                        Text(
                          ride.dropoffLocation.address!,
                          style: AppTypography.bodySmall.copyWith(
                            color: AppColors.onSurfaceVariant,
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDriverCard(Ride ride) {
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Driver Information', style: AppTypography.titleMedium),
            const SizedBox(height: AppSpacing.md),
            Row(
              children: [
                CircleAvatar(
                  radius: 24,
                  backgroundColor: AppColors.primaryContainer,
                  child: Icon(
                    Icons.person,
                    color: AppColors.onPrimaryContainer,
                  ),
                ),
                const SizedBox(width: AppSpacing.md),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Driver Name', // In a real app, this would come from driver info
                        style: AppTypography.bodyLarge.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        'Driver ID: ${ride.driverId}',
                        style: AppTypography.bodySmall.copyWith(
                          color: AppColors.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
                // Rating would go here if available
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFareCard(Ride ride) {
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Fare Details', style: AppTypography.titleMedium),
                _buildPaymentStatusChip(ride.paymentStatus),
              ],
            ),
            const SizedBox(height: AppSpacing.md),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Total Fare', style: AppTypography.bodyLarge),
                Text(
                  '\$${ride.fixedFare.toStringAsFixed(2)}',
                  style: AppTypography.headlineSmall.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimelineCard(Ride ride) {
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Timeline', style: AppTypography.titleMedium),
            const SizedBox(height: AppSpacing.md),

            // Requested
            _buildTimelineItem(
              'Ride Requested',
              ride.createdAt,
              Icons.add_circle_outline,
              true,
            ),

            // Accepted
            if (ride.acceptedAt != null)
              _buildTimelineItem(
                'Driver Accepted',
                ride.acceptedAt!,
                Icons.check_circle_outline,
                true,
              ),

            // Started
            if (ride.startedAt != null)
              _buildTimelineItem(
                'Trip Started',
                ride.startedAt!,
                Icons.play_circle_outline,
                true,
              ),

            // Completed or Cancelled
            if (ride.completedAt != null)
              _buildTimelineItem(
                'Trip Completed',
                ride.completedAt!,
                Icons.check_circle,
                true,
              )
            else if (ride.cancelledAt != null)
              _buildTimelineItem(
                'Trip Cancelled',
                ride.cancelledAt!,
                Icons.cancel,
                true,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimelineItem(
    String title,
    DateTime dateTime,
    IconData icon,
    bool isCompleted,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppSpacing.sm),
      child: Row(
        children: [
          Icon(
            icon,
            color: isCompleted ? AppColors.primary : AppColors.outline,
            size: 20,
          ),
          const SizedBox(width: AppSpacing.sm),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTypography.bodyMedium.copyWith(
                    fontWeight: FontWeight.w500,
                    color: isCompleted
                        ? AppColors.onSurface
                        : AppColors.onSurfaceVariant,
                  ),
                ),
                Text(
                  _formatDateTime(dateTime),
                  style: AppTypography.bodySmall.copyWith(
                    color: AppColors.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSpecialInstructionsCard(Ride ride) {
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Special Instructions', style: AppTypography.titleMedium),
            const SizedBox(height: AppSpacing.sm),
            Text(ride.specialInstructions!, style: AppTypography.bodyMedium),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context, Ride ride) {
    return Column(
      children: [
        if (ride.status == RideStatus.completed) ...[
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () =>
                  context.router.push(RideReceiptRoute(rideId: ride.id)),
              icon: const Icon(Icons.receipt),
              label: const Text('View Receipt'),
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
        ],

        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () => _shareRideDetails(context, ride),
            icon: const Icon(Icons.share),
            label: const Text('Share Ride Details'),
          ),
        ),

        if (ride.canBeCancelledByRider()) ...[
          const SizedBox(height: AppSpacing.sm),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => _showCancelDialog(context, ride),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
                foregroundColor: AppColors.onError,
              ),
              icon: const Icon(Icons.cancel),
              label: const Text('Cancel Ride'),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildStatusChip(RideStatus status) {
    Color backgroundColor;
    Color textColor;
    String text;

    switch (status) {
      case RideStatus.completed:
        backgroundColor = AppColors.successContainer;
        textColor = AppColors.onSuccessContainer;
        text = 'Completed';
        break;
      case RideStatus.cancelled:
        backgroundColor = AppColors.errorContainer;
        textColor = AppColors.onErrorContainer;
        text = 'Cancelled';
        break;
      case RideStatus.inProgress:
        backgroundColor = AppColors.primaryContainer;
        textColor = AppColors.onPrimaryContainer;
        text = 'In Progress';
        break;
      case RideStatus.accepted:
        backgroundColor = AppColors.secondaryContainer;
        textColor = AppColors.onSecondaryContainer;
        text = 'Accepted';
        break;
      case RideStatus.requested:
        backgroundColor = AppColors.tertiaryContainer;
        textColor = AppColors.onTertiaryContainer;
        text = 'Requested';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
        vertical: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(AppSpacing.sm),
      ),
      child: Text(
        text,
        style: AppTypography.labelSmall.copyWith(
          color: textColor,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildPaymentStatusChip(PaymentStatus status) {
    Color backgroundColor;
    Color textColor;
    String text;

    switch (status) {
      case PaymentStatus.completed:
        backgroundColor = AppColors.successContainer;
        textColor = AppColors.onSuccessContainer;
        text = 'Paid';
        break;
      case PaymentStatus.pending:
        backgroundColor = AppColors.warningContainer;
        textColor = AppColors.onWarningContainer;
        text = 'Pending';
        break;
      case PaymentStatus.failed:
        backgroundColor = AppColors.errorContainer;
        textColor = AppColors.onErrorContainer;
        text = 'Failed';
        break;
      case PaymentStatus.refunded:
        backgroundColor = AppColors.tertiaryContainer;
        textColor = AppColors.onTertiaryContainer;
        text = 'Refunded';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
        vertical: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(AppSpacing.sm),
      ),
      child: Text(
        text,
        style: AppTypography.labelSmall.copyWith(
          color: textColor,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  String _formatDuration(int minutes) {
    if (minutes < 60) {
      return '$minutes min';
    } else {
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      return '${hours}h ${remainingMinutes}min';
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} at ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _shareRideDetails(BuildContext context, Ride ride) {
    // In a real app, this would use the share package
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share functionality would be implemented here'),
      ),
    );
  }

  void _showCancelDialog(BuildContext context, Ride ride) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Ride'),
        content: const Text('Are you sure you want to cancel this ride?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Keep Ride'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // In a real app, this would trigger the cancellation
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Ride cancellation would be processed here'),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: AppColors.onError,
            ),
            child: const Text('Cancel Ride'),
          ),
        ],
      ),
    );
  }
}
