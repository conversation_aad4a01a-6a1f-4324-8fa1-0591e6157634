import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:auto_route/auto_route.dart';

import '../../core/theme/app_colors.dart';
import '../../core/theme/app_spacing.dart';
import '../../core/theme/app_typography.dart';
import '../../core/navigation/app_router.dart';
import '../../domain/entities/ride.dart';
import '../providers/ride_history_notifier.dart';
import '../providers/ride_history_state.dart';
import '../widgets/loading_components.dart';
import '../widgets/error_display_widget.dart';
import '../widgets/app_card.dart';

/// Screen for displaying ride history with pagination and filtering
@RoutePage()
class RideHistoryScreen extends ConsumerStatefulWidget {
  const RideHistoryScreen({super.key});

  @override
  ConsumerState<RideHistoryScreen> createState() => _RideHistoryScreenState();
}

class _RideHistoryScreenState extends ConsumerState<RideHistoryScreen> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();

    // Load initial ride history
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(rideHistoryNotifierProvider.notifier).loadRideHistory();
    });

    // Set up scroll listener for pagination
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent * 0.8) {
      // Load more when user scrolls to 80% of the list
      ref.read(rideHistoryNotifierProvider.notifier).loadMoreRides();
    }
  }

  @override
  Widget build(BuildContext context) {
    final historyState = ref.watch(rideHistoryNotifierProvider);
    final filteredRides = ref.watch(filteredRidesProvider);
    final hasActiveFilters = ref.watch(hasActiveFiltersProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Ride History'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.onPrimary,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(context),
            tooltip: 'Filter rides',
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () =>
            ref.read(rideHistoryNotifierProvider.notifier).refreshRideHistory(),
        child: Column(
          children: [
            // Search bar
            _buildSearchBar(),

            // Active filters indicator
            if (hasActiveFilters) _buildActiveFiltersIndicator(),

            // Content
            Expanded(child: _buildContent(historyState, filteredRides)),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      color: AppColors.surface,
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search by location or driver name...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    ref
                        .read(rideHistoryNotifierProvider.notifier)
                        .setSearchQuery('');
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppSpacing.sm),
            borderSide: BorderSide(color: AppColors.outline),
          ),
          filled: true,
          fillColor: AppColors.background,
        ),
        onChanged: (value) {
          ref.read(rideHistoryNotifierProvider.notifier).setSearchQuery(value);
        },
      ),
    );
  }

  Widget _buildActiveFiltersIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.md,
        vertical: AppSpacing.sm,
      ),
      color: AppColors.primaryContainer,
      child: Row(
        children: [
          Icon(Icons.filter_alt, size: 16, color: AppColors.onPrimaryContainer),
          const SizedBox(width: AppSpacing.xs),
          Text(
            'Filters active',
            style: AppTypography.bodySmall.copyWith(
              color: AppColors.onPrimaryContainer,
            ),
          ),
          const Spacer(),
          TextButton(
            onPressed: () {
              ref.read(rideHistoryNotifierProvider.notifier).clearFilters();
              _searchController.clear();
            },
            child: Text(
              'Clear all',
              style: AppTypography.bodySmall.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(
    RideHistoryState state,
    List<RideHistory> filteredRides,
  ) {
    if (state.isLoadingHistory) {
      return const Center(child: LoadingSpinner());
    }

    if (state.hasErrors) {
      return ErrorDisplayWidget(
        message: state.errorMessage ?? 'Failed to load ride history',
        onRetry: () =>
            ref.read(rideHistoryNotifierProvider.notifier).loadRideHistory(),
      );
    }

    if (state.isEmpty) {
      return _buildEmptyState();
    }

    return _buildRidesList(filteredRides, state);
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.history, size: 64, color: AppColors.outline),
          const SizedBox(height: AppSpacing.md),
          Text(
            'No rides yet',
            style: AppTypography.headlineSmall.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            'Your ride history will appear here once you start booking rides.',
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppSpacing.lg),
          ElevatedButton(
            onPressed: () => context.router.pushAndPopUntil(
              const RideBookingRoute(),
              predicate: (route) => route.settings.name == '/',
            ),
            child: const Text('Book your first ride'),
          ),
        ],
      ),
    );
  }

  Widget _buildRidesList(List<RideHistory> rides, RideHistoryState state) {
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(AppSpacing.md),
      itemCount: rides.length + (state.isLoadingMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index >= rides.length) {
          // Loading indicator for pagination
          return const Padding(
            padding: EdgeInsets.all(AppSpacing.md),
            child: Center(child: LoadingSpinner()),
          );
        }

        final ride = rides[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: AppSpacing.md),
          child: RideHistoryItem(
            ride: ride,
            onTap: () => _navigateToRideDetails(ride.id),
          ),
        );
      },
    );
  }

  void _navigateToRideDetails(String rideId) {
    context.router.push(RideDetailsRoute(rideId: rideId));
  }

  void _showFilterDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppSpacing.md),
        ),
      ),
      builder: (context) => const RideHistoryFilterSheet(),
    );
  }
}

/// Individual ride history item widget
class RideHistoryItem extends StatelessWidget {
  final RideHistory ride;
  final VoidCallback? onTap;

  const RideHistoryItem({super.key, required this.ride, this.onTap});

  @override
  Widget build(BuildContext context) {
    return AppCard(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with status and date
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildStatusChip(),
                Text(
                  _formatDate(ride.createdAt),
                  style: AppTypography.bodySmall.copyWith(
                    color: AppColors.onSurfaceVariant,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppSpacing.sm),

            // Route information
            _buildRouteInfo(),

            const SizedBox(height: AppSpacing.sm),

            // Bottom row with fare and driver info
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '\$${ride.fare.toStringAsFixed(2)}',
                  style: AppTypography.titleMedium.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (ride.driverName != null) ...[
                  Row(
                    children: [
                      Icon(
                        Icons.person,
                        size: 16,
                        color: AppColors.onSurfaceVariant,
                      ),
                      const SizedBox(width: AppSpacing.xs),
                      Text(
                        ride.driverName!,
                        style: AppTypography.bodySmall.copyWith(
                          color: AppColors.onSurfaceVariant,
                        ),
                      ),
                      if (ride.driverRating != null) ...[
                        const SizedBox(width: AppSpacing.xs),
                        Icon(Icons.star, size: 14, color: AppColors.tertiary),
                        Text(
                          ride.driverRating!.toStringAsFixed(1),
                          style: AppTypography.bodySmall.copyWith(
                            color: AppColors.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip() {
    Color backgroundColor;
    Color textColor;
    String text;

    switch (ride.status) {
      case RideStatus.completed:
        backgroundColor = AppColors.successContainer;
        textColor = AppColors.onSuccessContainer;
        text = 'Completed';
        break;
      case RideStatus.cancelled:
        backgroundColor = AppColors.errorContainer;
        textColor = AppColors.onErrorContainer;
        text = 'Cancelled';
        break;
      case RideStatus.inProgress:
        backgroundColor = AppColors.primaryContainer;
        textColor = AppColors.onPrimaryContainer;
        text = 'In Progress';
        break;
      case RideStatus.accepted:
        backgroundColor = AppColors.secondaryContainer;
        textColor = AppColors.onSecondaryContainer;
        text = 'Accepted';
        break;
      case RideStatus.requested:
        backgroundColor = AppColors.tertiaryContainer;
        textColor = AppColors.onTertiaryContainer;
        text = 'Requested';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
        vertical: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(AppSpacing.sm),
      ),
      child: Text(
        text,
        style: AppTypography.labelSmall.copyWith(
          color: textColor,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildRouteInfo() {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: AppSpacing.sm),
                  Expanded(
                    child: Text(
                      ride.pickupName,
                      style: AppTypography.bodyMedium.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppSpacing.xs),
              Row(
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: AppColors.error,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: AppSpacing.sm),
                  Expanded(
                    child: Text(
                      ride.dropoffName,
                      style: AppTypography.bodyMedium,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        Icon(Icons.chevron_right, color: AppColors.onSurfaceVariant),
      ],
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}

/// Filter sheet for ride history
class RideHistoryFilterSheet extends ConsumerStatefulWidget {
  const RideHistoryFilterSheet({super.key});

  @override
  ConsumerState<RideHistoryFilterSheet> createState() =>
      _RideHistoryFilterSheetState();
}

class _RideHistoryFilterSheetState
    extends ConsumerState<RideHistoryFilterSheet> {
  RideStatus? _selectedStatus;
  DateRange? _selectedDateRange;

  @override
  void initState() {
    super.initState();
    final currentState = ref.read(rideHistoryNotifierProvider);
    _selectedStatus = currentState.statusFilter;
    _selectedDateRange = currentState.dateFilter;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        left: AppSpacing.md,
        right: AppSpacing.md,
        top: AppSpacing.md,
        bottom: MediaQuery.of(context).viewInsets.bottom + AppSpacing.md,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Filter Rides', style: AppTypography.headlineSmall),
              TextButton(
                onPressed: () {
                  setState(() {
                    _selectedStatus = null;
                    _selectedDateRange = null;
                  });
                },
                child: const Text('Clear all'),
              ),
            ],
          ),
          const SizedBox(height: AppSpacing.lg),

          // Status filter
          Text('Status', style: AppTypography.titleMedium),
          const SizedBox(height: AppSpacing.sm),
          Wrap(
            spacing: AppSpacing.sm,
            children: RideStatus.values.map((status) {
              final isSelected = _selectedStatus == status;
              return FilterChip(
                label: Text(_getStatusDisplayName(status)),
                selected: isSelected,
                onSelected: (selected) {
                  setState(() {
                    _selectedStatus = selected ? status : null;
                  });
                },
              );
            }).toList(),
          ),
          const SizedBox(height: AppSpacing.lg),

          // Date filter
          Text('Date Range', style: AppTypography.titleMedium),
          const SizedBox(height: AppSpacing.sm),
          Wrap(
            spacing: AppSpacing.sm,
            children: DateRanges.predefinedRanges.map((range) {
              final isSelected = _selectedDateRange?.label == range.label;
              return FilterChip(
                label: Text(range.label ?? range.formattedRange),
                selected: isSelected,
                onSelected: (selected) {
                  setState(() {
                    _selectedDateRange = selected ? range : null;
                  });
                },
              );
            }).toList(),
          ),
          const SizedBox(height: AppSpacing.xl),

          // Apply button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                ref
                    .read(rideHistoryNotifierProvider.notifier)
                    .setStatusFilter(_selectedStatus);
                ref
                    .read(rideHistoryNotifierProvider.notifier)
                    .setDateFilter(_selectedDateRange);
                Navigator.of(context).pop();
              },
              child: const Text('Apply Filters'),
            ),
          ),
        ],
      ),
    );
  }

  String _getStatusDisplayName(RideStatus status) {
    switch (status) {
      case RideStatus.requested:
        return 'Requested';
      case RideStatus.accepted:
        return 'Accepted';
      case RideStatus.inProgress:
        return 'In Progress';
      case RideStatus.completed:
        return 'Completed';
      case RideStatus.cancelled:
        return 'Cancelled';
    }
  }
}
