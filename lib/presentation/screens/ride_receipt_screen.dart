import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:auto_route/auto_route.dart';

import '../../core/theme/app_colors.dart';
import '../../core/theme/app_spacing.dart';
import '../../core/theme/app_typography.dart';
import '../../domain/entities/ride.dart';
import '../providers/ride_history_notifier.dart';
import '../providers/ride_history_state.dart';
import '../widgets/loading_components.dart';
import '../widgets/error_display_widget.dart';
import '../widgets/app_card.dart';

/// Screen for displaying detailed ride receipt with fare breakdown
@RoutePage()
class RideReceiptScreen extends ConsumerWidget {
  final String rideId;

  const RideReceiptScreen({super.key, required this.rideId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final detailsState = ref.watch(rideDetailsProvider(rideId));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Receipt'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.onPrimary,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () => _shareReceipt(context, detailsState.receipt),
            tooltip: 'Share receipt',
          ),
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: () => _downloadReceipt(context, detailsState.receipt),
            tooltip: 'Download receipt',
          ),
        ],
      ),
      body: _buildContent(context, ref, detailsState),
    );
  }

  Widget _buildContent(
    BuildContext context,
    WidgetRef ref,
    RideDetailsState state,
  ) {
    if (state.isLoadingReceipt) {
      return const Center(child: LoadingSpinner());
    }

    if (state.errorMessage != null) {
      return ErrorDisplayWidget(
        message: state.errorMessage!,
        onRetry: () => ref
            .read(rideDetailsProvider(rideId).notifier)
            .loadRideReceipt(rideId),
      );
    }

    if (!state.hasReceipt) {
      return const Center(child: Text('Receipt not available'));
    }

    final receipt = state.receipt!;
    final ride = state.ride;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Receipt header
          _buildReceiptHeader(receipt),
          const SizedBox(height: AppSpacing.lg),

          // Trip details
          _buildTripDetails(receipt, ride),
          const SizedBox(height: AppSpacing.md),

          // Fare breakdown
          _buildFareBreakdown(receipt),
          const SizedBox(height: AppSpacing.md),

          // Payment information
          _buildPaymentInfo(receipt),
          const SizedBox(height: AppSpacing.md),

          // Driver and vehicle info
          _buildDriverVehicleInfo(receipt),
          const SizedBox(height: AppSpacing.lg),

          // Action buttons
          _buildActionButtons(context, receipt),
        ],
      ),
    );
  }

  Widget _buildReceiptHeader(RideReceipt receipt) {
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          children: [
            // App logo or icon
            Container(
              width: 64,
              height: 64,
              decoration: BoxDecoration(
                color: AppColors.primary,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.local_taxi,
                color: AppColors.onPrimary,
                size: 32,
              ),
            ),
            const SizedBox(height: AppSpacing.md),

            Text(
              'Lucian Rides',
              style: AppTypography.headlineMedium.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSpacing.sm),

            Text('Receipt', style: AppTypography.titleLarge),
            const SizedBox(height: AppSpacing.sm),

            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppSpacing.md,
                vertical: AppSpacing.sm,
              ),
              decoration: BoxDecoration(
                color: AppColors.surfaceVariant,
                borderRadius: BorderRadius.circular(AppSpacing.sm),
              ),
              child: Text(
                'Receipt #${receipt.receiptNumber}',
                style: AppTypography.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const SizedBox(height: AppSpacing.sm),

            Text(
              _formatDateTime(receipt.paymentDate),
              style: AppTypography.bodySmall.copyWith(
                color: AppColors.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTripDetails(RideReceipt receipt, Ride? ride) {
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Trip Details', style: AppTypography.titleMedium),
            const SizedBox(height: AppSpacing.md),

            // Route
            _buildDetailRow('From', receipt.pickupLocation),
            const SizedBox(height: AppSpacing.sm),
            _buildDetailRow('To', receipt.dropoffLocation),
            const SizedBox(height: AppSpacing.sm),

            const Divider(),
            const SizedBox(height: AppSpacing.sm),

            // Trip metrics
            Row(
              children: [
                Expanded(
                  child: _buildMetricCard(
                    'Distance',
                    '${receipt.distanceKm.toStringAsFixed(1)} km',
                    Icons.straighten,
                  ),
                ),
                const SizedBox(width: AppSpacing.sm),
                Expanded(
                  child: _buildMetricCard(
                    'Duration',
                    _formatDuration(receipt.durationMinutes),
                    Icons.access_time,
                  ),
                ),
              ],
            ),

            if (ride != null) ...[
              const SizedBox(height: AppSpacing.sm),
              _buildDetailRow('Ride ID', ride.id),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFareBreakdown(RideReceipt receipt) {
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Fare Breakdown', style: AppTypography.titleMedium),
            const SizedBox(height: AppSpacing.md),

            // Base fare
            _buildFareRow('Base Fare', receipt.baseFare),

            // Additional fees (if any)
            if (receipt.additionalFees > 0) ...[
              const SizedBox(height: AppSpacing.sm),
              _buildFareRow('Additional Fees', receipt.additionalFees),
            ],

            // Tax
            const SizedBox(height: AppSpacing.sm),
            _buildFareRow('Tax', receipt.tax),

            const SizedBox(height: AppSpacing.sm),
            const Divider(),
            const SizedBox(height: AppSpacing.sm),

            // Total
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Total',
                  style: AppTypography.titleLarge.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '\$${receipt.totalAmount.toStringAsFixed(2)}',
                  style: AppTypography.titleLarge.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentInfo(RideReceipt receipt) {
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Payment Information', style: AppTypography.titleMedium),
            const SizedBox(height: AppSpacing.md),

            _buildDetailRow('Payment Method', receipt.paymentMethod),
            const SizedBox(height: AppSpacing.sm),
            _buildDetailRow(
              'Payment Date',
              _formatDateTime(receipt.paymentDate),
            ),
            const SizedBox(height: AppSpacing.sm),

            Row(
              children: [
                Icon(Icons.check_circle, color: AppColors.success, size: 16),
                const SizedBox(width: AppSpacing.xs),
                Text(
                  'Payment Successful',
                  style: AppTypography.bodySmall.copyWith(
                    color: AppColors.success,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDriverVehicleInfo(RideReceipt receipt) {
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Driver & Vehicle', style: AppTypography.titleMedium),
            const SizedBox(height: AppSpacing.md),

            Row(
              children: [
                CircleAvatar(
                  radius: 24,
                  backgroundColor: AppColors.primaryContainer,
                  child: Icon(
                    Icons.person,
                    color: AppColors.onPrimaryContainer,
                  ),
                ),
                const SizedBox(width: AppSpacing.md),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        receipt.driverName,
                        style: AppTypography.bodyLarge.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        receipt.vehicleInfo,
                        style: AppTypography.bodyMedium.copyWith(
                          color: AppColors.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context, RideReceipt receipt) {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () => _downloadReceipt(context, receipt),
            icon: const Icon(Icons.download),
            label: const Text('Download PDF'),
          ),
        ),
        const SizedBox(height: AppSpacing.sm),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () => _shareReceipt(context, receipt),
            icon: const Icon(Icons.share),
            label: const Text('Share Receipt'),
          ),
        ),
        const SizedBox(height: AppSpacing.sm),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () => _emailReceipt(context, receipt),
            icon: const Icon(Icons.email),
            label: const Text('Email Receipt'),
          ),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            label,
            style: AppTypography.bodySmall.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
          ),
        ),
        Expanded(child: Text(value, style: AppTypography.bodyMedium)),
      ],
    );
  }

  Widget _buildFareRow(String label, double amount) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(label, style: AppTypography.bodyMedium),
        Text(
          '\$${amount.toStringAsFixed(2)}',
          style: AppTypography.bodyMedium.copyWith(fontWeight: FontWeight.w500),
        ),
      ],
    );
  }

  Widget _buildMetricCard(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.sm),
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant,
        borderRadius: BorderRadius.circular(AppSpacing.sm),
      ),
      child: Column(
        children: [
          Icon(icon, color: AppColors.primary, size: 20),
          const SizedBox(height: AppSpacing.xs),
          Text(
            value,
            style: AppTypography.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          Text(
            label,
            style: AppTypography.bodySmall.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} at ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  String _formatDuration(int minutes) {
    if (minutes < 60) {
      return '$minutes min';
    } else {
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      return '${hours}h ${remainingMinutes}min';
    }
  }

  void _shareReceipt(BuildContext context, RideReceipt? receipt) {
    if (receipt == null) return;

    // In a real app, this would use the share package
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share receipt functionality would be implemented here'),
      ),
    );
  }

  void _downloadReceipt(BuildContext context, RideReceipt? receipt) {
    if (receipt == null) return;

    // In a real app, this would generate and download a PDF
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text(
          'Download receipt functionality would be implemented here',
        ),
      ),
    );
  }

  void _emailReceipt(BuildContext context, RideReceipt? receipt) {
    if (receipt == null) return;

    // In a real app, this would open email client or send email
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Email receipt functionality would be implemented here'),
      ),
    );
  }
}
