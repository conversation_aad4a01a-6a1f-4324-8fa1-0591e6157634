import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:auto_route/auto_route.dart';

import '../../core/theme/app_colors.dart';
import '../../core/theme/app_spacing.dart';
import '../../core/utils/haptic_feedback_utils.dart';
import '../../domain/entities/ride.dart';
import '../providers/ride_tracking_notifier.dart';
import '../providers/ride_tracking_state.dart';

import '../widgets/error_display_widget.dart';
import '../widgets/custom_button.dart';
import '../widgets/ride_cancellation_button.dart';
import '../widgets/enhanced_loading_states.dart';
import '../widgets/smooth_transitions.dart';

/// Screen for tracking an active ride with real-time updates
@RoutePage()
class RideTrackingScreen extends ConsumerStatefulWidget {
  /// The ID of the ride to track
  final String rideId;

  const RideTrackingScreen({super.key, required this.rideId});

  @override
  ConsumerState<RideTrackingScreen> createState() => _RideTrackingScreenState();
}

class _RideTrackingScreenState extends ConsumerState<RideTrackingScreen> {
  @override
  void initState() {
    super.initState();
    // Initialize tracking when the screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref
          .read(rideTrackingNotifierProvider.notifier)
          .initializeTracking(widget.rideId);
    });
  }

  @override
  Widget build(BuildContext context) {
    final trackingState = ref.watch(rideTrackingNotifierProvider);
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Track Your Ride'),
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
        actions: [
          // Refresh button
          IconButton(
            onPressed: trackingState.isLoading
                ? null
                : () {
                    HapticFeedbackUtils.refresh();
                    ref
                        .read(rideTrackingNotifierProvider.notifier)
                        .refreshRideData();
                  },
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _buildBody(context, trackingState),
    );
  }

  Widget _buildBody(BuildContext context, RideTrackingState state) {
    if (state.isLoadingRide && !state.hasActiveRide) {
      return const DriverTrackingLoader();
    }

    if (state.errorMessage != null && !state.hasActiveRide) {
      return Center(
        child: ErrorDisplayWidget(
          message: state.errorMessage!,
          onRetry: () => ref
              .read(rideTrackingNotifierProvider.notifier)
              .initializeTracking(widget.rideId),
        ),
      );
    }

    if (!state.hasActiveRide) {
      return const Center(child: Text('No active ride found'));
    }

    return RefreshIndicator(
      onRefresh: () async {
        await ref.read(rideTrackingNotifierProvider.notifier).refreshRideData();
      },
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Connection status indicator
            _buildConnectionStatus(state),
            const SizedBox(height: AppSpacing.md),

            // Ride status card
            _buildRideStatusCard(state),
            const SizedBox(height: AppSpacing.md),

            // Cancellation card (if ride can be cancelled)
            if (state.canCancelRide) ...[
              RideCancellationCard(
                ride: state.activeRide!,
                onCancellationComplete: () => _handleCancellationComplete(),
              ),
              const SizedBox(height: AppSpacing.md),
            ],

            // Driver information (if available)
            if (state.hasDriverInfo) ...[
              _buildDriverInfoCard(state),
              const SizedBox(height: AppSpacing.md),
            ],

            // Trip progress (if in progress)
            if (state.activeRide!.status == RideStatus.inProgress &&
                state.tripProgress != null) ...[
              _buildTripProgressCard(state),
              const SizedBox(height: AppSpacing.md),
            ],

            // ETA information (if available)
            if (state.hasETA) ...[
              _buildETACard(state),
              const SizedBox(height: AppSpacing.md),
            ],

            // Trip details card
            _buildTripDetailsCard(state),
            const SizedBox(height: AppSpacing.md),

            // Action buttons
            _buildActionButtons(context, state),

            // Error message (if any)
            if (state.errorMessage != null) ...[
              const SizedBox(height: AppSpacing.md),
              ErrorDisplayWidget(
                message: state.errorMessage!,
                onRetry: () => ref
                    .read(rideTrackingNotifierProvider.notifier)
                    .clearErrors(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildConnectionStatus(RideTrackingState state) {
    final theme = Theme.of(context);

    Color statusColor;
    IconData statusIcon;
    String statusText;

    switch (state.connectionStatus) {
      case ConnectionStatus.connected:
        statusColor = AppColors.success;
        statusIcon = Icons.wifi;
        statusText = 'Connected';
        break;
      case ConnectionStatus.connecting:
        statusColor = AppColors.warning;
        statusIcon = Icons.wifi_find;
        statusText = 'Connecting...';
        break;
      case ConnectionStatus.reconnecting:
        statusColor = AppColors.warning;
        statusIcon = Icons.wifi_find;
        statusText = 'Reconnecting...';
        break;
      case ConnectionStatus.failed:
        statusColor = AppColors.error;
        statusIcon = Icons.wifi_off;
        statusText = 'Connection failed';
        break;
      case ConnectionStatus.disconnected:
        statusColor = theme.colorScheme.outline;
        statusIcon = Icons.wifi_off;
        statusText = 'Disconnected';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
        vertical: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppSpacing.xs),
        border: Border.all(color: statusColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(statusIcon, size: 16, color: statusColor),
          const SizedBox(width: AppSpacing.xs),
          Text(
            statusText,
            style: theme.textTheme.bodySmall?.copyWith(
              color: statusColor,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (state.lastUpdated != null) ...[
            const SizedBox(width: AppSpacing.sm),
            Text(
              'Updated ${_formatLastUpdated(state.lastUpdated!)}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.outline,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildRideStatusCard(RideTrackingState state) {
    final theme = Theme.of(context);
    final ride = state.activeRide!;

    Color statusColor;
    IconData statusIcon;

    switch (ride.status) {
      case RideStatus.requested:
        statusColor = AppColors.warning;
        statusIcon = Icons.search;
        break;
      case RideStatus.accepted:
        statusColor = AppColors.info;
        statusIcon = Icons.directions_car;
        break;
      case RideStatus.inProgress:
        statusColor = AppColors.success;
        statusIcon = Icons.navigation;
        break;
      case RideStatus.completed:
        statusColor = AppColors.success;
        statusIcon = Icons.check_circle;
        break;
      case RideStatus.cancelled:
        statusColor = AppColors.error;
        statusIcon = Icons.cancel;
        break;
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(AppSpacing.sm),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppSpacing.sm),
                  ),
                  child: Icon(statusIcon, color: statusColor, size: 24),
                ),
                const SizedBox(width: AppSpacing.md),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        state.statusDisplayText,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      if (ride.status == RideStatus.accepted && state.hasETA)
                        Text(
                          'ETA: ${state.formattedETA}',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                    ],
                  ),
                ),
                if (state.isUpdatingStatus)
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDriverInfoCard(RideTrackingState state) {
    final theme = Theme.of(context);
    final driverInfo = state.driverInfo!;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Your Driver',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppSpacing.md),
            Row(
              children: [
                // Driver avatar
                CircleAvatar(
                  radius: 30,
                  backgroundColor: theme.colorScheme.primary.withValues(
                    alpha: 0.1,
                  ),
                  backgroundImage: driverInfo.photoUrl != null
                      ? NetworkImage(driverInfo.photoUrl!)
                      : null,
                  child: driverInfo.photoUrl == null
                      ? Icon(
                          Icons.person,
                          size: 30,
                          color: theme.colorScheme.primary,
                        )
                      : null,
                ),
                const SizedBox(width: AppSpacing.md),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        driverInfo.displayName,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      if (driverInfo.rating != null)
                        Text(
                          driverInfo.formattedRating,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.outline,
                          ),
                        ),
                      if (driverInfo.vehicleInfo != null) ...[
                        const SizedBox(height: AppSpacing.xs),
                        Text(
                          driverInfo.vehicleInfo!.shortDescription,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          driverInfo.vehicleInfo!.licensePlate,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.outline,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                // Contact button
                IconButton(
                  onPressed: () => _showContactOptions(context, driverInfo),
                  icon: const Icon(Icons.phone),
                  tooltip: 'Contact Driver',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTripProgressCard(RideTrackingState state) {
    final theme = Theme.of(context);
    final progress = state.tripProgress!;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Trip Progress',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppSpacing.md),

            // Progress bar
            LinearProgressIndicator(
              value: progress.progressPercentage / 100,
              backgroundColor: theme.colorScheme.outline.withValues(alpha: 0.2),
              valueColor: AlwaysStoppedAnimation<Color>(
                theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: AppSpacing.sm),

            // Progress text
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  progress.formattedProgress,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  progress.formattedRemainingTime,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.outline,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildETACard(RideTrackingState state) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Row(
          children: [
            Icon(Icons.access_time, color: theme.colorScheme.primary),
            const SizedBox(width: AppSpacing.md),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Estimated Arrival',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.outline,
                    ),
                  ),
                  Text(
                    state.formattedETA!,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ),
            if (state.distanceKm != null)
              Text(
                '${state.distanceKm!.toStringAsFixed(1)} km',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.outline,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTripDetailsCard(RideTrackingState state) {
    final theme = Theme.of(context);
    final ride = state.activeRide!;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Trip Details',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppSpacing.md),

            // Pickup location
            _buildLocationRow(
              context,
              Icons.radio_button_checked,
              'Pickup',
              ride.pickupLocation.name,
              ride.pickupLocation.address,
              AppColors.success,
            ),
            const SizedBox(height: AppSpacing.sm),

            // Dropoff location
            _buildLocationRow(
              context,
              Icons.location_on,
              'Dropoff',
              ride.dropoffLocation.name,
              ride.dropoffLocation.address,
              AppColors.error,
            ),

            if (ride.specialInstructions?.isNotEmpty == true) ...[
              const SizedBox(height: AppSpacing.md),
              const Divider(),
              const SizedBox(height: AppSpacing.sm),
              Text(
                'Special Instructions',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: AppSpacing.xs),
              Text(
                ride.specialInstructions!,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.outline,
                ),
              ),
            ],

            const SizedBox(height: AppSpacing.md),
            const Divider(),
            const SizedBox(height: AppSpacing.sm),

            // Fare information
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Fare',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  '\$${ride.fixedFare.toStringAsFixed(2)}',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationRow(
    BuildContext context,
    IconData icon,
    String label,
    String name,
    String? address,
    Color iconColor,
  ) {
    final theme = Theme.of(context);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, color: iconColor, size: 20),
        const SizedBox(width: AppSpacing.sm),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.outline,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                name,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (address?.isNotEmpty == true)
                Text(
                  address!,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.outline,
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context, RideTrackingState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Emergency/Support button
        CustomButton(
          onPressed: () => _showHelpOptions(context),
          variant: ButtonVariant.secondary,
          icon: Icons.help_outline,
          child: const Text('Get Help'),
        ),
      ],
    );
  }

  void _showContactOptions(BuildContext context, DriverInfo driverInfo) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Contact ${driverInfo.displayName}',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: AppSpacing.md),

            if (driverInfo.phoneNumber != null)
              ListTile(
                leading: const Icon(Icons.phone),
                title: const Text('Call Driver'),
                subtitle: Text(driverInfo.phoneNumber!),
                onTap: () {
                  Navigator.pop(context);
                  // TODO: Implement phone call functionality
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Phone call functionality not implemented'),
                    ),
                  );
                },
              ),

            ListTile(
              leading: const Icon(Icons.message),
              title: const Text('Send Message'),
              subtitle: const Text('Send a message to your driver'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Implement messaging functionality
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Messaging functionality not implemented'),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  /// Handle successful ride cancellation
  void _handleCancellationComplete() {
    // Success haptic feedback
    HapticFeedbackUtils.success();

    // Refresh the ride data to get updated status
    ref.read(rideTrackingNotifierProvider.notifier).refreshRideData();

    // Show success message with smooth snackbar
    SmoothSnackBar.show(
      context: context,
      message: 'Ride cancelled successfully',
      type: SnackBarType.success,
    );
  }

  void _showHelpOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Get Help',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: AppSpacing.md),

            ListTile(
              leading: const Icon(Icons.support_agent),
              title: const Text('Contact Support'),
              subtitle: const Text('Get help from our support team'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Implement support contact functionality
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text(
                      'Support contact functionality not implemented',
                    ),
                  ),
                );
              },
            ),

            ListTile(
              leading: const Icon(Icons.emergency, color: AppColors.error),
              title: const Text('Emergency'),
              subtitle: const Text('Call emergency services'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Implement emergency call functionality
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text(
                      'Emergency call functionality not implemented',
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  String _formatLastUpdated(DateTime lastUpdated) {
    final now = DateTime.now();
    final difference = now.difference(lastUpdated);

    if (difference.inSeconds < 60) {
      return 'just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else {
      return '${difference.inHours}h ago';
    }
  }
}
