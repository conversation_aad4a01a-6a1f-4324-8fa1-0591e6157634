import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../core/theme/accessibility_colors.dart';
import '../../core/constants/app_constants.dart';

/// Widget that provides accessible focus management and keyboard navigation
class AccessibleFocusWidget extends StatefulWidget {
  const AccessibleFocusWidget({
    super.key,
    required this.child,
    this.onTap,
    this.onFocusChange,
    this.semanticLabel,
    this.semanticHint,
    this.isButton = false,
    this.autofocus = false,
    this.focusNode,
    this.canRequestFocus = true,
  });

  final Widget child;
  final VoidCallback? onTap;
  final ValueChanged<bool>? onFocusChange;
  final String? semanticLabel;
  final String? semanticHint;
  final bool isButton;
  final bool autofocus;
  final FocusNode? focusNode;
  final bool canRequestFocus;

  @override
  State<AccessibleFocusWidget> createState() => _AccessibleFocusWidgetState();
}

class _AccessibleFocusWidgetState extends State<AccessibleFocusWidget> {
  late FocusNode _focusNode;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      _focusNode.dispose();
    } else {
      _focusNode.removeListener(_onFocusChange);
    }
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });
    widget.onFocusChange?.call(_isFocused);
  }

  void _handleKeyEvent(KeyEvent event) {
    if (event is KeyDownEvent) {
      if (event.logicalKey == LogicalKeyboardKey.enter ||
          event.logicalKey == LogicalKeyboardKey.space) {
        widget.onTap?.call();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget child = Container(
      decoration: _isFocused
          ? BoxDecoration(
              border: Border.all(
                color: AccessibilityColors.getFocusIndicator(context),
                width: 2,
              ),
              borderRadius: BorderRadius.circular(4),
            )
          : null,
      child: widget.child,
    );

    if (widget.onTap != null) {
      child = GestureDetector(onTap: widget.onTap, child: child);
    }

    child = Focus(
      focusNode: _focusNode,
      autofocus: widget.autofocus,
      canRequestFocus: widget.canRequestFocus,
      onKeyEvent: (node, event) {
        _handleKeyEvent(event);
        return KeyEventResult.handled;
      },
      child: child,
    );

    if (widget.semanticLabel != null || widget.semanticHint != null) {
      child = Semantics(
        label: widget.semanticLabel,
        hint: widget.semanticHint,
        button: widget.isButton,
        focusable: true,
        focused: _isFocused,
        child: child,
      );
    }

    return child;
  }
}

/// Skip link widget for keyboard navigation
class SkipLink extends StatelessWidget {
  const SkipLink({super.key, required this.targetKey, required this.label});

  final GlobalKey targetKey;
  final String label;

  void _skipToTarget() {
    final context = targetKey.currentContext;
    if (context != null) {
      Scrollable.ensureVisible(
        context,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );

      // Focus the target if it's focusable
      final focusNode = Focus.of(context);
      focusNode.requestFocus();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: -100, // Hidden by default
      left: 0,
      child: Focus(
        onFocusChange: (hasFocus) {
          // Show skip link when focused
        },
        child: Container(
          padding: const EdgeInsets.all(8),
          color: AccessibilityColors.getFocusIndicator(context),
          child: GestureDetector(
            onTap: _skipToTarget,
            child: Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// Accessible tab navigation widget
class AccessibleTabBar extends StatefulWidget {
  const AccessibleTabBar({
    super.key,
    required this.tabs,
    required this.onTabChanged,
    this.currentIndex = 0,
  });

  final List<AccessibleTab> tabs;
  final ValueChanged<int> onTabChanged;
  final int currentIndex;

  @override
  State<AccessibleTabBar> createState() => _AccessibleTabBarState();
}

class _AccessibleTabBarState extends State<AccessibleTabBar> {
  late List<FocusNode> _focusNodes;

  @override
  void initState() {
    super.initState();
    _focusNodes = List.generate(widget.tabs.length, (index) => FocusNode());
  }

  @override
  void dispose() {
    for (final node in _focusNodes) {
      node.dispose();
    }
    super.dispose();
  }

  void _handleKeyEvent(KeyEvent event, int index) {
    if (event is KeyDownEvent) {
      if (event.logicalKey == LogicalKeyboardKey.arrowLeft && index > 0) {
        _focusNodes[index - 1].requestFocus();
      } else if (event.logicalKey == LogicalKeyboardKey.arrowRight &&
          index < widget.tabs.length - 1) {
        _focusNodes[index + 1].requestFocus();
      } else if (event.logicalKey == LogicalKeyboardKey.enter ||
          event.logicalKey == LogicalKeyboardKey.space) {
        widget.onTabChanged(index);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: 'Tab navigation with ${widget.tabs.length} tabs',
      child: Row(
        children: widget.tabs.asMap().entries.map((entry) {
          final index = entry.key;
          final tab = entry.value;
          final isSelected = index == widget.currentIndex;

          return Expanded(
            child: Focus(
              focusNode: _focusNodes[index],
              onKeyEvent: (node, event) {
                _handleKeyEvent(event, index);
                return KeyEventResult.handled;
              },
              child: Semantics(
                label: tab.label,
                hint: isSelected ? 'Selected tab' : 'Tab',
                button: true,
                selected: isSelected,
                child: GestureDetector(
                  onTap: () => widget.onTabChanged(index),
                  child: Container(
                    constraints: const BoxConstraints(
                      minHeight: AppConstants.minTouchTarget,
                    ),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? AccessibilityColors.getPrimary(context)
                          : Colors.transparent,
                      border: _focusNodes[index].hasFocus
                          ? Border.all(
                              color: AccessibilityColors.getFocusIndicator(
                                context,
                              ),
                              width: 2,
                            )
                          : null,
                    ),
                    child: Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (tab.icon != null)
                            Icon(
                              tab.icon,
                              color: isSelected
                                  ? Colors.white
                                  : AccessibilityColors.getTextSecondary(
                                      context,
                                    ),
                            ),
                          if (tab.label.isNotEmpty)
                            Text(
                              tab.label,
                              style: TextStyle(
                                color: isSelected
                                    ? Colors.white
                                    : AccessibilityColors.getTextSecondary(
                                        context,
                                      ),
                                fontWeight: isSelected
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}

/// Accessible tab data class
class AccessibleTab {
  const AccessibleTab({required this.label, this.icon});

  final String label;
  final IconData? icon;
}

/// Accessible form field wrapper
class AccessibleFormField extends StatelessWidget {
  const AccessibleFormField({
    super.key,
    required this.child,
    required this.label,
    this.hint,
    this.errorText,
    this.isRequired = false,
  });

  final Widget child;
  final String label;
  final String? hint;
  final String? errorText;
  final bool isRequired;

  @override
  Widget build(BuildContext context) {
    String semanticLabel = label;
    if (isRequired) {
      semanticLabel += ', required';
    }

    String? semanticHint = hint;
    if (errorText != null) {
      semanticHint = 'Error: $errorText';
    }

    return Semantics(
      label: semanticLabel,
      hint: semanticHint,
      textField: true,
      child: child,
    );
  }
}
