import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_typography.dart';

/// Badge variant types
enum BadgeVariant { notification, status, count }

/// Custom badge widget with consistent styling
class AppBadge extends StatelessWidget {
  const AppBadge({
    super.key,
    required this.child,
    this.text,
    this.count,
    this.variant = BadgeVariant.notification,
    this.backgroundColor,
    this.textColor,
    this.showBadge = true,
    this.position,
  });

  final Widget child;
  final String? text;
  final int? count;
  final BadgeVariant variant;
  final Color? backgroundColor;
  final Color? textColor;
  final bool showBadge;
  final BadgePosition? position;

  @override
  Widget build(BuildContext context) {
    if (!showBadge) return child;

    final badgeConfig = _getBadgeConfig();
    final badgeText = _getBadgeText();

    if (badgeText == null || badgeText.isEmpty) return child;

    return Stack(
      clipBehavior: Clip.none,
      children: [
        child,
        Positioned(
          top: position?.top ?? -8,
          right: position?.right ?? -8,
          child: Container(
            constraints: const BoxConstraints(minWidth: 20, minHeight: 20),
            padding: badgeConfig.padding,
            decoration: BoxDecoration(
              color: backgroundColor ?? badgeConfig.backgroundColor,
              borderRadius: BorderRadius.circular(badgeConfig.borderRadius),
              border: badgeConfig.border,
            ),
            child: Center(
              child: Text(
                badgeText,
                style: AppTypography.labelSmall.copyWith(
                  color: textColor ?? badgeConfig.textColor,
                  fontWeight: FontWeight.bold,
                  fontSize: badgeConfig.fontSize,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ),
      ],
    );
  }

  String? _getBadgeText() {
    switch (variant) {
      case BadgeVariant.notification:
        return text ?? '';
      case BadgeVariant.status:
        return text ?? '';
      case BadgeVariant.count:
        if (count == null) return null;
        return count! > 99 ? '99+' : count.toString();
    }
  }

  _BadgeConfig _getBadgeConfig() {
    switch (variant) {
      case BadgeVariant.notification:
        return const _BadgeConfig(
          backgroundColor: AppColors.error,
          textColor: AppColors.white,
          padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
          borderRadius: 10,
          fontSize: 10,
        );

      case BadgeVariant.status:
        return const _BadgeConfig(
          backgroundColor: AppColors.success,
          textColor: AppColors.white,
          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          borderRadius: 12,
          fontSize: 11,
        );

      case BadgeVariant.count:
        return const _BadgeConfig(
          backgroundColor: AppColors.error,
          textColor: AppColors.white,
          padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
          borderRadius: 10,
          fontSize: 10,
          border: Border.fromBorderSide(
            BorderSide(color: AppColors.white, width: 2),
          ),
        );
    }
  }
}

/// Badge position configuration
class BadgePosition {
  const BadgePosition({this.top, this.right, this.bottom, this.left});

  final double? top;
  final double? right;
  final double? bottom;
  final double? left;
}

/// Badge configuration class
class _BadgeConfig {
  const _BadgeConfig({
    required this.backgroundColor,
    required this.textColor,
    required this.padding,
    required this.borderRadius,
    required this.fontSize,
    this.border,
  });

  final Color backgroundColor;
  final Color textColor;
  final EdgeInsets padding;
  final double borderRadius;
  final double fontSize;
  final Border? border;
}

/// Notification badge for icons
class NotificationBadge extends StatelessWidget {
  const NotificationBadge({
    super.key,
    required this.child,
    this.count = 0,
    this.showZero = false,
  });

  final Widget child;
  final int count;
  final bool showZero;

  @override
  Widget build(BuildContext context) {
    return AppBadge(
      variant: BadgeVariant.count,
      count: count,
      showBadge: count > 0 || (count == 0 && showZero),
      child: child,
    );
  }
}

/// Status badge for indicating states
class StatusBadge extends StatelessWidget {
  const StatusBadge({super.key, required this.status, this.color});

  final String status;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    final statusColor = color ?? _getStatusColor(status);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: statusColor.withValues(alpha: 0.3)),
      ),
      child: Text(
        status,
        style: AppTypography.labelSmall.copyWith(
          color: statusColor,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
      case 'completed':
      case 'success':
        return AppColors.success;
      case 'pending':
      case 'warning':
        return AppColors.warning;
      case 'inactive':
      case 'failed':
      case 'error':
        return AppColors.error;
      case 'info':
      case 'processing':
        return AppColors.info;
      default:
        return AppColors.primary;
    }
  }
}

/// Chip widget for tags and filters
class AppChip extends StatelessWidget {
  const AppChip({
    super.key,
    required this.label,
    this.onTap,
    this.onDeleted,
    this.isSelected = false,
    this.avatar,
    this.deleteIcon,
    this.backgroundColor,
    this.selectedColor,
    this.textColor,
  });

  final String label;
  final VoidCallback? onTap;
  final VoidCallback? onDeleted;
  final bool isSelected;
  final Widget? avatar;
  final Widget? deleteIcon;
  final Color? backgroundColor;
  final Color? selectedColor;
  final Color? textColor;

  @override
  Widget build(BuildContext context) {
    final effectiveBackgroundColor = isSelected
        ? (selectedColor ?? AppColors.primary)
        : (backgroundColor ?? AppColors.backgroundSecondary);
    final effectiveTextColor = isSelected
        ? AppColors.white
        : (textColor ?? AppColors.textPrimary);

    return Material(
      color: effectiveBackgroundColor,
      borderRadius: BorderRadius.circular(20),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (avatar != null) ...[avatar!, const SizedBox(width: 6)],
              Text(
                label,
                style: AppTypography.labelMedium.copyWith(
                  color: effectiveTextColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (onDeleted != null) ...[
                const SizedBox(width: 6),
                GestureDetector(
                  onTap: onDeleted,
                  child:
                      deleteIcon ??
                      Icon(Icons.close, size: 16, color: effectiveTextColor),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

/// Choice chip for single selection
class ChoiceChip extends StatelessWidget {
  const ChoiceChip({
    super.key,
    required this.label,
    required this.isSelected,
    required this.onSelected,
    this.avatar,
  });

  final String label;
  final bool isSelected;
  final ValueChanged<bool> onSelected;
  final Widget? avatar;

  @override
  Widget build(BuildContext context) {
    return AppChip(
      label: label,
      isSelected: isSelected,
      onTap: () => onSelected(!isSelected),
      avatar: avatar,
    );
  }
}

/// Filter chip for multiple selection
class FilterChip extends StatelessWidget {
  const FilterChip({
    super.key,
    required this.label,
    required this.isSelected,
    required this.onSelected,
    this.avatar,
  });

  final String label;
  final bool isSelected;
  final ValueChanged<bool> onSelected;
  final Widget? avatar;

  @override
  Widget build(BuildContext context) {
    return AppChip(
      label: label,
      isSelected: isSelected,
      onTap: () => onSelected(!isSelected),
      avatar: avatar,
      backgroundColor: AppColors.backgroundSecondary,
      selectedColor: AppColors.primary.withValues(alpha: 0.2),
      textColor: isSelected ? AppColors.primary : AppColors.textPrimary,
    );
  }
}
