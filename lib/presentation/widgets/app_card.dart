import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';
import '../../core/constants/app_constants.dart';

/// Card variant types
enum CardVariant { elevated, outlined, filled }

/// Custom card widget with consistent styling
class AppCard extends StatefulWidget {
  const AppCard({
    super.key,
    required this.child,
    this.variant = CardVariant.elevated,
    this.onTap,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.borderColor,
    this.borderRadius,
    this.elevation,
    this.width,
    this.height,
    this.isInteractive = false,
  });

  final Widget child;
  final CardVariant variant;
  final VoidCallback? onTap;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final Color? backgroundColor;
  final Color? borderColor;
  final BorderRadius? borderRadius;
  final double? elevation;
  final double? width;
  final double? height;
  final bool isInteractive;

  @override
  State<AppCard> createState() => _AppCardState();
}

class _AppCardState extends State<AppCard> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  // bool _isPressed = false; // Commented out as it's not used

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _scaleAnimation =
        Tween<double>(begin: 1.0, end: AppConstants.cardHoverScale).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeInOut,
          ),
        );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    if (widget.isInteractive && widget.onTap != null) {
      _animationController.forward();
    }
  }

  void _onTapUp(TapUpDetails details) {
    if (widget.isInteractive && widget.onTap != null) {
      _animationController.reverse();
    }
  }

  void _onTapCancel() {
    if (widget.isInteractive && widget.onTap != null) {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final cardConfig = _getCardConfig();

    Widget card = AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: widget.isInteractive ? _scaleAnimation.value : 1.0,
          child: Container(
            width: widget.width,
            height: widget.height,
            margin: widget.margin ?? cardConfig.margin,
            constraints: widget.onTap != null
                ? const BoxConstraints(
                    minWidth: AppConstants.minTouchTarget,
                    minHeight: AppConstants.minTouchTarget,
                  )
                : null,
            decoration: BoxDecoration(
              color: widget.backgroundColor ?? cardConfig.backgroundColor,
              borderRadius: widget.borderRadius ?? cardConfig.borderRadius,
              border: cardConfig.border,
              boxShadow: cardConfig.boxShadow,
            ),
            child: Material(
              color: Colors.transparent,
              child: widget.onTap != null
                  ? InkWell(
                      onTap: widget.onTap,
                      onTapDown: _onTapDown,
                      onTapUp: _onTapUp,
                      onTapCancel: _onTapCancel,
                      borderRadius:
                          widget.borderRadius ?? cardConfig.borderRadius,
                      child: Container(
                        padding: widget.padding ?? cardConfig.padding,
                        child: widget.child,
                      ),
                    )
                  : Container(
                      padding: widget.padding ?? cardConfig.padding,
                      child: widget.child,
                    ),
            ),
          ),
        );
      },
    );

    // Add accessibility semantics for interactive cards
    if (widget.onTap != null) {
      card = Semantics(button: true, enabled: true, child: card);
    }

    return card;
  }

  _CardConfig _getCardConfig() {
    switch (widget.variant) {
      case CardVariant.elevated:
        return _CardConfig(
          backgroundColor: AppColors.surface,
          borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          margin: const EdgeInsets.all(AppConstants.defaultMargin),
          boxShadow: [
            BoxShadow(
              color: AppColors.shadow,
              blurRadius: widget.elevation ?? AppConstants.cardElevation,
              offset: Offset(
                0,
                (widget.elevation ?? AppConstants.cardElevation) / 2,
              ),
            ),
          ],
        );

      case CardVariant.outlined:
        return _CardConfig(
          backgroundColor: AppColors.surface,
          borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          margin: const EdgeInsets.all(AppConstants.defaultMargin),
          border: Border.all(
            color: widget.borderColor ?? AppColors.border,
            width: 1,
          ),
        );

      case CardVariant.filled:
        return _CardConfig(
          backgroundColor: AppColors.backgroundSecondary,
          borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          margin: const EdgeInsets.all(AppConstants.defaultMargin),
        );
    }
  }
}

/// Card configuration class
class _CardConfig {
  const _CardConfig({
    required this.backgroundColor,
    required this.borderRadius,
    required this.padding,
    required this.margin,
    this.border,
    this.boxShadow,
  });

  final Color backgroundColor;
  final BorderRadius borderRadius;
  final EdgeInsets padding;
  final EdgeInsets margin;
  final Border? border;
  final List<BoxShadow>? boxShadow;
}

/// List item card with consistent styling
class ListItemCard extends StatelessWidget {
  const ListItemCard({
    super.key,
    required this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.onTap,
    this.height,
  });

  final String title;
  final String? subtitle;
  final Widget? leading;
  final Widget? trailing;
  final VoidCallback? onTap;
  final double? height;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return AppCard(
      variant: CardVariant.elevated,
      onTap: onTap,
      isInteractive: onTap != null,
      height: height ?? 72,
      margin: const EdgeInsets.symmetric(
        horizontal: AppConstants.defaultMargin,
        vertical: 4,
      ),
      child: Row(
        children: [
          if (leading != null) ...[leading!, const SizedBox(width: 12)],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    subtitle!,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
          if (trailing != null) ...[const SizedBox(width: 12), trailing!],
        ],
      ),
    );
  }
}

/// Info card with icon and content
class InfoCard extends StatelessWidget {
  const InfoCard({
    super.key,
    required this.title,
    required this.content,
    this.icon,
    this.iconColor,
    this.onTap,
  });

  final String title;
  final String content;
  final IconData? icon;
  final Color? iconColor;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return AppCard(
      variant: CardVariant.elevated,
      onTap: onTap,
      isInteractive: onTap != null,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (icon != null) ...[
                Icon(icon, color: iconColor ?? AppColors.primary, size: 24),
                const SizedBox(width: 12),
              ],
              Expanded(
                child: Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(content, style: theme.textTheme.bodyMedium),
        ],
      ),
    );
  }
}

/// Status card with colored indicator
class StatusCard extends StatelessWidget {
  const StatusCard({
    super.key,
    required this.title,
    required this.status,
    required this.statusColor,
    this.subtitle,
    this.onTap,
  });

  final String title;
  final String status;
  final Color statusColor;
  final String? subtitle;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return AppCard(
      variant: CardVariant.elevated,
      onTap: onTap,
      isInteractive: onTap != null,
      child: Row(
        children: [
          Container(
            width: 4,
            height: 40,
            decoration: BoxDecoration(
              color: statusColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    subtitle!,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: statusColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              status,
              style: theme.textTheme.labelSmall?.copyWith(
                color: statusColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}