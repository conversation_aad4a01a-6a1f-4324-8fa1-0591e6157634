import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_typography.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/haptic_feedback_utils.dart';

/// Button variant types
enum ButtonVariant { primary, secondary, ghost, danger }

/// Button size types
enum ButtonSize { small, medium, large }

/// Custom button widget with loading states and consistent styling
class CustomButton extends StatefulWidget {
  const CustomButton({
    super.key,
    required this.onPressed,
    required this.child,
    this.variant = ButtonVariant.primary,
    this.size = ButtonSize.large,
    this.isLoading = false,
    this.isEnabled = true,
    this.width,
    this.height,
    this.icon,
    this.iconPosition = IconPosition.leading,
    this.loadingText,
    this.tooltip,
  });

  /// Callback when button is pressed
  final VoidCallback? onPressed;

  /// Button content (usually Text widget)
  final Widget child;

  /// Button visual variant
  final ButtonVariant variant;

  /// Button size
  final ButtonSize size;

  /// Whether button is in loading state
  final bool isLoading;

  /// Whether button is enabled
  final bool isEnabled;

  /// Custom width (overrides size-based width)
  final double? width;

  /// Custom height (overrides size-based height)
  final double? height;

  /// Optional icon
  final IconData? icon;

  /// Icon position relative to text
  final IconPosition iconPosition;

  /// Text to show when loading
  final String? loadingText;

  /// Tooltip text
  final String? tooltip;

  @override
  State<CustomButton> createState() => _CustomButtonState();
}

class _CustomButtonState extends State<CustomButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _scaleAnimation =
        Tween<double>(begin: 1.0, end: AppConstants.buttonScaleDown).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeInOut,
          ),
        );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    if (_canInteract) {
      _animationController.forward();
    }
  }

  void _onTapUp(TapUpDetails details) {
    if (_canInteract) {
      _animationController.reverse();
    }
  }

  void _onTapCancel() {
    if (_canInteract) {
      _animationController.reverse();
    }
  }

  bool get _canInteract => widget.isEnabled && !widget.isLoading;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final buttonConfig = _getButtonConfig();

    // Get button text for accessibility
    String? buttonText;
    if (widget.child is Text) {
      buttonText = (widget.child as Text).data;
    }

    // Create semantic label
    String? semanticLabel = buttonText;
    if (widget.isLoading && widget.loadingText != null) {
      semanticLabel = widget.loadingText;
    }

    // Create semantic hint
    String? semanticHint;
    if (!_canInteract && widget.isLoading) {
      semanticHint = 'Loading';
    } else if (!widget.isEnabled) {
      semanticHint = 'Disabled';
    }

    Widget button = AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            width: widget.width ?? buttonConfig.width,
            height: widget.height ?? buttonConfig.height,
            constraints: const BoxConstraints(
              minWidth: AppConstants.minTouchTarget,
              minHeight: AppConstants.minTouchTarget,
            ),
            decoration: BoxDecoration(
              color: buttonConfig.backgroundColor,
              borderRadius: BorderRadius.circular(
                AppConstants.defaultBorderRadius,
              ),
              border: buttonConfig.borderColor != null
                  ? Border.all(color: buttonConfig.borderColor!)
                  : null,
              boxShadow: buttonConfig.elevation > 0
                  ? [
                      BoxShadow(
                        color: AppColors.shadow.withValues(alpha: 0.2),
                        blurRadius: buttonConfig.elevation,
                        offset: Offset(0, buttonConfig.elevation / 2),
                      ),
                    ]
                  : null,
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: _canInteract
                    ? () {
                        // Add haptic feedback based on button variant
                        switch (widget.variant) {
                          case ButtonVariant.primary:
                            HapticFeedbackUtils.buttonPress();
                            break;
                          case ButtonVariant.secondary:
                            HapticFeedbackUtils.lightImpact();
                            break;
                          case ButtonVariant.ghost:
                            HapticFeedbackUtils.lightImpact();
                            break;
                          case ButtonVariant.danger:
                            HapticFeedbackUtils.warning();
                            break;
                        }
                        widget.onPressed?.call();
                      }
                    : null,
                onTapDown: _onTapDown,
                onTapUp: _onTapUp,
                onTapCancel: _onTapCancel,
                borderRadius: BorderRadius.circular(
                  AppConstants.defaultBorderRadius,
                ),
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: buttonConfig.horizontalPadding,
                    vertical: buttonConfig.verticalPadding,
                  ),
                  child: _buildButtonContent(theme, buttonConfig),
                ),
              ),
            ),
          ),
        );
      },
    );

    // Add accessibility semantics
    button = Semantics(
      label: semanticLabel,
      hint: semanticHint,
      button: true,
      enabled: _canInteract,
      child: button,
    );

    if (widget.tooltip != null) {
      button = Tooltip(message: widget.tooltip!, child: button);
    }

    return button;
  }

  Widget _buildButtonContent(ThemeData theme, _ButtonConfig config) {
    if (widget.isLoading) {
      return _buildLoadingContent(theme, config);
    }

    if (widget.icon != null) {
      return _buildIconContent(theme, config);
    }

    return _buildTextContent(theme, config);
  }

  Widget _buildLoadingContent(ThemeData theme, _ButtonConfig config) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: config.iconSize,
          height: config.iconSize,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(config.foregroundColor),
          ),
        ),
        if (widget.loadingText != null) ...[
          const SizedBox(width: 8),
          Text(
            widget.loadingText!,
            style: config.textStyle.copyWith(color: config.foregroundColor),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }

  Widget _buildIconContent(ThemeData theme, _ButtonConfig config) {
    final icon = Icon(
      widget.icon,
      size: config.iconSize,
      color: config.foregroundColor,
    );

    final text = DefaultTextStyle(
      style: config.textStyle.copyWith(color: config.foregroundColor),
      child: widget.child,
    );

    if (widget.iconPosition == IconPosition.leading) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [icon, const SizedBox(width: 8), text],
      );
    } else {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [text, const SizedBox(width: 8), icon],
      );
    }
  }

  Widget _buildTextContent(ThemeData theme, _ButtonConfig config) {
    return DefaultTextStyle(
      style: config.textStyle.copyWith(color: config.foregroundColor),
      textAlign: TextAlign.center,
      child: widget.child,
    );
  }

  _ButtonConfig _getButtonConfig() {
    final isEnabled = _canInteract;

    switch (widget.variant) {
      case ButtonVariant.primary:
        return _ButtonConfig(
          backgroundColor: isEnabled ? AppColors.primary : AppColors.mediumGray,
          foregroundColor: isEnabled
              ? AppColors.textOnPrimary
              : AppColors.textSecondary,
          textStyle: _getTextStyle(),
          elevation: isEnabled ? 2 : 0,
          width: _getWidth(),
          height: _getHeight(),
          horizontalPadding: _getHorizontalPadding(),
          verticalPadding: _getVerticalPadding(),
          iconSize: _getIconSize(),
        );

      case ButtonVariant.secondary:
        return _ButtonConfig(
          backgroundColor: isEnabled
              ? AppColors.backgroundSecondary
              : AppColors.lightGray,
          foregroundColor: isEnabled
              ? AppColors.textPrimary
              : AppColors.textSecondary,
          borderColor: isEnabled ? AppColors.border : AppColors.mediumGray,
          textStyle: _getTextStyle(),
          elevation: 0,
          width: _getWidth(),
          height: _getHeight(),
          horizontalPadding: _getHorizontalPadding(),
          verticalPadding: _getVerticalPadding(),
          iconSize: _getIconSize(),
        );

      case ButtonVariant.ghost:
        return _ButtonConfig(
          backgroundColor: Colors.transparent,
          foregroundColor: isEnabled
              ? AppColors.primary
              : AppColors.textSecondary,
          textStyle: _getTextStyle(),
          elevation: 0,
          width: _getWidth(),
          height: _getHeight(),
          horizontalPadding: _getHorizontalPadding(),
          verticalPadding: _getVerticalPadding(),
          iconSize: _getIconSize(),
        );

      case ButtonVariant.danger:
        return _ButtonConfig(
          backgroundColor: isEnabled ? AppColors.error : AppColors.mediumGray,
          foregroundColor: isEnabled
              ? AppColors.textOnPrimary
              : AppColors.textSecondary,
          textStyle: _getTextStyle(),
          elevation: isEnabled ? 2 : 0,
          width: _getWidth(),
          height: _getHeight(),
          horizontalPadding: _getHorizontalPadding(),
          verticalPadding: _getVerticalPadding(),
          iconSize: _getIconSize(),
        );
    }
  }

  TextStyle _getTextStyle() {
    switch (widget.size) {
      case ButtonSize.small:
        return AppTypography.buttonMedium;
      case ButtonSize.medium:
        return AppTypography.buttonLarge;
      case ButtonSize.large:
        return AppTypography.buttonLarge;
    }
  }

  double _getWidth() {
    switch (widget.size) {
      case ButtonSize.small:
        return double.infinity;
      case ButtonSize.medium:
        return double.infinity;
      case ButtonSize.large:
        return double.infinity;
    }
  }

  double _getHeight() {
    switch (widget.size) {
      case ButtonSize.small:
        return 36;
      case ButtonSize.medium:
        return 44;
      case ButtonSize.large:
        return 48;
    }
  }

  double _getHorizontalPadding() {
    switch (widget.size) {
      case ButtonSize.small:
        return 12;
      case ButtonSize.medium:
        return 16;
      case ButtonSize.large:
        return 20;
    }
  }

  double _getVerticalPadding() {
    switch (widget.size) {
      case ButtonSize.small:
        return 8;
      case ButtonSize.medium:
        return 10;
      case ButtonSize.large:
        return 12;
    }
  }

  double _getIconSize() {
    switch (widget.size) {
      case ButtonSize.small:
        return 16;
      case ButtonSize.medium:
        return 18;
      case ButtonSize.large:
        return 20;
    }
  }
}

/// Icon position enum
enum IconPosition { leading, trailing }

/// Button configuration class
class _ButtonConfig {
  const _ButtonConfig({
    required this.backgroundColor,
    required this.foregroundColor,
    required this.textStyle,
    required this.elevation,
    required this.width,
    required this.height,
    required this.horizontalPadding,
    required this.verticalPadding,
    required this.iconSize,
    this.borderColor,
  });

  final Color backgroundColor;
  final Color foregroundColor;
  final Color? borderColor;
  final TextStyle textStyle;
  final double elevation;
  final double width;
  final double height;
  final double horizontalPadding;
  final double verticalPadding;
  final double iconSize;
}

/// Convenience constructors for common button types
class PrimaryButton extends StatelessWidget {
  const PrimaryButton({
    super.key,
    required this.onPressed,
    required this.text,
    this.isLoading = false,
    this.isEnabled = true,
    this.icon,
    this.loadingText,
    this.size = ButtonSize.large,
  });

  final VoidCallback? onPressed;
  final String text;
  final bool isLoading;
  final bool isEnabled;
  final IconData? icon;
  final String? loadingText;
  final ButtonSize size;

  @override
  Widget build(BuildContext context) {
    return CustomButton(
      onPressed: onPressed,
      variant: ButtonVariant.primary,
      size: size,
      isLoading: isLoading,
      isEnabled: isEnabled,
      icon: icon,
      loadingText: loadingText,
      child: Text(text),
    );
  }
}

class SecondaryButton extends StatelessWidget {
  const SecondaryButton({
    super.key,
    required this.onPressed,
    required this.text,
    this.isLoading = false,
    this.isEnabled = true,
    this.icon,
    this.loadingText,
    this.size = ButtonSize.large,
  });

  final VoidCallback? onPressed;
  final String text;
  final bool isLoading;
  final bool isEnabled;
  final IconData? icon;
  final String? loadingText;
  final ButtonSize size;

  @override
  Widget build(BuildContext context) {
    return CustomButton(
      onPressed: onPressed,
      variant: ButtonVariant.secondary,
      size: size,
      isLoading: isLoading,
      isEnabled: isEnabled,
      icon: icon,
      loadingText: loadingText,
      child: Text(text),
    );
  }
}

class GhostButton extends StatelessWidget {
  const GhostButton({
    super.key,
    required this.onPressed,
    required this.text,
    this.isLoading = false,
    this.isEnabled = true,
    this.icon,
    this.loadingText,
    this.size = ButtonSize.large,
  });

  final VoidCallback? onPressed;
  final String text;
  final bool isLoading;
  final bool isEnabled;
  final IconData? icon;
  final String? loadingText;
  final ButtonSize size;

  @override
  Widget build(BuildContext context) {
    return CustomButton(
      onPressed: onPressed,
      variant: ButtonVariant.ghost,
      size: size,
      isLoading: isLoading,
      isEnabled: isEnabled,
      icon: icon,
      loadingText: loadingText,
      child: Text(text),
    );
  }
}

class DangerButton extends StatelessWidget {
  const DangerButton({
    super.key,
    required this.onPressed,
    required this.text,
    this.isLoading = false,
    this.isEnabled = true,
    this.icon,
    this.loadingText,
    this.size = ButtonSize.large,
  });

  final VoidCallback? onPressed;
  final String text;
  final bool isLoading;
  final bool isEnabled;
  final IconData? icon;
  final String? loadingText;
  final ButtonSize size;

  @override
  Widget build(BuildContext context) {
    return CustomButton(
      onPressed: onPressed,
      variant: ButtonVariant.danger,
      size: size,
      isLoading: isLoading,
      isEnabled: isEnabled,
      icon: icon,
      loadingText: loadingText,
      child: Text(text),
    );
  }
}
