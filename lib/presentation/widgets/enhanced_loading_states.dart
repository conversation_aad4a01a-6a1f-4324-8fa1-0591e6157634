import 'package:flutter/material.dart';
import 'dart:ui';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_spacing.dart';
import '../../core/theme/app_typography.dart';

/// Enhanced loading states with smooth animations and better UX
class EnhancedLoadingStates {
  EnhancedLoadingStates._();
}

/// Ride booking loading state with animated progress
class RideBookingLoader extends StatefulWidget {
  const RideBookingLoader({
    super.key,
    this.message = 'Finding your ride...',
    this.showProgress = true,
  });

  final String message;
  final bool showProgress;

  @override
  State<RideBookingLoader> createState() => _RideBookingLoaderState();
}

class _RideBookingLoaderState extends State<RideBookingLoader>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _rotationController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _rotationAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _rotationController, curve: Curves.linear),
    );

    _pulseController.repeat(reverse: true);
    _rotationController.repeat();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.xl),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Animated car icon
          AnimatedBuilder(
            animation: Listenable.merge([_pulseAnimation, _rotationAnimation]),
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation.value,
                child: Transform.rotate(
                  angle: _rotationAnimation.value * 0.1, // Subtle rotation
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.directions_car,
                      size: 40,
                      color: AppColors.primary,
                    ),
                  ),
                ),
              );
            },
          ),

          const SizedBox(height: AppSpacing.lg),

          // Loading message with fade animation
          AnimatedOpacity(
            opacity: 1.0,
            duration: const Duration(milliseconds: 800),
            child: Text(
              widget.message,
              style: AppTypography.bodyLarge.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          const SizedBox(height: AppSpacing.md),

          // Animated progress indicator
          if (widget.showProgress)
            AnimatedContainer(
              duration: const Duration(milliseconds: 400),
              width: 200,
              child: LinearProgressIndicator(
                backgroundColor: AppColors.lightGray,
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
            ),

          const SizedBox(height: AppSpacing.sm),

          // Helpful tip
          AnimatedOpacity(
            opacity: 1.0,
            duration: const Duration(milliseconds: 600),
            child: Text(
              'This usually takes 30-60 seconds',
              style: AppTypography.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}

/// Driver tracking loading state
class DriverTrackingLoader extends StatefulWidget {
  const DriverTrackingLoader({super.key, this.driverName, this.eta});

  final String? driverName;
  final String? eta;

  @override
  State<DriverTrackingLoader> createState() => _DriverTrackingLoaderState();
}

class _DriverTrackingLoaderState extends State<DriverTrackingLoader>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Animated location ping
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return Stack(
                alignment: Alignment.center,
                children: [
                  // Outer ring
                  Container(
                    width: 100 * (1 + _animation.value * 0.5),
                    height: 100 * (1 + _animation.value * 0.5),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: AppColors.primary.withValues(
                          alpha: 1 - _animation.value,
                        ),
                        width: 2,
                      ),
                    ),
                  ),
                  // Inner circle
                  Container(
                    width: 60,
                    height: 60,
                    decoration: const BoxDecoration(
                      color: AppColors.primary,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.navigation,
                      color: Colors.white,
                      size: 30,
                    ),
                  ),
                ],
              );
            },
          ),

          const SizedBox(height: AppSpacing.lg),

          Text(
            widget.driverName != null
                ? '${widget.driverName} is on the way'
                : 'Your driver is on the way',
            style: AppTypography.h4.copyWith(fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),

          if (widget.eta != null) ...[
            const SizedBox(height: AppSpacing.sm),
            Text(
              'ETA: ${widget.eta}',
              style: AppTypography.bodyLarge.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],

          const SizedBox(height: AppSpacing.md),

          Text(
            'Track your driver in real-time',
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// Skeleton loader for ride history items
class RideHistorySkeletonLoader extends StatelessWidget {
  const RideHistorySkeletonLoader({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: 5,
      padding: const EdgeInsets.all(AppSpacing.md),
      itemBuilder: (context, index) {
        return Card(
          margin: const EdgeInsets.only(bottom: AppSpacing.md),
          child: Padding(
            padding: const EdgeInsets.all(AppSpacing.md),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Date and status row
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildShimmerBox(width: 100, height: 16),
                    _buildShimmerBox(width: 80, height: 20),
                  ],
                ),
                const SizedBox(height: AppSpacing.sm),

                // Pickup location
                Row(
                  children: [
                    _buildShimmerBox(width: 16, height: 16),
                    const SizedBox(width: AppSpacing.sm),
                    Expanded(child: _buildShimmerBox(height: 16)),
                  ],
                ),
                const SizedBox(height: AppSpacing.xs),

                // Dropoff location
                Row(
                  children: [
                    _buildShimmerBox(width: 16, height: 16),
                    const SizedBox(width: AppSpacing.sm),
                    Expanded(child: _buildShimmerBox(height: 16)),
                  ],
                ),
                const SizedBox(height: AppSpacing.sm),

                // Fare and duration
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildShimmerBox(width: 60, height: 20),
                    _buildShimmerBox(width: 80, height: 16),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildShimmerBox({double? width, required double height}) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: AppColors.lightGray,
        borderRadius: BorderRadius.circular(4),
      ),
    );
  }
}

/// Enhanced pull-to-refresh indicator
class EnhancedRefreshIndicator extends StatelessWidget {
  const EnhancedRefreshIndicator({
    super.key,
    required this.child,
    required this.onRefresh,
    this.displacement = 40.0,
  });

  final Widget child;
  final RefreshCallback onRefresh;
  final double displacement;

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: onRefresh,
      displacement: displacement,
      color: AppColors.primary,
      backgroundColor: AppColors.surface,
      strokeWidth: 3.0,
      child: child,
    );
  }
}

/// Loading overlay with blur effect
class BlurredLoadingOverlay extends StatelessWidget {
  const BlurredLoadingOverlay({
    super.key,
    required this.isLoading,
    required this.child,
    this.message,
    this.showProgress = false,
  });

  final bool isLoading;
  final Widget child;
  final String? message;
  final bool showProgress;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isLoading)
          Container(
            color: Colors.black.withValues(alpha: 0.3),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 2, sigmaY: 2),
              child: Center(
                child: Container(
                  padding: const EdgeInsets.all(AppSpacing.xl),
                  margin: const EdgeInsets.symmetric(horizontal: AppSpacing.xl),
                  decoration: BoxDecoration(
                    color: AppColors.surface,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.2),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(
                          AppColors.primary,
                        ),
                      ),
                      if (message != null) ...[
                        const SizedBox(height: AppSpacing.md),
                        Text(
                          message!,
                          style: AppTypography.bodyMedium,
                          textAlign: TextAlign.center,
                        ),
                      ],
                      if (showProgress) ...[
                        const SizedBox(height: AppSpacing.md),
                        const LinearProgressIndicator(
                          backgroundColor: AppColors.lightGray,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            AppColors.primary,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }
}

/// Smooth page transition loading
class PageTransitionLoader extends StatefulWidget {
  const PageTransitionLoader({
    super.key,
    required this.isLoading,
    required this.child,
    this.loadingChild,
  });

  final bool isLoading;
  final Widget child;
  final Widget? loadingChild;

  @override
  State<PageTransitionLoader> createState() => _PageTransitionLoaderState();
}

class _PageTransitionLoaderState extends State<PageTransitionLoader>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));

    if (!widget.isLoading) {
      _controller.forward();
    }
  }

  @override
  void didUpdateWidget(PageTransitionLoader oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.isLoading != widget.isLoading) {
      if (widget.isLoading) {
        _controller.reverse();
      } else {
        _controller.forward();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return Stack(
          children: [
            if (!widget.isLoading)
              Opacity(opacity: _fadeAnimation.value, child: widget.child),
            if (widget.isLoading)
              widget.loadingChild ??
                  const Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppColors.primary,
                      ),
                    ),
                  ),
          ],
        );
      },
    );
  }
}
