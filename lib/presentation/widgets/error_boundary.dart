import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../core/errors/app_error.dart';
import '../../core/errors/error_extensions.dart';
import '../../core/errors/global_error_handler.dart';

import '../../core/theme/app_spacing.dart';
import '../../core/theme/app_typography.dart';
import 'error_display_widget.dart';

/// Error boundary widget that catches and handles errors in its child widget tree
class ErrorBoundary extends ConsumerStatefulWidget {
  final Widget child;
  final String? context;
  final Widget Function(AppError error, VoidCallback retry)? errorBuilder;
  final void Function(AppError error)? onError;
  final bool showErrorDetails;

  const ErrorBoundary({
    super.key,
    required this.child,
    this.context,
    this.errorBuilder,
    this.onError,
    this.showErrorDetails = false,
  });

  @override
  ConsumerState<ErrorBoundary> createState() => _ErrorBoundaryState();
}

class _ErrorBoundaryState extends ConsumerState<ErrorBoundary> {
  AppError? _error;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();

    // Register error callback with global error handler
    final errorHandler = ref.read(globalErrorHandlerProvider);
    errorHandler.registerErrorCallback(_handleGlobalError);
  }

  @override
  void dispose() {
    // Unregister error callback
    final errorHandler = ref.read(globalErrorHandlerProvider);
    errorHandler.unregisterErrorCallback(_handleGlobalError);
    super.dispose();
  }

  void _handleGlobalError(AppError error) {
    if (mounted) {
      setState(() {
        _error = error;
        _hasError = true;
      });

      // Call custom error handler if provided
      widget.onError?.call(error);
    }
  }

  void _handleError(Object error, StackTrace stackTrace) {
    final appError = AppError.unknown(
      message: 'An unexpected error occurred',
      exception: error,
    );

    // Report to global error handler
    final errorHandler = ref.read(globalErrorHandlerProvider);
    errorHandler.handleError(
      appError,
      context: widget.context,
      showToUser: false, // We'll handle UI display here
      logError: true,
    );

    if (mounted) {
      setState(() {
        _error = appError;
        _hasError = true;
      });

      // Call custom error handler if provided
      widget.onError?.call(appError);
    }
  }

  void _retry() {
    if (mounted) {
      setState(() {
        _error = null;
        _hasError = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_hasError && _error != null) {
      // Use custom error builder if provided
      if (widget.errorBuilder != null) {
        return widget.errorBuilder!(_error!, _retry);
      }

      // Default error UI
      return _buildDefaultErrorUI(context, _error!);
    }

    // Wrap child with error handling
    return ErrorCatcher(onError: _handleError, child: widget.child);
  }

  Widget _buildDefaultErrorUI(BuildContext context, AppError error) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Something went wrong'),
        backgroundColor: Theme.of(context).colorScheme.errorContainer,
        foregroundColor: Theme.of(context).colorScheme.onErrorContainer,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.lg),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Theme.of(context).colorScheme.error,
              ),
              const SizedBox(height: AppSpacing.lg),
              Text(
                'Oops! Something went wrong',
                style: AppTypography.headlineSmall.copyWith(
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppSpacing.md),
              EnhancedErrorDisplayWidget(
                error: error,
                context: widget.context,
                onRetry: _retry,
                showDismissButton: false,
                padding: EdgeInsets.zero,
              ),
              const SizedBox(height: AppSpacing.lg),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: _retry,
                      icon: const Icon(Icons.refresh),
                      label: const Text('Try Again'),
                    ),
                  ),
                  const SizedBox(width: AppSpacing.md),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        // Navigate back or to home
                        Navigator.of(
                          context,
                        ).popUntil((route) => route.isFirst);
                      },
                      icon: const Icon(Icons.home),
                      label: const Text('Go Home'),
                    ),
                  ),
                ],
              ),
              if (widget.showErrorDetails) ...[
                const SizedBox(height: AppSpacing.lg),
                ExpansionTile(
                  title: const Text('Error Details'),
                  children: [
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(AppSpacing.md),
                      decoration: BoxDecoration(
                        color: Theme.of(
                          context,
                        ).colorScheme.surfaceContainerHighest,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        error.toString(),
                        style: AppTypography.bodySmall.copyWith(
                          fontFamily: 'monospace',
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

/// Widget that catches errors in its child widget tree
class ErrorCatcher extends StatefulWidget {
  final Widget child;
  final void Function(Object error, StackTrace stackTrace) onError;

  const ErrorCatcher({super.key, required this.child, required this.onError});

  @override
  State<ErrorCatcher> createState() => _ErrorCatcherState();
}

class _ErrorCatcherState extends State<ErrorCatcher> {
  @override
  Widget build(BuildContext context) {
    return widget.child;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Set up error handling for this widget tree
    FlutterError.onError = (FlutterErrorDetails details) {
      widget.onError(details.exception, details.stack ?? StackTrace.current);
    };
  }
}

/// Mixin for widgets that need comprehensive error handling
mixin ErrorHandlingMixin<T extends ConsumerStatefulWidget> on ConsumerState<T> {
  /// Handle an error with optional retry and recovery options
  Future<R?> handleError<R>(
    Future<R> Function() operation, {
    String? operationName,
    bool showError = true,
    bool showRecoveryOptions = true,
    List<ErrorRecoveryAction>? customRecoveryActions,
  }) async {
    try {
      return await operation();
    } catch (e) {
      final error = e is AppError
          ? e
          : AppError.unknown(
              message: 'Operation failed: ${operationName ?? 'Unknown'}',
              exception: e,
            );

      if (showError) {
        await _showErrorDialog(
          error,
          operationName: operationName,
          showRecoveryOptions: showRecoveryOptions,
          customRecoveryActions: customRecoveryActions,
        );
      }

      // Report to global error handler
      final errorHandler = ref.read(globalErrorHandlerProvider);
      await errorHandler.handleError(
        error,
        context: operationName ?? T.toString(),
        showToUser: false, // We handled UI above
      );

      return null;
    }
  }

  /// Show error dialog with recovery options
  Future<void> _showErrorDialog(
    AppError error, {
    String? operationName,
    bool showRecoveryOptions = true,
    List<ErrorRecoveryAction>? customRecoveryActions,
  }) async {
    if (!mounted) return;

    await showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(error.icon, color: error.getColor(context)),
            const SizedBox(width: AppSpacing.sm),
            Expanded(child: Text(error.title)),
          ],
        ),
        content: SingleChildScrollView(
          child: EnhancedErrorDisplayWidget(
            error: error,
            context: operationName,
            showDismissButton: false,
            showRecoveryOptions: showRecoveryOptions,
            customRecoveryActions: customRecoveryActions,
            padding: EdgeInsets.zero,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  /// Show error snackbar for less critical errors
  void showErrorSnackbar(
    AppError error, {
    Duration duration = const Duration(seconds: 4),
    SnackBarAction? action,
  }) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(error.icon, color: Colors.white, size: 20),
            const SizedBox(width: AppSpacing.sm),
            Expanded(
              child: Text(
                error.message,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: error.getColor(context),
        duration: duration,
        action: action,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// Handle network errors with offline fallback
  Future<R?> handleNetworkOperation<R>(
    Future<R> Function() operation, {
    String? operationName,
    R? Function()? offlineFallback,
  }) async {
    try {
      return await operation();
    } catch (e) {
      final error = e is AppError
          ? e
          : AppError.unknown(message: 'Network operation failed', exception: e);

      if (error.isNetworkError && offlineFallback != null) {
        // Try offline fallback
        try {
          final result = offlineFallback();
          if (result != null) {
            showErrorSnackbar(
              AppError.network(
                message: 'Using offline data',
                details: 'Some information may be outdated',
              ),
              duration: const Duration(seconds: 2),
            );
            return result;
          }
        } catch (fallbackError) {
          // Fallback also failed, continue with original error handling
        }
      }

      return await handleError(
        () async => throw error,
        operationName: operationName,
        showRecoveryOptions: error.isNetworkError,
      );
    }
  }
}

/// Provider for error boundary state
final errorBoundaryProvider = StateProvider<AppError?>((ref) => null);

/// Extension for easy error handling in async operations
extension AsyncErrorHandling<T> on Future<T> {
  /// Catch and handle errors with the global error handler
  Future<T?> catchAndHandle(
    WidgetRef ref, {
    String? context,
    bool showToUser = true,
  }) async {
    try {
      return await this;
    } catch (e) {
      final error = e is AppError
          ? e
          : AppError.unknown(message: 'Async operation failed', exception: e);

      final errorHandler = ref.read(globalErrorHandlerProvider);
      await errorHandler.handleError(
        error,
        context: context,
        showToUser: showToUser,
      );

      return null;
    }
  }
}
