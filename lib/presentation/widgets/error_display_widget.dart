import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../core/errors/app_error.dart';
import '../../core/errors/error_extensions.dart';

import '../../core/theme/app_spacing.dart';
import '../../core/theme/app_typography.dart';
import '../../services/location/location_fallback_service.dart';

/// Enhanced widget for displaying errors with comprehensive recovery options
class EnhancedErrorDisplayWidget extends ConsumerWidget {
  final AppError error;
  final VoidCallback? onRetry;
  final VoidCallback? onDismiss;
  final bool showRetryButton;
  final bool showDismissButton;
  final bool showRecoveryOptions;
  final EdgeInsetsGeometry? padding;
  final bool compact;
  final String? context;
  final List<ErrorRecoveryAction>? customRecoveryActions;

  const EnhancedErrorDisplayWidget({
    super.key,
    required this.error,
    this.onRetry,
    this.onDismiss,
    this.showRetryButton = true,
    this.showDismissButton = true,
    this.showRecoveryOptions = true,
    this.padding,
    this.compact = false,
    this.context,
    this.customRecoveryActions,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final errorColor = error.getColor(context);

    if (compact) {
      return _buildCompactError(context, theme, errorColor);
    }

    return Container(
      width: double.infinity,
      padding: padding ?? const EdgeInsets.all(AppSpacing.md),
      margin: const EdgeInsets.all(AppSpacing.sm),
      decoration: BoxDecoration(
        color: errorColor.withValues(alpha: 0.1),
        border: Border.all(color: errorColor.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildErrorHeader(context, theme, errorColor),
          const SizedBox(height: AppSpacing.sm),
          _buildErrorMessage(context, theme),
          if (error.details != null) ...[
            const SizedBox(height: AppSpacing.xs),
            _buildErrorDetails(context, theme),
          ],
          if (error.fieldErrors.isNotEmpty) ...[
            const SizedBox(height: AppSpacing.sm),
            _buildFieldErrors(context, theme, errorColor),
          ],
          if (showRecoveryOptions) ...[
            const SizedBox(height: AppSpacing.md),
            _buildRecoveryOptions(context, theme, errorColor),
          ],
          if (showRetryButton && error.canRetry && onRetry != null) ...[
            const SizedBox(height: AppSpacing.md),
            _buildRetryButton(context, errorColor),
          ],
        ],
      ),
    );
  }

  Widget _buildErrorHeader(
    BuildContext context,
    ThemeData theme,
    Color errorColor,
  ) {
    return Row(
      children: [
        Icon(error.icon, color: errorColor, size: 24),
        const SizedBox(width: AppSpacing.sm),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                error.title,
                style: AppTypography.bodyLarge.copyWith(
                  color: errorColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
              if (this.context?.isNotEmpty == true) ...[
                const SizedBox(height: 2),
                Text(
                  'Context: ${this.context}',
                  style: AppTypography.bodySmall.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ],
          ),
        ),
        if (showDismissButton && onDismiss != null)
          IconButton(
            onPressed: onDismiss,
            icon: Icon(Icons.close, color: errorColor, size: 20),
            constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
          ),
      ],
    );
  }

  Widget _buildErrorMessage(BuildContext context, ThemeData theme) {
    return Text(
      error.message,
      style: AppTypography.bodyMedium.copyWith(
        color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
      ),
    );
  }

  Widget _buildErrorDetails(BuildContext context, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.sm),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        error.details!,
        style: AppTypography.bodySmall.copyWith(
          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
          fontFamily: 'monospace',
        ),
      ),
    );
  }

  Widget _buildFieldErrors(
    BuildContext context,
    ThemeData theme,
    Color errorColor,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: error.fieldErrors.entries
          .map(
            (entry) => Padding(
              padding: const EdgeInsets.only(bottom: AppSpacing.xs),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(Icons.error_outline, color: errorColor, size: 16),
                  const SizedBox(width: AppSpacing.xs),
                  Expanded(
                    child: RichText(
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text: '${entry.key}: ',
                            style: AppTypography.bodySmall.copyWith(
                              color: errorColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          TextSpan(
                            text: entry.value,
                            style: AppTypography.bodySmall.copyWith(
                              color: errorColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          )
          .toList(),
    );
  }

  Widget _buildRecoveryOptions(
    BuildContext context,
    ThemeData theme,
    Color errorColor,
  ) {
    final recoveryActions =
        customRecoveryActions ?? _getDefaultRecoveryActions();

    if (recoveryActions.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'What would you like to do?',
          style: AppTypography.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: AppSpacing.sm),
        ...recoveryActions.map(
          (action) => Padding(
            padding: const EdgeInsets.only(bottom: AppSpacing.xs),
            child: SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: action.onPressed,
                icon: Icon(action.icon, size: 18),
                label: Text(action.title),
                style: OutlinedButton.styleFrom(
                  foregroundColor: theme.colorScheme.primary,
                  side: BorderSide(color: theme.colorScheme.outline),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRetryButton(BuildContext context, Color errorColor) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: onRetry,
        icon: const Icon(Icons.refresh),
        label: const Text('Try Again'),
        style: ElevatedButton.styleFrom(
          backgroundColor: errorColor,
          foregroundColor: Colors.white,
        ),
      ),
    );
  }

  Widget _buildCompactError(
    BuildContext context,
    ThemeData theme,
    Color errorColor,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
        vertical: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: errorColor.withValues(alpha: 0.1),
        border: Border.all(color: errorColor.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        children: [
          Icon(error.icon, color: errorColor, size: 16),
          const SizedBox(width: AppSpacing.xs),
          Expanded(
            child: Text(
              error.message,
              style: AppTypography.bodySmall.copyWith(color: errorColor),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (showRetryButton && error.canRetry && onRetry != null)
            IconButton(
              onPressed: onRetry,
              icon: Icon(Icons.refresh, color: errorColor, size: 16),
              constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
            ),
        ],
      ),
    );
  }

  List<ErrorRecoveryAction> _getDefaultRecoveryActions() {
    // Get context-specific recovery actions based on error type
    if (error.isNetworkError) {
      return [
        ErrorRecoveryAction(
          title: 'Check Connection',
          icon: Icons.wifi,
          onPressed: () {
            // Open network settings or show network status
          },
        ),
        ErrorRecoveryAction(
          title: 'Use Offline Mode',
          icon: Icons.cloud_off,
          onPressed: () {
            // Switch to offline mode
          },
        ),
      ];
    }

    if (error.isValidationError && error.fieldErrors.containsKey('location')) {
      return LocationErrorRecovery.getRecoveryOptions(error)
          .map(
            (option) => ErrorRecoveryAction(
              title: option.title,
              icon: _getIconForRecoveryType(option.type),
              onPressed: () {
                // Handle recovery action
              },
            ),
          )
          .toList();
    }

    if (error.isAuthError) {
      return [
        ErrorRecoveryAction(
          title: 'Login Again',
          icon: Icons.login,
          onPressed: () {
            // Navigate to login screen
          },
        ),
      ];
    }

    return [];
  }

  IconData _getIconForRecoveryType(LocationRecoveryType type) {
    switch (type) {
      case LocationRecoveryType.retry:
        return Icons.refresh;
      case LocationRecoveryType.useCache:
        return Icons.history;
      case LocationRecoveryType.useDefault:
        return Icons.location_city;
      case LocationRecoveryType.manualEntry:
        return Icons.edit_location;
      case LocationRecoveryType.requestPermission:
        return Icons.location_on;
    }
  }
}

/// Action that can be taken to recover from an error
class ErrorRecoveryAction {
  final String title;
  final IconData icon;
  final VoidCallback onPressed;

  const ErrorRecoveryAction({
    required this.title,
    required this.icon,
    required this.onPressed,
  });
}

/// Legacy widget for displaying error messages with retry functionality
/// Kept for backward compatibility
class ErrorDisplayWidget extends StatelessWidget {
  final String message;
  final VoidCallback? onRetry;
  final IconData? icon;
  final bool showRetryButton;

  const ErrorDisplayWidget({
    super.key,
    required this.message,
    this.onRetry,
    this.icon,
    this.showRetryButton = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.errorContainer,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.error.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                icon ?? Icons.error_outline,
                color: Theme.of(context).colorScheme.onErrorContainer,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Error',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        color: Theme.of(context).colorScheme.onErrorContainer,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      message,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onErrorContainer,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (showRetryButton && onRetry != null) ...[
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: const Text('Retry'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Theme.of(
                    context,
                  ).colorScheme.onErrorContainer,
                  side: BorderSide(
                    color: Theme.of(context).colorScheme.onErrorContainer,
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Compact version of error display for inline use
class CompactErrorWidget extends StatelessWidget {
  final String message;
  final VoidCallback? onDismiss;

  const CompactErrorWidget({super.key, required this.message, this.onDismiss});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.errorContainer,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.warning_amber_rounded,
            size: 16,
            color: Theme.of(context).colorScheme.onErrorContainer,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              message,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onErrorContainer,
              ),
            ),
          ),
          GestureDetector(
            onTap: onDismiss,
            child: Icon(
              Icons.close,
              size: 16,
              color: Theme.of(context).colorScheme.onErrorContainer,
            ),
          ),
        ],
      ),
    );
  }
}
