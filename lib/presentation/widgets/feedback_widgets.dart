import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_typography.dart';
import '../../core/constants/app_constants.dart';

/// A widget that displays loading state with optional message
class LoadingWidget extends StatelessWidget {
  const LoadingWidget({
    super.key,
    this.message,
    this.size = 24.0,
    this.padding = const EdgeInsets.all(16.0),
    this.color,
  });

  final String? message;
  final double size;
  final EdgeInsets padding;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: size,
            height: size,
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(
                color ?? AppColors.primary,
              ),
              strokeWidth: size > 20 ? 3.0 : 2.0,
            ),
          ),
          if (message != null) ...[
            const SizedBox(height: 16),
            Text(
              message!,
              style: AppTypography.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}

/// A compact loading widget for inline display
class InlineLoadingWidget extends StatelessWidget {
  const InlineLoadingWidget({
    super.key,
    this.message,
    this.size = 16.0,
    this.color,
  });

  final String? message;
  final double size;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: size,
          height: size,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              color ?? AppColors.primary,
            ),
          ),
        ),
        if (message != null) ...[
          const SizedBox(width: 8),
          Text(
            message!,
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ],
    );
  }
}

/// A widget that displays success state with optional message
class SuccessWidget extends StatefulWidget {
  const SuccessWidget({
    super.key,
    this.message,
    this.showIcon = true,
    this.padding = const EdgeInsets.all(16.0),
    this.animated = true,
  });

  final String? message;
  final bool showIcon;
  final EdgeInsets padding;
  final bool animated;

  @override
  State<SuccessWidget> createState() => _SuccessWidgetState();
}

class _SuccessWidgetState extends State<SuccessWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    if (widget.animated) {
      _animationController = AnimationController(
        duration: AppConstants.animationDuration,
        vsync: this,
      );
      _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: _animationController, curve: Curves.elasticOut),
      );
      _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: _animationController, curve: Curves.easeIn),
      );
      _animationController.forward();
    }
  }

  @override
  void dispose() {
    if (widget.animated) {
      _animationController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Widget content = Container(
      padding: widget.padding,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (widget.showIcon) ...[
            Icon(Icons.check_circle, size: 48, color: AppColors.success),
            const SizedBox(height: 16),
          ],
          if (widget.message != null)
            Text(
              widget.message!,
              style: AppTypography.bodyMedium.copyWith(
                color: AppColors.success,
              ),
              textAlign: TextAlign.center,
            ),
        ],
      ),
    );

    if (!widget.animated) return content;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(opacity: _fadeAnimation.value, child: content),
        );
      },
    );
  }
}

/// A snackbar for showing success messages
class SuccessSnackBar extends SnackBar {
  SuccessSnackBar({
    super.key,
    required String message,
    super.duration = const Duration(seconds: 3),
  }) : super(
         content: Row(
           children: [
             const Icon(Icons.check_circle, color: Colors.white, size: 20),
             const SizedBox(width: 12),
             Expanded(
               child: Text(
                 message,
                 style: AppTypography.bodyMedium.copyWith(color: Colors.white),
               ),
             ),
           ],
         ),
         backgroundColor: AppColors.success,
         behavior: SnackBarBehavior.floating,
         shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
       );
}

/// A snackbar for showing error messages
class ErrorSnackBar extends SnackBar {
  ErrorSnackBar({
    super.key,
    required String message,
    super.duration = const Duration(seconds: 4),
    VoidCallback? onRetry,
  }) : super(
         content: Row(
           children: [
             const Icon(Icons.error_outline, color: Colors.white, size: 20),
             const SizedBox(width: 12),
             Expanded(
               child: Text(
                 message,
                 style: AppTypography.bodyMedium.copyWith(color: Colors.white),
               ),
             ),
           ],
         ),
         backgroundColor: AppColors.error,
         behavior: SnackBarBehavior.floating,
         shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
         action: onRetry != null
             ? SnackBarAction(
                 label: 'Retry',
                 textColor: Colors.white,
                 onPressed: onRetry,
               )
             : null,
       );
}

/// A snackbar for showing warning messages
class WarningSnackBar extends SnackBar {
  WarningSnackBar({
    super.key,
    required String message,
    super.duration = const Duration(seconds: 3),
  }) : super(
         content: Row(
           children: [
             const Icon(Icons.warning_amber, color: Colors.white, size: 20),
             const SizedBox(width: 12),
             Expanded(
               child: Text(
                 message,
                 style: AppTypography.bodyMedium.copyWith(color: Colors.white),
               ),
             ),
           ],
         ),
         backgroundColor: AppColors.warning,
         behavior: SnackBarBehavior.floating,
         shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
       );
}

/// A snackbar for showing info messages
class InfoSnackBar extends SnackBar {
  InfoSnackBar({
    super.key,
    required String message,
    super.duration = const Duration(seconds: 3),
  }) : super(
         content: Row(
           children: [
             const Icon(Icons.info_outline, color: Colors.white, size: 20),
             const SizedBox(width: 12),
             Expanded(
               child: Text(
                 message,
                 style: AppTypography.bodyMedium.copyWith(color: Colors.white),
               ),
             ),
           ],
         ),
         backgroundColor: AppColors.info,
         behavior: SnackBarBehavior.floating,
         shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
       );
}

/// A widget that handles async state display (loading, error, success, data)
class AsyncStateWidget<T> extends StatelessWidget {
  const AsyncStateWidget({
    super.key,
    required this.state,
    required this.dataBuilder,
    this.loadingBuilder,
    this.errorBuilder,
    this.emptyBuilder,
    this.loadingMessage,
    this.onRetry,
  });

  final AsyncValue<T> state;
  final Widget Function(T data) dataBuilder;
  final Widget Function()? loadingBuilder;
  final Widget Function(Object error, VoidCallback? onRetry)? errorBuilder;
  final Widget Function()? emptyBuilder;
  final String? loadingMessage;
  final VoidCallback? onRetry;

  @override
  Widget build(BuildContext context) {
    return state.when(
      data: (data) {
        // Handle empty data case
        if (data == null ||
            (data is List && (data as List).isEmpty) ||
            (data is Map && (data as Map).isEmpty)) {
          return emptyBuilder != null
              ? emptyBuilder!()
              : const Center(child: Text('No data available'));
        }
        return dataBuilder(data);
      },
      loading: () =>
          loadingBuilder?.call() ?? LoadingWidget(message: loadingMessage),
      error: (error, stackTrace) =>
          errorBuilder?.call(error, onRetry) ??
          Center(child: Text('Error: $error')),
    );
  }
}

/// Represents the state of an async operation
sealed class AsyncValue<T> {
  const AsyncValue();

  /// Creates a loading state
  const factory AsyncValue.loading() = AsyncLoading<T>;

  /// Creates a data state
  const factory AsyncValue.data(T data) = AsyncData<T>;

  /// Creates an error state
  const factory AsyncValue.error(Object error, [StackTrace? stackTrace]) =
      AsyncError<T>;

  /// Returns true if this is a loading state
  bool get isLoading => this is AsyncLoading<T>;

  /// Returns true if this is a data state
  bool get hasData => this is AsyncData<T>;

  /// Returns true if this is an error state
  bool get hasError => this is AsyncError<T>;

  /// Returns the data if available, null otherwise
  T? get data => switch (this) {
    AsyncData<T>(data: final data) => data,
    _ => null,
  };

  /// Returns the error if available, null otherwise
  Object? get error => switch (this) {
    AsyncError<T>(error: final error) => error,
    _ => null,
  };

  /// Handles all possible states
  R when<R>({
    required R Function() loading,
    required R Function(T data) data,
    required R Function(Object error, StackTrace? stackTrace) error,
  }) => switch (this) {
    AsyncLoading<T>() => loading(),
    AsyncData<T>(data: final dataValue) => data(dataValue),
    AsyncError<T>(error: final errorValue, stackTrace: final stackTrace) =>
      error(errorValue, stackTrace),
  };

  /// Maps the data if available
  AsyncValue<R> map<R>(R Function(T data) transform) => switch (this) {
    AsyncLoading<T>() => const AsyncValue.loading(),
    AsyncData<T>(data: final data) => AsyncValue.data(transform(data)),
    AsyncError<T>(error: final error, stackTrace: final stackTrace) =>
      AsyncValue.error(error, stackTrace),
  };
}

/// Loading state implementation
final class AsyncLoading<T> extends AsyncValue<T> {
  const AsyncLoading();

  @override
  bool operator ==(Object other) =>
      identical(this, other) || other is AsyncLoading<T>;

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() => 'AsyncLoading<$T>()';
}

/// Data state implementation
final class AsyncData<T> extends AsyncValue<T> {
  const AsyncData(this.data);

  @override
  final T data;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AsyncData<T> &&
          runtimeType == other.runtimeType &&
          data == other.data;

  @override
  int get hashCode => data.hashCode;

  @override
  String toString() => 'AsyncData<$T>($data)';
}

/// Error state implementation
final class AsyncError<T> extends AsyncValue<T> {
  const AsyncError(this.error, [this.stackTrace]);

  @override
  final Object error;
  final StackTrace? stackTrace;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AsyncError<T> &&
          runtimeType == other.runtimeType &&
          error == other.error &&
          stackTrace == other.stackTrace;

  @override
  int get hashCode => Object.hash(error, stackTrace);

  @override
  String toString() => 'AsyncError<$T>($error, $stackTrace)';
}
