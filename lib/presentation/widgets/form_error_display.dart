import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';
import '../../core/constants/app_constants.dart';

/// Widget to display form errors in a consistent manner
class FormErrorDisplay extends StatelessWidget {
  const FormErrorDisplay({
    super.key,
    this.errorMessage,
    this.fieldErrors = const {},
    this.showFieldErrors = true,
    this.padding,
  });

  /// General error message
  final String? errorMessage;

  /// Field-specific error messages
  final Map<String, String> fieldErrors;

  /// Whether to show field-specific errors
  final bool showFieldErrors;

  /// Custom padding
  final EdgeInsets? padding;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final hasErrors = errorMessage != null || fieldErrors.isNotEmpty;

    if (!hasErrors) return const SizedBox.shrink();

    return Container(
      width: double.infinity,
      padding: padding ?? const EdgeInsets.all(AppConstants.defaultPadding),
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.errorLight.withValues(alpha: 0.1),
        border: Border.all(color: AppColors.error.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // General error message
          if (errorMessage != null) ...[
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(Icons.error_outline, color: AppColors.error, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    errorMessage!,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: AppColors.error,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            if (showFieldErrors && fieldErrors.isNotEmpty) ...[
              const SizedBox(height: 12),
              const Divider(color: AppColors.error, height: 1),
              const SizedBox(height: 12),
            ],
          ],

          // Field-specific errors
          if (showFieldErrors && fieldErrors.isNotEmpty) ...[
            ...fieldErrors.entries.map((entry) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.warning_amber_outlined,
                      color: AppColors.error,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        '${_formatFieldName(entry.key)}: ${entry.value}',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: AppColors.error,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ],
      ),
    );
  }

  /// Format field name for display
  String _formatFieldName(String fieldName) {
    switch (fieldName.toLowerCase()) {
      case 'email':
        return 'Email';
      case 'password':
        return 'Password';
      case 'confirmpassword':
        return 'Confirm Password';
      case 'name':
        return 'Name';
      case 'phone':
        return 'Phone';
      default:
        // Convert camelCase to Title Case
        return fieldName
            .replaceAllMapped(
              RegExp(r'([A-Z])'),
              (match) => ' ${match.group(1)}',
            )
            .split(' ')
            .map(
              (word) => word.isNotEmpty
                  ? '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}'
                  : '',
            )
            .join(' ')
            .trim();
    }
  }
}

/// Widget to display success messages
class FormSuccessDisplay extends StatelessWidget {
  const FormSuccessDisplay({
    super.key,
    required this.message,
    this.icon = Icons.check_circle_outline,
    this.padding,
    this.onDismiss,
  });

  /// Success message to display
  final String message;

  /// Icon to display
  final IconData icon;

  /// Custom padding
  final EdgeInsets? padding;

  /// Callback when dismiss button is pressed
  final VoidCallback? onDismiss;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      padding: padding ?? const EdgeInsets.all(AppConstants.defaultPadding),
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.success.withValues(alpha: 0.1),
        border: Border.all(color: AppColors.success.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, color: AppColors.success, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              message,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: AppColors.success,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (onDismiss != null) ...[
            const SizedBox(width: 8),
            GestureDetector(
              onTap: onDismiss,
              child: Icon(Icons.close, color: AppColors.success, size: 18),
            ),
          ],
        ],
      ),
    );
  }
}

/// Widget to display warning messages
class FormWarningDisplay extends StatelessWidget {
  const FormWarningDisplay({
    super.key,
    required this.message,
    this.icon = Icons.warning_amber_outlined,
    this.padding,
    this.onDismiss,
  });

  /// Warning message to display
  final String message;

  /// Icon to display
  final IconData icon;

  /// Custom padding
  final EdgeInsets? padding;

  /// Callback when dismiss button is pressed
  final VoidCallback? onDismiss;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      padding: padding ?? const EdgeInsets.all(AppConstants.defaultPadding),
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.warning.withValues(alpha: 0.1),
        border: Border.all(color: AppColors.warning.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, color: AppColors.warning, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              message,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: AppColors.warning,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (onDismiss != null) ...[
            const SizedBox(width: 8),
            GestureDetector(
              onTap: onDismiss,
              child: Icon(Icons.close, color: AppColors.warning, size: 18),
            ),
          ],
        ],
      ),
    );
  }
}

/// Widget to display info messages
class FormInfoDisplay extends StatelessWidget {
  const FormInfoDisplay({
    super.key,
    required this.message,
    this.icon = Icons.info_outline,
    this.padding,
    this.onDismiss,
  });

  /// Info message to display
  final String message;

  /// Icon to display
  final IconData icon;

  /// Custom padding
  final EdgeInsets? padding;

  /// Callback when dismiss button is pressed
  final VoidCallback? onDismiss;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      padding: padding ?? const EdgeInsets.all(AppConstants.defaultPadding),
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.blue.withValues(alpha: 0.1),
        border: Border.all(color: AppColors.blue.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, color: AppColors.blue, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              message,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: AppColors.blue,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (onDismiss != null) ...[
            const SizedBox(width: 8),
            GestureDetector(
              onTap: onDismiss,
              child: Icon(Icons.close, color: AppColors.blue, size: 18),
            ),
          ],
        ],
      ),
    );
  }
}
