import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../core/theme/app_colors.dart';
import '../../core/theme/app_spacing.dart';
import '../../domain/entities/location_models.dart';
import '../../domain/entities/ride.dart';

/// Callback function for location selection
typedef LocationSelectionCallback = void Function(LocationCoordinates location);

/// Callback function for map tap events
typedef MapTapCallback = void Function(LatLng position);

/// Interactive map widget for ride booking and location selection
class InteractiveMapWidget extends ConsumerStatefulWidget {
  /// Current user location to center the map
  final LocationCoordinates? currentLocation;

  /// Pickup location marker
  final RideLocation? pickupLocation;

  /// Dropoff location marker
  final RideLocation? dropoffLocation;

  /// Whether to show route between pickup and dropoff
  final bool showRoute;

  /// Whether markers can be dragged
  final bool enableMarkerDragging;

  /// Whether to show current location marker
  final bool showCurrentLocation;

  /// Whether to show map controls (zoom, location button)
  final bool showControls;

  /// Initial zoom level
  final double initialZoom;

  /// Callback when pickup location is selected/moved
  final LocationSelectionCallback? onPickupLocationChanged;

  /// Callback when dropoff location is selected/moved
  final LocationSelectionCallback? onDropoffLocationChanged;

  /// Callback when map is tapped
  final MapTapCallback? onMapTap;

  /// Height of the map widget
  final double? height;

  /// Whether the map should fill available space
  final bool expandToFill;

  const InteractiveMapWidget({
    super.key,
    this.currentLocation,
    this.pickupLocation,
    this.dropoffLocation,
    this.showRoute = false,
    this.enableMarkerDragging = true,
    this.showCurrentLocation = true,
    this.showControls = true,
    this.initialZoom = 14.0,
    this.onPickupLocationChanged,
    this.onDropoffLocationChanged,
    this.onMapTap,
    this.height,
    this.expandToFill = false,
  });

  @override
  ConsumerState<InteractiveMapWidget> createState() =>
      _InteractiveMapWidgetState();
}

class _InteractiveMapWidgetState extends ConsumerState<InteractiveMapWidget> {
  late final MapController _mapController;

  // St. Lucia default center coordinates
  static const LatLng _stLuciaCenter = LatLng(13.9094, -60.9789);

  @override
  void initState() {
    super.initState();
    _mapController = MapController();
  }

  @override
  void dispose() {
    _mapController.dispose();
    super.dispose();
  }

  /// Get the center point for the map
  LatLng get _mapCenter {
    if (widget.currentLocation != null) {
      return LatLng(
        widget.currentLocation!.latitude,
        widget.currentLocation!.longitude,
      );
    }
    return _stLuciaCenter;
  }

  /// Build list of markers to display on the map
  List<Marker> _buildMarkers() {
    final List<Marker> markers = [];

    // Current location marker
    if (widget.showCurrentLocation && widget.currentLocation != null) {
      markers.add(
        Marker(
          point: LatLng(
            widget.currentLocation!.latitude,
            widget.currentLocation!.longitude,
          ),
          width: 24,
          height: 24,
          child: const _CurrentLocationMarker(),
        ),
      );
    }

    // Pickup location marker
    if (widget.pickupLocation != null) {
      markers.add(
        Marker(
          point: LatLng(
            widget.pickupLocation!.latitude,
            widget.pickupLocation!.longitude,
          ),
          width: 40,
          height: 40,
          child: _LocationMarker(
            type: LocationMarkerType.pickup,
            isDraggable: widget.enableMarkerDragging,
            onDragEnd: (newPosition) {
              if (widget.onPickupLocationChanged != null) {
                widget.onPickupLocationChanged!(
                  LocationCoordinates(
                    latitude: newPosition.latitude,
                    longitude: newPosition.longitude,
                  ),
                );
              }
            },
          ),
        ),
      );
    }

    // Dropoff location marker
    if (widget.dropoffLocation != null) {
      markers.add(
        Marker(
          point: LatLng(
            widget.dropoffLocation!.latitude,
            widget.dropoffLocation!.longitude,
          ),
          width: 40,
          height: 40,
          child: _LocationMarker(
            type: LocationMarkerType.dropoff,
            isDraggable: widget.enableMarkerDragging,
            onDragEnd: (newPosition) {
              if (widget.onDropoffLocationChanged != null) {
                widget.onDropoffLocationChanged!(
                  LocationCoordinates(
                    latitude: newPosition.latitude,
                    longitude: newPosition.longitude,
                  ),
                );
              }
            },
          ),
        ),
      );
    }

    return markers;
  }

  /// Build polyline for route visualization
  List<Polyline> _buildPolylines() {
    if (!widget.showRoute ||
        widget.pickupLocation == null ||
        widget.dropoffLocation == null) {
      return [];
    }

    // Simple straight line route (in a real app, you'd use a routing service)
    return [
      Polyline(
        points: [
          LatLng(
            widget.pickupLocation!.latitude,
            widget.pickupLocation!.longitude,
          ),
          LatLng(
            widget.dropoffLocation!.latitude,
            widget.dropoffLocation!.longitude,
          ),
        ],
        strokeWidth: 4.0,
        color: AppColors.primary,
        isDotted: true,
      ),
    ];
  }

  /// Handle map tap events
  void _handleMapTap(TapPosition tapPosition, LatLng point) {
    if (widget.onMapTap != null) {
      widget.onMapTap!(point);
    }
  }

  /// Center map on current location
  void _centerOnCurrentLocation() {
    if (widget.currentLocation != null) {
      _mapController.move(
        LatLng(
          widget.currentLocation!.latitude,
          widget.currentLocation!.longitude,
        ),
        widget.initialZoom,
      );
    }
  }

  /// Fit map to show both pickup and dropoff locations
  void _fitToLocations() {
    if (widget.pickupLocation != null && widget.dropoffLocation != null) {
      final bounds = LatLngBounds.fromPoints([
        LatLng(
          widget.pickupLocation!.latitude,
          widget.pickupLocation!.longitude,
        ),
        LatLng(
          widget.dropoffLocation!.latitude,
          widget.dropoffLocation!.longitude,
        ),
      ]);

      _mapController.fitCamera(
        CameraFit.bounds(
          bounds: bounds,
          padding: const EdgeInsets.all(AppSpacing.xl),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final mapWidget = FlutterMap(
      mapController: _mapController,
      options: MapOptions(
        initialCenter: _mapCenter,
        initialZoom: widget.initialZoom,
        minZoom: 10.0,
        maxZoom: 18.0,
        onTap: _handleMapTap,
        // St. Lucia bounds to restrict panning
        cameraConstraint: CameraConstraint.contain(
          bounds: LatLngBounds(
            const LatLng(13.6, -61.2), // Southwest
            const LatLng(14.2, -60.7), // Northeast
          ),
        ),
      ),
      children: [
        // Map tiles
        TileLayer(
          urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
          userAgentPackageName: 'com.example.lucian_rides_app',
          maxZoom: 18,
        ),

        // Route polylines
        PolylineLayer(polylines: _buildPolylines()),

        // Location markers
        MarkerLayer(markers: _buildMarkers()),
      ],
    );

    // Wrap the map with controls if needed
    Widget finalWidget = mapWidget;

    if (widget.showControls) {
      finalWidget = Stack(
        children: [
          mapWidget,
          Positioned(
            top: AppSpacing.md,
            right: AppSpacing.md,
            child: _MapControls(
              onLocationPressed: _centerOnCurrentLocation,
              onFitPressed:
                  widget.pickupLocation != null &&
                      widget.dropoffLocation != null
                  ? _fitToLocations
                  : null,
            ),
          ),
        ],
      );
    }

    if (widget.expandToFill) {
      return finalWidget;
    }

    return SizedBox(height: widget.height ?? 300, child: finalWidget);
  }
}

/// Types of location markers
enum LocationMarkerType { pickup, dropoff }

/// Custom location marker widget
class _LocationMarker extends StatelessWidget {
  final LocationMarkerType type;
  final bool isDraggable;
  final Function(LatLng)? onDragEnd;

  const _LocationMarker({
    required this.type,
    this.isDraggable = false,
    this.onDragEnd,
  });

  @override
  Widget build(BuildContext context) {
    final color = type == LocationMarkerType.pickup
        ? AppColors.pickupPin
        : AppColors.destinationPin;

    final icon = type == LocationMarkerType.pickup
        ? Icons.location_on
        : Icons.flag;

    return Container(
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Icon(icon, color: AppColors.white, size: 24),
    );
  }
}

/// Current location marker widget
class _CurrentLocationMarker extends StatelessWidget {
  const _CurrentLocationMarker();

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.blue,
        shape: BoxShape.circle,
        border: Border.all(color: AppColors.white, width: 2),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: const Icon(Icons.my_location, color: AppColors.white, size: 12),
    );
  }
}

/// Map controls widget
class _MapControls extends StatelessWidget {
  final VoidCallback? onLocationPressed;
  final VoidCallback? onFitPressed;

  const _MapControls({this.onLocationPressed, this.onFitPressed});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Location button
        if (onLocationPressed != null)
          _MapControlButton(
            icon: Icons.my_location,
            onPressed: onLocationPressed!,
            tooltip: 'Center on current location',
          ),

        if (onLocationPressed != null && onFitPressed != null)
          const SizedBox(height: AppSpacing.sm),

        // Fit to locations button
        if (onFitPressed != null)
          _MapControlButton(
            icon: Icons.fit_screen,
            onPressed: onFitPressed!,
            tooltip: 'Fit to route',
          ),
      ],
    );
  }
}

/// Individual map control button
class _MapControlButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback onPressed;
  final String tooltip;

  const _MapControlButton({
    required this.icon,
    required this.onPressed,
    required this.tooltip,
  });

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: tooltip,
      child: Material(
        color: AppColors.white,
        elevation: 4,
        shape: const CircleBorder(),
        child: InkWell(
          onTap: onPressed,
          customBorder: const CircleBorder(),
          child: Container(
            width: 48,
            height: 48,
            decoration: const BoxDecoration(shape: BoxShape.circle),
            child: Icon(icon, color: AppColors.textPrimary, size: 24),
          ),
        ),
      ),
    );
  }
}
