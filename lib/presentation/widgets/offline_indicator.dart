import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../../core/theme/app_colors.dart';
import '../../core/theme/app_spacing.dart';
import '../../core/theme/app_typography.dart';
import '../providers/offline_state.dart';

/// Widget that displays an offline indicator when the device is not connected to the internet
class OfflineIndicator extends ConsumerWidget {
  /// Whether to show the indicator as a banner at the top of the screen
  final bool showAsBanner;

  /// Custom message to display when offline
  final String? customMessage;

  /// Whether to show sync status information
  final bool showSyncStatus;

  const OfflineIndicator({
    super.key,
    this.showAsBanner = true,
    this.customMessage,
    this.showSyncStatus = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final offlineState = ref.watch(offlineStateProvider);

    // Don't show indicator if online or not initialized
    if (offlineState.isOnline || !offlineState.showOfflineIndicator) {
      return const SizedBox.shrink();
    }

    final message =
        customMessage ?? _getOfflineMessage(offlineState.connectivity);

    if (showAsBanner) {
      return _buildBannerIndicator(context, message, offlineState);
    } else {
      return _buildInlineIndicator(context, message, offlineState);
    }
  }

  /// Build banner-style offline indicator
  Widget _buildBannerIndicator(
    BuildContext context,
    String message,
    OfflineState state,
  ) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.md,
        vertical: AppSpacing.sm,
      ),
      decoration: BoxDecoration(
        color: AppColors.error,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SafeArea(
        bottom: false,
        child: Row(
          children: [
            Icon(Icons.cloud_off, color: AppColors.onError, size: 20),
            const SizedBox(width: AppSpacing.sm),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'No Internet Connection',
                    style: AppTypography.bodyMedium.copyWith(
                      color: AppColors.onError,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  if (showSyncStatus && state.lastConnectedAt != null) ...[
                    const SizedBox(height: 2),
                    Text(
                      'Last connected: ${_formatLastConnected(state.lastConnectedAt!)}',
                      style: AppTypography.bodySmall.copyWith(
                        color: AppColors.onError.withValues(alpha: 0.8),
                      ),
                    ),
                  ],
                ],
              ),
            ),
            Consumer(
              builder: (context, ref, child) {
                return IconButton(
                  onPressed: () {
                    ref
                        .read(offlineStateProvider.notifier)
                        .refreshConnectivity();
                  },
                  icon: Icon(Icons.refresh, color: AppColors.onError, size: 20),
                  tooltip: 'Check connection',
                  constraints: const BoxConstraints(
                    minWidth: 32,
                    minHeight: 32,
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  /// Build inline-style offline indicator
  Widget _buildInlineIndicator(
    BuildContext context,
    String message,
    OfflineState state,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      margin: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColors.error.withValues(alpha: 0.1),
        border: Border.all(color: AppColors.error.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(Icons.cloud_off, color: AppColors.error, size: 24),
          const SizedBox(width: AppSpacing.md),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Offline Mode',
                  style: AppTypography.bodyLarge.copyWith(
                    color: AppColors.error,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: AppSpacing.xs),
                Text(
                  message,
                  style: AppTypography.bodyMedium.copyWith(
                    color: AppColors.onSurface.withValues(alpha: 0.7),
                  ),
                ),
                if (showSyncStatus && state.lastConnectedAt != null) ...[
                  const SizedBox(height: AppSpacing.xs),
                  Text(
                    'Last connected: ${_formatLastConnected(state.lastConnectedAt!)}',
                    style: AppTypography.bodySmall.copyWith(
                      color: AppColors.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                ],
              ],
            ),
          ),
          Consumer(
            builder: (context, ref, child) {
              return IconButton(
                onPressed: () {
                  ref.read(offlineStateProvider.notifier).refreshConnectivity();
                },
                icon: Icon(Icons.refresh, color: AppColors.error),
                tooltip: 'Check connection',
              );
            },
          ),
        ],
      ),
    );
  }

  /// Get appropriate offline message based on connectivity type
  String _getOfflineMessage(ConnectivityResult connectivity) {
    switch (connectivity) {
      case ConnectivityResult.none:
        return 'You\'re currently offline. Some features may not be available.';
      case ConnectivityResult.wifi:
      case ConnectivityResult.mobile:
      case ConnectivityResult.ethernet:
        return 'Connection restored but some data may be outdated.';
      default:
        return 'Limited connectivity detected. Some features may not work properly.';
    }
  }

  /// Format last connected timestamp for display
  String _formatLastConnected(String timestamp) {
    try {
      final dateTime = DateTime.parse(timestamp);
      final now = DateTime.now();
      final difference = now.difference(dateTime);

      if (difference.inMinutes < 1) {
        return 'Just now';
      } else if (difference.inMinutes < 60) {
        return '${difference.inMinutes}m ago';
      } else if (difference.inHours < 24) {
        return '${difference.inHours}h ago';
      } else {
        return '${difference.inDays}d ago';
      }
    } catch (e) {
      return 'Unknown';
    }
  }
}

/// Compact offline indicator for use in app bars or status areas
class CompactOfflineIndicator extends ConsumerWidget {
  const CompactOfflineIndicator({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final offlineState = ref.watch(offlineStateProvider);

    if (offlineState.isOnline) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
        vertical: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: AppColors.error,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.cloud_off, color: AppColors.onError, size: 16),
          const SizedBox(width: AppSpacing.xs),
          Text(
            'Offline',
            style: AppTypography.bodySmall.copyWith(
              color: AppColors.onError,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

/// Offline status chip that can be used in various contexts
class OfflineStatusChip extends ConsumerWidget {
  /// Whether to show detailed status information
  final bool showDetails;

  /// Custom styling for the chip
  final EdgeInsetsGeometry? padding;

  const OfflineStatusChip({super.key, this.showDetails = false, this.padding});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final offlineState = ref.watch(offlineStateProvider);

    final isOnline = offlineState.isOnline;
    final color = isOnline ? AppColors.success : AppColors.error;
    final icon = isOnline ? Icons.cloud_done : Icons.cloud_off;
    final text = isOnline ? 'Online' : 'Offline';

    return Container(
      padding:
          padding ??
          const EdgeInsets.symmetric(
            horizontal: AppSpacing.sm,
            vertical: AppSpacing.xs,
          ),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        border: Border.all(color: color.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(width: AppSpacing.xs),
          Text(
            text,
            style: AppTypography.bodySmall.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (showDetails &&
              !isOnline &&
              offlineState.lastConnectedAt != null) ...[
            const SizedBox(width: AppSpacing.xs),
            Text(
              '• ${_formatLastConnected(offlineState.lastConnectedAt!)}',
              style: AppTypography.bodySmall.copyWith(
                color: color.withValues(alpha: 0.7),
              ),
            ),
          ],
        ],
      ),
    );
  }

  String _formatLastConnected(String timestamp) {
    try {
      final dateTime = DateTime.parse(timestamp);
      final now = DateTime.now();
      final difference = now.difference(dateTime);

      if (difference.inMinutes < 1) {
        return 'now';
      } else if (difference.inMinutes < 60) {
        return '${difference.inMinutes}m';
      } else if (difference.inHours < 24) {
        return '${difference.inHours}h';
      } else {
        return '${difference.inDays}d';
      }
    } catch (e) {
      return '?';
    }
  }
}
