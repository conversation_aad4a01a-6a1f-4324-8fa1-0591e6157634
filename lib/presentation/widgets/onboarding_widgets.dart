import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_spacing.dart';
import '../../core/theme/app_typography.dart';
import '../../core/utils/haptic_feedback_utils.dart';

import 'custom_button.dart';

/// Onboarding flow for ride features
class RideOnboardingFlow extends StatefulWidget {
  const RideOnboardingFlow({super.key, required this.onComplete, this.onSkip});

  final VoidCallback onComplete;
  final VoidCallback? onSkip;

  @override
  State<RideOnboardingFlow> createState() => _RideOnboardingFlowState();
}

class _RideOnboardingFlowState extends State<RideOnboardingFlow> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  final List<OnboardingPage> _pages = [
    OnboardingPage(
      title: 'Book Your Ride',
      description:
          'Easily book rides around St. Lucia with just a few taps. Set your pickup and destination, and we\'ll find you a driver.',
      icon: Icons.location_on,
      color: AppColors.primary,
      features: [
        'Quick location selection',
        'Fixed transparent pricing',
        'Real-time driver matching',
      ],
    ),
    OnboardingPage(
      title: 'Track in Real-Time',
      description:
          'Follow your driver\'s location in real-time and get accurate arrival estimates. Stay informed every step of the way.',
      icon: Icons.navigation,
      color: AppColors.blue,
      features: [
        'Live driver tracking',
        'Accurate ETAs',
        'Driver contact options',
      ],
    ),
    OnboardingPage(
      title: 'Safe & Reliable',
      description:
          'All our drivers are verified and rated. Your safety is our priority with 24/7 support and emergency features.',
      icon: Icons.security,
      color: AppColors.success,
      features: ['Verified drivers', 'Rating system', '24/7 support'],
    ),
    OnboardingPage(
      title: 'Ready to Ride?',
      description:
          'You\'re all set! Start exploring St. Lucia with convenient, reliable transportation at your fingertips.',
      icon: Icons.check_circle,
      color: AppColors.primary,
      features: [
        'Book your first ride',
        'Explore the island',
        'Travel with confidence',
      ],
    ),
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _nextPage() {
    HapticFeedbackUtils.navigation();
    if (_currentPage < _pages.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      widget.onComplete();
    }
  }

  void _previousPage() {
    HapticFeedbackUtils.navigation();
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _skipOnboarding() {
    HapticFeedbackUtils.lightImpact();
    if (widget.onSkip != null) {
      widget.onSkip!();
    } else {
      widget.onComplete();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Skip button
            Padding(
              padding: const EdgeInsets.all(AppSpacing.md),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if (_currentPage > 0)
                    TextButton(
                      onPressed: _previousPage,
                      child: const Text('Back'),
                    )
                  else
                    const SizedBox.shrink(),
                  if (_currentPage < _pages.length - 1)
                    TextButton(
                      onPressed: _skipOnboarding,
                      child: const Text('Skip'),
                    )
                  else
                    const SizedBox.shrink(),
                ],
              ),
            ),

            // Page content
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                itemCount: _pages.length,
                itemBuilder: (context, index) {
                  return OnboardingPageWidget(
                    page: _pages[index],
                    isActive: index == _currentPage,
                  );
                },
              ),
            ),

            // Page indicators and navigation
            Padding(
              padding: const EdgeInsets.all(AppSpacing.lg),
              child: Column(
                children: [
                  // Page indicators
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                      _pages.length,
                      (index) => AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        width: _currentPage == index ? 24 : 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: _currentPage == index
                              ? AppColors.primary
                              : AppColors.lightGray,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: AppSpacing.lg),

                  // Navigation button
                  SizedBox(
                    width: double.infinity,
                    child: CustomButton(
                      onPressed: _nextPage,
                      variant: ButtonVariant.primary,
                      child: Text(
                        _currentPage == _pages.length - 1
                            ? 'Get Started'
                            : 'Continue',
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Individual onboarding page widget
class OnboardingPageWidget extends StatelessWidget {
  const OnboardingPageWidget({
    super.key,
    required this.page,
    required this.isActive,
  });

  final OnboardingPage page;
  final bool isActive;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Animated icon
          AnimatedScale(
            scale: isActive ? 1.0 : 0.5,
            duration: const Duration(milliseconds: 600),
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: page.color.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(page.icon, size: 60, color: page.color),
            ),
          ),

          const SizedBox(height: AppSpacing.xl),

          // Title
          AnimatedOpacity(
            opacity: isActive ? 1.0 : 0.0,
            duration: const Duration(milliseconds: 400),
            child: Text(
              page.title,
              style: AppTypography.h2.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          const SizedBox(height: AppSpacing.md),

          // Description
          AnimatedOpacity(
            opacity: isActive ? 1.0 : 0.0,
            duration: const Duration(milliseconds: 600),
            child: Text(
              page.description,
              style: AppTypography.bodyLarge.copyWith(
                color: AppColors.textSecondary,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          const SizedBox(height: AppSpacing.xl),

          // Features list
          AnimatedContainer(
            duration: const Duration(milliseconds: 800),
            child: Column(
              children: page.features.asMap().entries.map((entry) {
                final index = entry.key;
                final feature = entry.value;
                return AnimatedOpacity(
                  opacity: isActive ? 1.0 : 0.0,
                  duration: Duration(milliseconds: 1000 + (index * 100)),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: Row(
                      children: [
                        Icon(Icons.check_circle, color: page.color, size: 20),
                        const SizedBox(width: AppSpacing.sm),
                        Expanded(
                          child: Text(
                            feature,
                            style: AppTypography.bodyMedium.copyWith(
                              color: AppColors.textPrimary,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }
}

/// Data class for onboarding pages
class OnboardingPage {
  const OnboardingPage({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    required this.features,
  });

  final String title;
  final String description;
  final IconData icon;
  final Color color;
  final List<String> features;
}

/// Feature tooltip for highlighting new features
class FeatureTooltip extends StatefulWidget {
  const FeatureTooltip({
    super.key,
    required this.child,
    required this.message,
    required this.targetKey,
    this.onDismiss,
    this.showArrow = true,
    this.backgroundColor,
  });

  final Widget child;
  final String message;
  final GlobalKey targetKey;
  final VoidCallback? onDismiss;
  final bool showArrow;
  final Color? backgroundColor;

  @override
  State<FeatureTooltip> createState() => _FeatureTooltipState();
}

class _FeatureTooltipState extends State<FeatureTooltip>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeOutBack));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _dismiss() {
    HapticFeedbackUtils.lightImpact();
    _controller.reverse().then((_) {
      widget.onDismiss?.call();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        Positioned.fill(
          child: AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              return Opacity(
                opacity: _fadeAnimation.value,
                child: Container(
                  color: Colors.black.withValues(alpha: 0.5),
                  child: Center(
                    child: Transform.scale(
                      scale: _scaleAnimation.value,
                      child: Container(
                        margin: const EdgeInsets.all(AppSpacing.lg),
                        padding: const EdgeInsets.all(AppSpacing.lg),
                        decoration: BoxDecoration(
                          color: widget.backgroundColor ?? AppColors.surface,
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.2),
                              blurRadius: 20,
                              offset: const Offset(0, 10),
                            ),
                          ],
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.lightbulb_outline,
                              color: AppColors.primary,
                              size: 32,
                            ),
                            const SizedBox(height: AppSpacing.md),
                            Text(
                              'New Feature!',
                              style: AppTypography.h4.copyWith(
                                fontWeight: FontWeight.bold,
                                color: AppColors.primary,
                              ),
                            ),
                            const SizedBox(height: AppSpacing.sm),
                            Text(
                              widget.message,
                              style: AppTypography.bodyMedium.copyWith(
                                color: AppColors.textPrimary,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: AppSpacing.lg),
                            CustomButton(
                              onPressed: _dismiss,
                              variant: ButtonVariant.primary,
                              child: const Text('Got it!'),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

/// Quick tour overlay for showing app features
class QuickTour extends StatefulWidget {
  const QuickTour({
    super.key,
    required this.steps,
    required this.onComplete,
    this.onSkip,
  });

  final List<TourStep> steps;
  final VoidCallback onComplete;
  final VoidCallback? onSkip;

  @override
  State<QuickTour> createState() => _QuickTourState();
}

class _QuickTourState extends State<QuickTour> {
  int _currentStep = 0;

  void _nextStep() {
    HapticFeedbackUtils.lightImpact();
    if (_currentStep < widget.steps.length - 1) {
      setState(() {
        _currentStep++;
      });
    } else {
      widget.onComplete();
    }
  }

  void _skipTour() {
    HapticFeedbackUtils.lightImpact();
    if (widget.onSkip != null) {
      widget.onSkip!();
    } else {
      widget.onComplete();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_currentStep >= widget.steps.length) {
      return const SizedBox.shrink();
    }

    final step = widget.steps[_currentStep];

    return Material(
      color: Colors.black.withValues(alpha: 0.7),
      child: Stack(
        children: [
          // Highlight area
          if (step.targetKey.currentContext != null)
            _buildHighlight(step.targetKey.currentContext!),

          // Tooltip
          _buildTooltip(step),

          // Skip button
          Positioned(
            top: MediaQuery.of(context).padding.top + 16,
            right: 16,
            child: TextButton(
              onPressed: _skipTour,
              child: Text('Skip Tour', style: TextStyle(color: Colors.white)),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHighlight(BuildContext targetContext) {
    final RenderBox renderBox = targetContext.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final position = renderBox.localToGlobal(Offset.zero);

    return Positioned(
      left: position.dx - 8,
      top: position.dy - 8,
      child: Container(
        width: size.width + 16,
        height: size.height + 16,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: AppColors.primary.withValues(alpha: 0.3),
              blurRadius: 20,
              spreadRadius: 4,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTooltip(TourStep step) {
    return Positioned(
      bottom: 100,
      left: 16,
      right: 16,
      child: Container(
        padding: const EdgeInsets.all(AppSpacing.lg),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              step.title,
              style: AppTypography.h4.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(step.description, style: AppTypography.bodyMedium),
            const SizedBox(height: AppSpacing.lg),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${_currentStep + 1} of ${widget.steps.length}',
                  style: AppTypography.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                CustomButton(
                  onPressed: _nextStep,
                  variant: ButtonVariant.primary,
                  child: Text(
                    _currentStep == widget.steps.length - 1 ? 'Finish' : 'Next',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// Data class for tour steps
class TourStep {
  const TourStep({
    required this.targetKey,
    required this.title,
    required this.description,
  });

  final GlobalKey targetKey;
  final String title;
  final String description;
}

/// Welcome banner for first-time users
class WelcomeBanner extends StatelessWidget {
  const WelcomeBanner({
    super.key,
    required this.userName,
    required this.onStartTour,
    required this.onDismiss,
  });

  final String userName;
  final VoidCallback onStartTour;
  final VoidCallback onDismiss;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(AppSpacing.md),
      padding: const EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Icon(Icons.waving_hand, color: Colors.white, size: 24),
              IconButton(
                onPressed: onDismiss,
                icon: Icon(Icons.close, color: Colors.white),
                iconSize: 20,
              ),
            ],
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            'Welcome to Lucian Rides, $userName!',
            style: AppTypography.h4.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            'Ready to explore St. Lucia? Let us show you around the app.',
            style: AppTypography.bodyMedium.copyWith(
              color: Colors.white.withValues(alpha: 0.9),
            ),
          ),
          const SizedBox(height: AppSpacing.lg),
          Row(
            children: [
              Expanded(
                child: CustomButton(
                  onPressed: onStartTour,
                  variant: ButtonVariant.secondary,
                  child: const Text('Take Tour'),
                ),
              ),
              const SizedBox(width: AppSpacing.sm),
              Expanded(
                child: CustomButton(
                  onPressed: onDismiss,
                  variant: ButtonVariant.secondary,
                  child: const Text('Skip'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
