import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../domain/entities/ride.dart';
import '../providers/ride_cancellation_notifier.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_spacing.dart';
import '../../core/theme/app_typography.dart';
import 'ride_cancellation_dialog.dart';

/// Button widget for initiating ride cancellation
class RideCancellationButton extends ConsumerWidget {
  /// The ride that can be cancelled
  final Ride ride;

  /// Callback when cancellation is completed successfully
  final VoidCallback? onCancellationComplete;

  /// Button style variant
  final RideCancellationButtonStyle style;

  /// Whether the button should be expanded to full width
  final bool expanded;

  const RideCancellationButton({
    super.key,
    required this.ride,
    this.onCancellationComplete,
    this.style = RideCancellationButtonStyle.outlined,
    this.expanded = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Check if ride can be cancelled
    final canCancel = ride.canBeCancelledByRider();

    if (!canCancel) {
      return const SizedBox.shrink();
    }

    final button = _buildButton(context, ref);

    return expanded ? SizedBox(width: double.infinity, child: button) : button;
  }

  Widget _buildButton(BuildContext context, WidgetRef ref) {
    switch (style) {
      case RideCancellationButtonStyle.filled:
        return ElevatedButton.icon(
          onPressed: () => _showCancellationDialog(context, ref),
          icon: const Icon(Icons.cancel_outlined, size: 18),
          label: const Text('Cancel Ride'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.error,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(
              horizontal: AppSpacing.lg,
              vertical: AppSpacing.md,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );

      case RideCancellationButtonStyle.outlined:
        return OutlinedButton.icon(
          onPressed: () => _showCancellationDialog(context, ref),
          icon: Icon(Icons.cancel_outlined, size: 18, color: AppColors.error),
          label: Text('Cancel Ride', style: TextStyle(color: AppColors.error)),
          style: OutlinedButton.styleFrom(
            side: BorderSide(color: AppColors.error),
            padding: const EdgeInsets.symmetric(
              horizontal: AppSpacing.lg,
              vertical: AppSpacing.md,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );

      case RideCancellationButtonStyle.text:
        return TextButton.icon(
          onPressed: () => _showCancellationDialog(context, ref),
          icon: Icon(Icons.cancel_outlined, size: 18, color: AppColors.error),
          label: Text('Cancel Ride', style: TextStyle(color: AppColors.error)),
          style: TextButton.styleFrom(
            padding: const EdgeInsets.symmetric(
              horizontal: AppSpacing.md,
              vertical: AppSpacing.sm,
            ),
          ),
        );

      case RideCancellationButtonStyle.iconOnly:
        return IconButton(
          onPressed: () => _showCancellationDialog(context, ref),
          icon: Icon(Icons.cancel_outlined, color: AppColors.error),
          tooltip: 'Cancel Ride',
          style: IconButton.styleFrom(
            backgroundColor: AppColors.error.withValues(alpha: 0.1),
            padding: const EdgeInsets.all(AppSpacing.sm),
          ),
        );
    }
  }

  /// Show the cancellation dialog
  void _showCancellationDialog(BuildContext context, WidgetRef ref) {
    // Reset cancellation state before showing dialog
    ref.read(rideCancellationNotifierProvider.notifier).resetCancellation();

    showDialog<void>(
      context: context,
      barrierDismissible: false, // Prevent dismissing by tapping outside
      builder: (context) => RideCancellationDialog(
        ride: ride,
        onCancellationComplete: () {
          // Reset state after successful cancellation
          ref
              .read(rideCancellationNotifierProvider.notifier)
              .resetCancellation();
          onCancellationComplete?.call();
        },
        onDismiss: () {
          // Reset state when dialog is dismissed
          ref
              .read(rideCancellationNotifierProvider.notifier)
              .resetCancellation();
        },
      ),
    );
  }
}

/// Style variants for the cancellation button
enum RideCancellationButtonStyle {
  /// Filled button with error background color
  filled,

  /// Outlined button with error border
  outlined,

  /// Text button with error color
  text,

  /// Icon-only button
  iconOnly,
}

/// Compact cancellation button for use in app bars or tight spaces
class CompactRideCancellationButton extends ConsumerWidget {
  /// The ride that can be cancelled
  final Ride ride;

  /// Callback when cancellation is completed successfully
  final VoidCallback? onCancellationComplete;

  const CompactRideCancellationButton({
    super.key,
    required this.ride,
    this.onCancellationComplete,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return RideCancellationButton(
      ride: ride,
      onCancellationComplete: onCancellationComplete,
      style: RideCancellationButtonStyle.iconOnly,
    );
  }
}

/// Cancellation action card for displaying in ride details
class RideCancellationCard extends ConsumerWidget {
  /// The ride that can be cancelled
  final Ride ride;

  /// Callback when cancellation is completed successfully
  final VoidCallback? onCancellationComplete;

  const RideCancellationCard({
    super.key,
    required this.ride,
    this.onCancellationComplete,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Check if ride can be cancelled
    final canCancel = ride.canBeCancelledByRider();

    if (!canCancel) {
      return const SizedBox.shrink();
    }

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: AppColors.warning, size: 20),
                const SizedBox(width: AppSpacing.sm),
                Text(
                  'Need to cancel?',
                  style: AppTypography.bodyLarge.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              _getCancellationMessage(ride),
              style: AppTypography.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: AppSpacing.md),
            RideCancellationButton(
              ride: ride,
              onCancellationComplete: onCancellationComplete,
              style: RideCancellationButtonStyle.outlined,
              expanded: true,
            ),
          ],
        ),
      ),
    );
  }

  /// Get appropriate cancellation message based on ride status
  String _getCancellationMessage(Ride ride) {
    switch (ride.status) {
      case RideStatus.requested:
        return 'You can cancel this ride for free since no driver has been assigned yet.';
      case RideStatus.accepted:
        final timeSinceAccepted = DateTime.now().difference(
          ride.acceptedAt ?? ride.createdAt,
        );

        if (timeSinceAccepted.inMinutes <= 5) {
          return 'Free cancellation is available for the next ${5 - timeSinceAccepted.inMinutes} minutes.';
        } else {
          return 'A cancellation fee may apply since your driver is already on the way.';
        }
      default:
        return 'You can cancel this ride, but fees may apply.';
    }
  }
}
