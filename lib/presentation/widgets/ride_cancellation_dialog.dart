import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../domain/entities/ride.dart';
import '../providers/ride_cancellation_notifier.dart';
import '../providers/ride_cancellation_state.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_spacing.dart';
import '../../core/theme/app_typography.dart';
import 'custom_button.dart';

/// Dialog for ride cancellation with reason selection and confirmation
class RideCancellationDialog extends ConsumerStatefulWidget {
  /// The ride to be cancelled
  final Ride ride;

  /// Callback when cancellation is completed successfully
  final VoidCallback? onCancellationComplete;

  /// Callback when dialog is dismissed
  final VoidCallback? onDismiss;

  const RideCancellationDialog({
    super.key,
    required this.ride,
    this.onCancellationComplete,
    this.onDismiss,
  });

  @override
  ConsumerState<RideCancellationDialog> createState() =>
      _RideCancellationDialogState();
}

class _RideCancellationDialogState
    extends ConsumerState<RideCancellationDialog> {
  final TextEditingController _customReasonController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Initialize cancellation flow when dialog opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref
          .read(rideCancellationNotifierProvider.notifier)
          .initializeCancellation(widget.ride);
    });
  }

  @override
  void dispose() {
    _customReasonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final cancellationState = ref.watch(rideCancellationNotifierProvider);
    final notifier = ref.read(rideCancellationNotifierProvider.notifier);

    // Listen for successful cancellation
    ref.listen<RideCancellationState>(rideCancellationNotifierProvider, (
      previous,
      current,
    ) {
      if (current.cancellationSuccessful && !current.isCancelling) {
        // Close dialog and notify parent
        Navigator.of(context).pop();
        widget.onCancellationComplete?.call();
      }
    });

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 400),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(AppSpacing.lg),
              decoration: BoxDecoration(
                color: AppColors.error.withValues(alpha: 0.1),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Icon(Icons.cancel_outlined, color: AppColors.error, size: 24),
                  const SizedBox(width: AppSpacing.md),
                  Expanded(
                    child: Text(
                      'Cancel Ride',
                      style: AppTypography.h3.copyWith(
                        color: AppColors.error,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      widget.onDismiss?.call();
                    },
                    icon: const Icon(Icons.close),
                    iconSize: 20,
                  ),
                ],
              ),
            ),

            // Content
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppSpacing.lg),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (cancellationState.showReasonDialog) ...[
                      _buildReasonSelection(
                        context,
                        cancellationState,
                        notifier,
                      ),
                    ] else if (cancellationState.showConfirmationDialog) ...[
                      _buildConfirmationStep(
                        context,
                        cancellationState,
                        notifier,
                      ),
                    ],

                    // Error message
                    if (cancellationState.hasError) ...[
                      const SizedBox(height: AppSpacing.md),
                      Container(
                        padding: const EdgeInsets.all(AppSpacing.md),
                        decoration: BoxDecoration(
                          color: AppColors.error.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: AppColors.error.withValues(alpha: 0.3),
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.error_outline,
                              color: AppColors.error,
                              size: 20,
                            ),
                            const SizedBox(width: AppSpacing.sm),
                            Expanded(
                              child: Text(
                                cancellationState.errorMessage!,
                                style: AppTypography.bodyMedium.copyWith(
                                  color: AppColors.error,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build reason selection step
  Widget _buildReasonSelection(
    BuildContext context,
    RideCancellationState state,
    RideCancellationNotifier notifier,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Why are you cancelling this ride?',
          style: AppTypography.h3.copyWith(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: AppSpacing.sm),
        Text(
          'Please select a reason for cancellation. This helps us improve our service.',
          style: AppTypography.bodyMedium.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: AppSpacing.lg),

        // Reason options
        ...CancellationReasonInfo.availableReasons.map((reasonInfo) {
          final isSelected = state.selectedReason == reasonInfo.reason;

          return Padding(
            padding: const EdgeInsets.only(bottom: AppSpacing.sm),
            child: InkWell(
              onTap: () => notifier.selectReason(reasonInfo.reason),
              borderRadius: BorderRadius.circular(8),
              child: Container(
                padding: const EdgeInsets.all(AppSpacing.md),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: isSelected ? AppColors.primary : AppColors.border,
                    width: isSelected ? 2 : 1,
                  ),
                  borderRadius: BorderRadius.circular(8),
                  color: isSelected
                      ? AppColors.primary.withValues(alpha: 0.05)
                      : null,
                ),
                child: Row(
                  children: [
                    Radio<CancellationReason>(
                      value: reasonInfo.reason,
                      groupValue: state.selectedReason,
                      onChanged: (reason) {
                        if (reason != null) {
                          notifier.selectReason(reason);
                        }
                      },
                      activeColor: AppColors.primary,
                    ),
                    const SizedBox(width: AppSpacing.sm),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            reasonInfo.title,
                            style: AppTypography.bodyLarge.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: AppSpacing.xs),
                          Text(
                            reasonInfo.description,
                            style: AppTypography.bodySmall.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }),

        // Custom reason text field (shown when "Other" is selected)
        if (state.selectedReason == CancellationReason.other) ...[
          const SizedBox(height: AppSpacing.md),
          TextFormField(
            controller: _customReasonController,
            onChanged: notifier.setCustomReason,
            maxLines: 3,
            maxLength: 200,
            decoration: InputDecoration(
              labelText: 'Please specify your reason',
              hintText: 'Enter your reason for cancellation...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppColors.primary),
              ),
            ),
          ),
        ],

        const SizedBox(height: AppSpacing.xl),

        // Action buttons
        Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  widget.onDismiss?.call();
                },
                child: const Text('Keep Ride'),
              ),
            ),
            const SizedBox(width: AppSpacing.md),
            Expanded(
              child: CustomButton(
                onPressed: state.hasSelectedReason && state.isCustomReasonValid
                    ? () => notifier.showConfirmation()
                    : null,
                variant: ButtonVariant.danger,
                child: const Text('Continue'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build confirmation step
  Widget _buildConfirmationStep(
    BuildContext context,
    RideCancellationState state,
    RideCancellationNotifier notifier,
  ) {
    final policy = state.cancellationPolicy;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Confirm Cancellation',
          style: AppTypography.h3.copyWith(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: AppSpacing.md),

        // Selected reason summary
        Container(
          padding: const EdgeInsets.all(AppSpacing.md),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: AppColors.border),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Cancellation Reason:',
                style: AppTypography.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: AppSpacing.xs),
              Text(state.selectedReasonText, style: AppTypography.bodyMedium),
            ],
          ),
        ),

        const SizedBox(height: AppSpacing.lg),

        // Cancellation policy
        if (policy != null) ...[
          Text(
            'Cancellation Policy',
            style: AppTypography.bodyLarge.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),

          Container(
            padding: const EdgeInsets.all(AppSpacing.md),
            decoration: BoxDecoration(
              color: AppColors.warning.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppColors.warning.withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(policy.policyText, style: AppTypography.bodyMedium),

                if (policy.additionalTerms?.isNotEmpty == true) ...[
                  const SizedBox(height: AppSpacing.sm),
                  ...policy.additionalTerms!.map(
                    (term) => Padding(
                      padding: const EdgeInsets.only(top: AppSpacing.xs),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '• ',
                            style: AppTypography.bodySmall.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                          Expanded(
                            child: Text(
                              term,
                              style: AppTypography.bodySmall.copyWith(
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),

          const SizedBox(height: AppSpacing.md),

          // Refund information
          if (state.hasRefund || state.hasCancellationFee) ...[
            Container(
              padding: const EdgeInsets.all(AppSpacing.md),
              decoration: BoxDecoration(
                color: AppColors.info.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppColors.info.withValues(alpha: 0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Refund Information',
                    style: AppTypography.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: AppSpacing.sm),
                  Text(
                    ref.read(refundInfoProvider),
                    style: AppTypography.bodyMedium,
                  ),
                ],
              ),
            ),
            const SizedBox(height: AppSpacing.md),
          ],

          // Policy confirmation checkbox
          CheckboxListTile(
            value: state.policyConfirmed,
            onChanged: (value) => notifier.confirmPolicy(value ?? false),
            title: Text(
              'I understand the cancellation policy and agree to the terms',
              style: AppTypography.bodyMedium,
            ),
            controlAffinity: ListTileControlAffinity.leading,
            activeColor: AppColors.primary,
            contentPadding: EdgeInsets.zero,
          ),

          const SizedBox(height: AppSpacing.xl),
        ],

        // Action buttons
        Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: state.isCancelling
                    ? null
                    : () => notifier.showReasonSelection(),
                child: const Text('Back'),
              ),
            ),
            const SizedBox(width: AppSpacing.md),
            Expanded(
              child: CustomButton(
                onPressed:
                    state.canProceedWithCancellation && !state.isCancelling
                    ? () => notifier.cancelRide()
                    : null,
                variant: ButtonVariant.danger,
                isLoading: state.isCancelling,
                loadingText: 'Cancelling...',
                child: const Text('Cancel Ride'),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
