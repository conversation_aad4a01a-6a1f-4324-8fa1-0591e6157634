import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../core/theme/app_colors.dart';
import '../../core/theme/app_spacing.dart';
import '../../core/theme/app_typography.dart';
import '../../core/utils/haptic_feedback_utils.dart';
import '../../core/utils/accessibility_utils.dart';
import '../../core/utils/performance_utils.dart';

import 'enhanced_loading_states.dart';
import 'smooth_transitions.dart';
import 'onboarding_widgets.dart';
import 'custom_button.dart';
import 'accessible_focus_widget.dart';

/// Showcase widget demonstrating all UX enhancements
/// This widget serves as a comprehensive example of how all the polishing features work together
class UXEnhancementsShowcase extends ConsumerStatefulWidget {
  const UXEnhancementsShowcase({super.key});

  @override
  ConsumerState<UXEnhancementsShowcase> createState() =>
      _UXEnhancementsShowcaseState();
}

class _UXEnhancementsShowcaseState extends ConsumerState<UXEnhancementsShowcase>
    with TickerProviderStateMixin {
  bool _isLoading = false;
  bool _showOnboarding = false;
  int _selectedDemo = 0;

  final List<DemoItem> _demoItems = [
    DemoItem(
      title: 'Enhanced Loading States',
      description:
          'Smooth, animated loading indicators with contextual messaging',
      icon: Icons.hourglass_empty,
      color: AppColors.primary,
    ),
    DemoItem(
      title: 'Haptic Feedback',
      description: 'Tactile responses for different interaction types',
      icon: Icons.vibration,
      color: AppColors.blue,
    ),
    DemoItem(
      title: 'Smooth Transitions',
      description: 'Fluid animations and page transitions',
      icon: Icons.animation,
      color: AppColors.success,
    ),
    DemoItem(
      title: 'Accessibility Features',
      description: 'Screen reader support and keyboard navigation',
      icon: Icons.accessibility,
      color: AppColors.warning,
    ),
    DemoItem(
      title: 'Performance Optimizations',
      description: 'Memory-efficient widgets and optimized rendering',
      icon: Icons.speed,
      color: AppColors.error,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('UX Enhancements Showcase'),
        actions: [
          IconButton(
            onPressed: () {
              HapticFeedbackUtils.lightImpact();
              setState(() {
                _showOnboarding = true;
              });
            },
            icon: const Icon(Icons.help_outline),
            tooltip: 'Show Onboarding',
          ),
        ],
      ),
      body: Stack(
        children: [
          // Main content
          _buildMainContent(),

          // Loading overlay
          if (_isLoading)
            const BlurredLoadingOverlay(
              isLoading: true,
              message: 'Demonstrating enhanced loading...',
              showProgress: true,
              child: SizedBox.shrink(),
            ),

          // Onboarding overlay
          if (_showOnboarding)
            RideOnboardingFlow(
              onComplete: () {
                setState(() {
                  _showOnboarding = false;
                });
                HapticFeedbackUtils.success();
                AccessibilityUtils.announceToScreenReader(
                  context,
                  'Onboarding completed',
                );
              },
              onSkip: () {
                setState(() {
                  _showOnboarding = false;
                });
              },
            ),
        ],
      ),
    );
  }

  Widget _buildMainContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome section
          _buildWelcomeSection(),

          const SizedBox(height: AppSpacing.xl),

          // Demo items with staggered animation
          SmoothListTransition(
            children: _demoItems.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              return _buildDemoCard(item, index);
            }).toList(),
          ),

          const SizedBox(height: AppSpacing.xl),

          // Action buttons
          _buildActionButtons(),

          const SizedBox(height: AppSpacing.xl),

          // Performance metrics (debug only)
          if (PerformanceUtils.isLowEndDevice()) _buildPerformanceInfo(),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return AccessibleFocusWidget(
      semanticLabel: 'UX Enhancements Welcome Section',
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppSpacing.lg),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              AppColors.primary,
              AppColors.primary.withValues(alpha: 0.8),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: AppColors.primary.withValues(alpha: 0.3),
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(Icons.auto_awesome, color: Colors.white, size: 32),
            const SizedBox(height: AppSpacing.md),
            Text(
              'Enhanced User Experience',
              style: AppTypography.h3.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              'Experience the polished interactions, smooth animations, and accessibility features that make Lucian Rides delightful to use.',
              style: AppTypography.bodyMedium.copyWith(
                color: Colors.white.withValues(alpha: 0.9),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDemoCard(DemoItem item, int index) {
    final isSelected = _selectedDemo == index;

    return AccessibleFocusWidget(
      semanticLabel: '${item.title} demo card',
      semanticHint: item.description,
      isButton: true,
      onTap: () {
        HapticFeedbackUtils.selectionClick();
        setState(() {
          _selectedDemo = index;
        });
        _demonstrateFeature(item, index);
      },
      child: SmoothAnimatedContainer(
        margin: const EdgeInsets.only(bottom: AppSpacing.md),
        padding: const EdgeInsets.all(AppSpacing.lg),
        decoration: BoxDecoration(
          color: isSelected
              ? item.color.withValues(alpha: 0.1)
              : AppColors.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? item.color : AppColors.border,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: item.color.withValues(alpha: 0.2),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ]
              : null,
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: item.color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(item.icon, color: item.color, size: 24),
            ),
            const SizedBox(width: AppSpacing.md),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item.title,
                    style: AppTypography.labelLarge.copyWith(
                      fontWeight: FontWeight.w600,
                      color: isSelected ? item.color : AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    item.description,
                    style: AppTypography.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              Icon(Icons.check_circle, color: item.color, size: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          'Try These Features',
          style: AppTypography.h4.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: AppSpacing.md),

        Row(
          children: [
            Expanded(
              child: CustomButton(
                onPressed: () => _demonstrateLoadingStates(),
                variant: ButtonVariant.primary,
                icon: Icons.hourglass_empty,
                child: const Text('Loading Demo'),
              ),
            ),
            const SizedBox(width: AppSpacing.sm),
            Expanded(
              child: CustomButton(
                onPressed: () => _demonstrateHapticFeedback(),
                variant: ButtonVariant.secondary,
                icon: Icons.vibration,
                child: const Text('Haptic Demo'),
              ),
            ),
          ],
        ),

        const SizedBox(height: AppSpacing.sm),

        Row(
          children: [
            Expanded(
              child: CustomButton(
                onPressed: () => _demonstrateTransitions(),
                variant: ButtonVariant.ghost,
                icon: Icons.animation,
                child: const Text('Transitions'),
              ),
            ),
            const SizedBox(width: AppSpacing.sm),
            Expanded(
              child: CustomButton(
                onPressed: () => _demonstrateAccessibility(),
                variant: ButtonVariant.secondary,
                icon: Icons.accessibility,
                child: const Text('Accessibility'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPerformanceInfo() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColors.warning.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.warning.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.info, color: AppColors.warning),
          const SizedBox(width: AppSpacing.sm),
          Expanded(
            child: Text(
              'Low-end device detected. Performance optimizations are active.',
              style: AppTypography.bodySmall.copyWith(color: AppColors.warning),
            ),
          ),
        ],
      ),
    );
  }

  void _demonstrateFeature(DemoItem item, int index) {
    switch (index) {
      case 0:
        _demonstrateLoadingStates();
        break;
      case 1:
        _demonstrateHapticFeedback();
        break;
      case 2:
        _demonstrateTransitions();
        break;
      case 3:
        _demonstrateAccessibility();
        break;
      case 4:
        _demonstratePerformance();
        break;
    }
  }

  void _demonstrateLoadingStates() {
    setState(() {
      _isLoading = true;
    });

    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        SmoothSnackBar.show(
          context: context,
          message: 'Loading demonstration completed!',
          type: SnackBarType.success,
        );
      }
    });
  }

  void _demonstrateHapticFeedback() async {
    await HapticFeedbackUtils.lightImpact();
    await Future.delayed(const Duration(milliseconds: 200));
    await HapticFeedbackUtils.mediumImpact();
    await Future.delayed(const Duration(milliseconds: 200));
    await HapticFeedbackUtils.heavyImpact();
    await Future.delayed(const Duration(milliseconds: 200));
    await HapticFeedbackUtils.success();

    SmoothSnackBar.show(
      context: context,
      message: 'Haptic feedback sequence completed!',
      type: SnackBarType.info,
    );
  }

  void _demonstrateTransitions() {
    SmoothDialog.show(
      context: context,
      child: Container(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.animation, size: 48, color: AppColors.primary),
            const SizedBox(height: AppSpacing.md),
            Text(
              'Smooth Transitions',
              style: AppTypography.h4.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              'This dialog appeared with a smooth scale and fade animation!',
              style: AppTypography.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSpacing.lg),
            CustomButton(
              onPressed: () => Navigator.of(context).pop(),
              variant: ButtonVariant.primary,
              child: const Text('Close'),
            ),
          ],
        ),
      ),
    );
  }

  void _demonstrateAccessibility() {
    AccessibilityUtils.announceToScreenReader(
      context,
      'Accessibility demonstration: This message is being announced to screen readers. The app includes semantic labels, focus management, and keyboard navigation support.',
    );

    SmoothSnackBar.show(
      context: context,
      message: 'Accessibility announcement sent to screen readers',
      type: SnackBarType.info,
    );
  }

  void _demonstratePerformance() {
    // Clear computation cache to demonstrate performance optimization
    PerformanceUtils.clearComputationCache();

    // Demonstrate optimized list building
    final items = List.generate(1000, (index) => 'Item $index');

    SmoothBottomSheet.show(
      context: context,
      child: Container(
        height: 400,
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          children: [
            Text(
              'Performance Optimization Demo',
              style: AppTypography.h4.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppSpacing.md),
            Text(
              'This list of 1000 items uses performance optimizations for smooth scrolling:',
              style: AppTypography.bodyMedium,
            ),
            const SizedBox(height: AppSpacing.md),
            Expanded(
              child: PerformanceUtils.optimizedListView(
                itemCount: items.length,
                itemBuilder: (context, index) {
                  return ListTile(
                    title: Text(items[index]),
                    subtitle: Text('Optimized item #$index'),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Data class for demo items
class DemoItem {
  const DemoItem({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
  });

  final String title;
  final String description;
  final IconData icon;
  final Color color;
}
