import 'dart:async';
import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';

import '../../core/errors/app_error.dart';
import '../../domain/entities/location_models.dart';

import 'location_service.dart';

/// Fallback service for location operations when primary location service fails
abstract class LocationFallbackService {
  /// Get fallback location suggestions when location services are unavailable
  Future<Either<AppError, List<LocationSuggestion>>> getFallbackSuggestions({
    String? query,
    int limit = 10,
  });

  /// Get default location for St. Lucia when current location is unavailable
  Either<AppError, LocationCoordinates> getDefaultLocation();

  /// Get popular destinations as fallback options
  List<LocationSuggestion> getPopularDestinations({int limit = 5});

  /// Get cached recent locations as fallback
  Future<Either<AppError, List<RecentLocation>>> getCachedRecentLocations();

  /// Provide manual location entry options
  List<LocationInputOption> getManualInputOptions();

  /// Get location from known landmarks
  Either<AppError, LocationCoordinates> getLocationFromLandmark(
    String landmark,
  );

  /// Get approximate location from IP (if available)
  Future<Either<AppError, LocationCoordinates>> getApproximateLocationFromIP();
}

/// Implementation of location fallback service
class LocationFallbackServiceImpl implements LocationFallbackService {
  final LocationService _locationService;

  /// Popular St. Lucia locations for fallback suggestions
  static const List<Map<String, dynamic>> _popularLocations = [
    {
      'name': 'Castries City Center',
      'address': 'Castries, St. Lucia',
      'latitude': 14.0101,
      'longitude': -60.9897,
      'category': 'City Center',
      'description': 'Capital city and main commercial center',
    },
    {
      'name': 'Rodney Bay',
      'address': 'Rodney Bay, Gros Islet, St. Lucia',
      'latitude': 14.0783,
      'longitude': -60.9564,
      'category': 'Tourist Area',
      'description':
          'Popular tourist destination with restaurants and nightlife',
    },
    {
      'name': 'Hewanorra International Airport',
      'address': 'Vieux Fort, St. Lucia',
      'latitude': 13.7332,
      'longitude': -60.9526,
      'category': 'Airport',
      'description': 'Main international airport',
    },
    {
      'name': 'George F.L. Charles Airport',
      'address': 'Castries, St. Lucia',
      'latitude': 14.0196,
      'longitude': -60.9930,
      'category': 'Airport',
      'description': 'Regional airport near Castries',
    },
    {
      'name': 'Soufrière',
      'address': 'Soufrière, St. Lucia',
      'latitude': 13.8566,
      'longitude': -61.0566,
      'category': 'Town',
      'description': 'Historic town near the Pitons',
    },
    {
      'name': 'Marigot Bay',
      'address': 'Marigot Bay, St. Lucia',
      'latitude': 13.9644,
      'longitude': -61.0242,
      'category': 'Bay',
      'description': 'Beautiful natural harbor',
    },
    {
      'name': 'The Pitons',
      'address': 'Soufrière, St. Lucia',
      'latitude': 13.8296,
      'longitude': -61.0743,
      'category': 'Landmark',
      'description': 'UNESCO World Heritage volcanic peaks',
    },
    {
      'name': 'Pigeon Island National Park',
      'address': 'Pigeon Island, Gros Islet, St. Lucia',
      'latitude': 14.0950,
      'longitude': -60.9522,
      'category': 'Park',
      'description': 'Historic national park and beach',
    },
    {
      'name': 'Anse Chastanet Beach',
      'address': 'Anse Chastanet, Soufrière, St. Lucia',
      'latitude': 13.8644,
      'longitude': -61.0786,
      'category': 'Beach',
      'description': 'Popular beach with snorkeling',
    },
    {
      'name': 'Sugar Beach',
      'address': 'Val des Pitons, Soufrière, St. Lucia',
      'latitude': 13.8401,
      'longitude': -61.0686,
      'category': 'Beach',
      'description': 'Luxury beach resort area',
    },
  ];

  /// Common landmarks and their coordinates
  static const Map<String, Map<String, dynamic>> _landmarks = {
    'airport': {
      'name': 'Hewanorra International Airport',
      'latitude': 13.7332,
      'longitude': -60.9526,
    },
    'castries': {
      'name': 'Castries City Center',
      'latitude': 14.0101,
      'longitude': -60.9897,
    },
    'rodney bay': {
      'name': 'Rodney Bay',
      'latitude': 14.0783,
      'longitude': -60.9564,
    },
    'soufriere': {
      'name': 'Soufrière',
      'latitude': 13.8566,
      'longitude': -61.0566,
    },
    'pitons': {
      'name': 'The Pitons',
      'latitude': 13.8296,
      'longitude': -61.0743,
    },
  };

  LocationFallbackServiceImpl({required LocationService locationService})
    : _locationService = locationService;

  @override
  Future<Either<AppError, List<LocationSuggestion>>> getFallbackSuggestions({
    String? query,
    int limit = 10,
  }) async {
    try {
      final List<LocationSuggestion> suggestions = [];

      // First, try to get cached recent locations
      final cachedRecent = await getCachedRecentLocations();
      cachedRecent.fold(
        (error) => debugPrint(
          'Failed to get cached recent locations: ${error.message}',
        ),
        (recentLocations) {
          // Convert recent locations to suggestions
          final recentSuggestions = recentLocations
              .take(limit ~/ 2) // Use half the limit for recent locations
              .map(
                (location) => LocationSuggestion(
                  id: location.id,
                  mainText: location.name,
                  secondaryText: 'Recent location',
                  fullAddress: location.address,
                  coordinates: location.coordinates,
                ),
              )
              .toList();
          suggestions.addAll(recentSuggestions);
        },
      );

      // Filter popular locations based on query if provided
      List<Map<String, dynamic>> filteredLocations = _popularLocations;
      if (query != null && query.isNotEmpty) {
        final lowercaseQuery = query.toLowerCase();
        filteredLocations = _popularLocations.where((location) {
          final name = (location['name'] as String).toLowerCase();
          final address = (location['address'] as String).toLowerCase();
          final category = (location['category'] as String).toLowerCase();
          return name.contains(lowercaseQuery) ||
              address.contains(lowercaseQuery) ||
              category.contains(lowercaseQuery);
        }).toList();
      }

      // Add popular locations to fill remaining slots
      final remainingSlots = limit - suggestions.length;
      final popularSuggestions = filteredLocations
          .take(remainingSlots)
          .map(
            (location) => LocationSuggestion(
              id: '${location['latitude']},${location['longitude']}',
              mainText: location['name'] as String,
              secondaryText: location['category'] as String,
              fullAddress: location['address'] as String,
              coordinates: LocationCoordinates(
                latitude: location['latitude'] as double,
                longitude: location['longitude'] as double,
              ),
            ),
          )
          .toList();

      suggestions.addAll(popularSuggestions);

      return Right(suggestions);
    } catch (e) {
      debugPrint('Error getting fallback suggestions: $e');
      return Left(
        AppError.unknown(
          message: 'Failed to get location suggestions',
          exception: e,
        ),
      );
    }
  }

  @override
  Either<AppError, LocationCoordinates> getDefaultLocation() {
    try {
      // Default to Castries city center as it's the capital
      const defaultLocation = LocationCoordinates(
        latitude: 14.0101,
        longitude: -60.9897,
        accuracy: 1000.0, // 1km accuracy for default location
        timestamp: null,
      );

      return Right(defaultLocation);
    } catch (e) {
      return Left(
        AppError.unknown(
          message: 'Failed to get default location',
          exception: e,
        ),
      );
    }
  }

  @override
  List<LocationSuggestion> getPopularDestinations({int limit = 5}) {
    return _popularLocations
        .take(limit)
        .map(
          (location) => LocationSuggestion(
            id: '${location['latitude']},${location['longitude']}',
            mainText: location['name'] as String,
            secondaryText: location['category'] as String,
            fullAddress: location['address'] as String,
            coordinates: LocationCoordinates(
              latitude: location['latitude'] as double,
              longitude: location['longitude'] as double,
            ),
          ),
        )
        .toList();
  }

  @override
  Future<Either<AppError, List<RecentLocation>>>
  getCachedRecentLocations() async {
    try {
      // Try to get recent locations from the main location service
      final result = await _locationService.getRecentLocations(limit: 10);
      return result;
    } catch (e) {
      debugPrint('Error getting cached recent locations: $e');
      return Left(
        AppError.unknown(
          message: 'Failed to get cached recent locations',
          exception: e,
        ),
      );
    }
  }

  @override
  List<LocationInputOption> getManualInputOptions() {
    return [
      LocationInputOption(
        type: LocationInputType.address,
        title: 'Enter Address',
        description: 'Type the full address in St. Lucia',
        icon: 'location_on',
        placeholder: 'e.g., Bay Street, Castries, St. Lucia',
      ),
      LocationInputOption(
        type: LocationInputType.landmark,
        title: 'Select Landmark',
        description: 'Choose from popular landmarks',
        icon: 'place',
        placeholder: 'e.g., Airport, Castries, Rodney Bay',
      ),
      LocationInputOption(
        type: LocationInputType.coordinates,
        title: 'Enter Coordinates',
        description: 'Provide latitude and longitude',
        icon: 'my_location',
        placeholder: 'e.g., 14.0101, -60.9897',
      ),
      LocationInputOption(
        type: LocationInputType.mapSelection,
        title: 'Select on Map',
        description: 'Tap on the map to select location',
        icon: 'map',
        placeholder: 'Tap anywhere on the St. Lucia map',
      ),
    ];
  }

  @override
  Either<AppError, LocationCoordinates> getLocationFromLandmark(
    String landmark,
  ) {
    try {
      final lowercaseLandmark = landmark.toLowerCase().trim();

      // Check if the landmark exists in our predefined list
      final landmarkData = _landmarks[lowercaseLandmark];
      if (landmarkData != null) {
        return Right(
          LocationCoordinates(
            latitude: landmarkData['latitude'] as double,
            longitude: landmarkData['longitude'] as double,
            accuracy: 100.0, // 100m accuracy for landmarks
            timestamp: DateTime.now(),
          ),
        );
      }

      // Try partial matching
      for (final entry in _landmarks.entries) {
        if (entry.key.contains(lowercaseLandmark) ||
            lowercaseLandmark.contains(entry.key)) {
          return Right(
            LocationCoordinates(
              latitude: entry.value['latitude'] as double,
              longitude: entry.value['longitude'] as double,
              accuracy: 100.0,
              timestamp: DateTime.now(),
            ),
          );
        }
      }

      // Check popular locations for landmark match
      for (final location in _popularLocations) {
        final name = (location['name'] as String).toLowerCase();
        if (name.contains(lowercaseLandmark) ||
            lowercaseLandmark.contains(name)) {
          return Right(
            LocationCoordinates(
              latitude: location['latitude'] as double,
              longitude: location['longitude'] as double,
              accuracy: 100.0,
              timestamp: DateTime.now(),
            ),
          );
        }
      }

      return Left(
        AppError.validation(
          message: 'Landmark not found',
          fieldErrors: {
            'landmark': 'Could not find coordinates for landmark: $landmark',
          },
        ),
      );
    } catch (e) {
      return Left(
        AppError.unknown(
          message: 'Failed to get location from landmark',
          exception: e,
        ),
      );
    }
  }

  @override
  Future<Either<AppError, LocationCoordinates>>
  getApproximateLocationFromIP() async {
    try {
      // This is a simplified implementation
      // In a real app, you would use an IP geolocation service
      // For now, we'll return the default location (Castries)

      debugPrint('IP-based location not implemented, using default location');
      return getDefaultLocation();
    } catch (e) {
      return Left(
        AppError.unknown(
          message: 'Failed to get approximate location',
          exception: e,
        ),
      );
    }
  }
}

/// Types of manual location input options
enum LocationInputType { address, landmark, coordinates, mapSelection }

/// Manual location input option
class LocationInputOption {
  final LocationInputType type;
  final String title;
  final String description;
  final String icon;
  final String placeholder;

  const LocationInputOption({
    required this.type,
    required this.title,
    required this.description,
    required this.icon,
    required this.placeholder,
  });
}

/// Error recovery strategies for location services
class LocationErrorRecovery {
  /// Get recovery options for a specific location error
  static List<LocationRecoveryOption> getRecoveryOptions(AppError error) {
    return error.when(
      network: (message, details) => [
        LocationRecoveryOption(
          type: LocationRecoveryType.useCache,
          title: 'Use Cached Locations',
          description: 'Use previously saved locations',
          action: 'Show cached locations',
        ),
        LocationRecoveryOption(
          type: LocationRecoveryType.manualEntry,
          title: 'Enter Manually',
          description: 'Type your location manually',
          action: 'Open manual entry',
        ),
      ],
      validation: (message, fieldErrors) {
        if (fieldErrors.containsKey('location') ||
            fieldErrors.containsKey('permission')) {
          return [
            LocationRecoveryOption(
              type: LocationRecoveryType.requestPermission,
              title: 'Grant Permission',
              description: 'Allow location access in settings',
              action: 'Open settings',
            ),
            LocationRecoveryOption(
              type: LocationRecoveryType.manualEntry,
              title: 'Enter Manually',
              description: 'Type your location manually',
              action: 'Open manual entry',
            ),
          ];
        }
        return [
          LocationRecoveryOption(
            type: LocationRecoveryType.manualEntry,
            title: 'Enter Manually',
            description: 'Type your location manually',
            action: 'Open manual entry',
          ),
        ];
      },
      authentication: (message, errorCode) => [],
      server: (message, statusCode) => [
        LocationRecoveryOption(
          type: LocationRecoveryType.useCache,
          title: 'Use Cached Locations',
          description: 'Use previously saved locations',
          action: 'Show cached locations',
        ),
        LocationRecoveryOption(
          type: LocationRecoveryType.retry,
          title: 'Try Again',
          description: 'Retry location request',
          action: 'Retry',
        ),
      ],
      unknown: (message, exception) => [
        LocationRecoveryOption(
          type: LocationRecoveryType.useDefault,
          title: 'Use Default Location',
          description: 'Start from Castries city center',
          action: 'Use Castries',
        ),
        LocationRecoveryOption(
          type: LocationRecoveryType.manualEntry,
          title: 'Enter Manually',
          description: 'Type your location manually',
          action: 'Open manual entry',
        ),
      ],
    );
  }
}

/// Types of location error recovery
enum LocationRecoveryType {
  retry,
  useCache,
  useDefault,
  manualEntry,
  requestPermission,
}

/// Location error recovery option
class LocationRecoveryOption {
  final LocationRecoveryType type;
  final String title;
  final String description;
  final String action;

  const LocationRecoveryOption({
    required this.type,
    required this.title,
    required this.description,
    required this.action,
  });
}
