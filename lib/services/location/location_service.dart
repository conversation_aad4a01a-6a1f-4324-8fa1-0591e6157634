import 'dart:async';
import 'package:dartz/dartz.dart';
import '../../core/errors/app_error.dart';
import '../../domain/entities/location_models.dart';
import '../../domain/entities/ride.dart';

/// Abstract interface for location-related operations
abstract class LocationService {
  /// Initialize the location service and request permissions if needed
  Future<Either<AppError, bool>> initialize();

  /// Check if location permissions are granted
  Future<Either<AppError, bool>> checkLocationPermission();

  /// Request location permissions from the user
  Future<Either<AppError, bool>> requestLocationPermission();

  /// Get the current device location
  Future<Either<AppError, LocationCoordinates>> getCurrentLocation({
    LocationAccuracy accuracy = LocationAccuracy.high,
  });

  /// Get a stream of location updates
  Stream<Either<AppError, LocationCoordinates>> getLocationStream({
    LocationAccuracy accuracy = LocationAccuracy.high,
    double distanceFilter = 10.0, // in meters
  });

  /// Convert address to coordinates (geocoding)
  Future<Either<AppError, LocationCoordinates>> getCoordinatesFromAddress(
    String address,
  );

  /// Convert coordinates to address (reverse geocoding)
  Future<Either<AppError, String>> getAddressFromCoordinates(
    LocationCoordinates coordinates,
  );

  /// Get location suggestions based on query text
  Future<Either<AppError, List<LocationSuggestion>>> getLocationSuggestions(
    String query, {
    int limit = 5,
  });

  /// Store a recent location
  Future<Either<AppError, void>> storeRecentLocation(RecentLocation location);

  /// Get recent locations
  Future<Either<AppError, List<RecentLocation>>> getRecentLocations({
    RecentLocationType? type,
    int limit = 5,
  });

  /// Clear recent locations
  Future<Either<AppError, void>> clearRecentLocations();

  /// Convert RideLocation to LocationCoordinates
  LocationCoordinates rideLocationToCoordinates(RideLocation location);

  /// Convert LocationCoordinates to RideLocation
  Future<Either<AppError, RideLocation>> coordinatesToRideLocation(
    LocationCoordinates coordinates, {
    String? name,
    String? instructions,
  });

  /// Validate if location is within service area (St. Lucia)
  bool isLocationInServiceArea(LocationCoordinates coordinates);

  /// Dispose resources
  void dispose();
}
