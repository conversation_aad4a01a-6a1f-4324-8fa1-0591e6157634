import 'dart:async';
import 'dart:io';
// import 'dart:math' as math; // Unused import
import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart' show debugPrint;
import 'package:geolocator/geolocator.dart' as geo;
import 'package:geocoding/geocoding.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../core/errors/app_error.dart';
import '../../data/datasources/storage_service.dart';
import '../../domain/entities/location_models.dart';
import '../../domain/entities/ride.dart';
import '../../core/constants/app_constants.dart';
import 'location_service.dart';

/// Implementation of LocationService using Geolocator and Geocoding packages
class LocationServiceImpl implements LocationService {
  final StorageService _storageService;

  /// Stream controller for location updates
  final StreamController<Either<AppError, LocationCoordinates>>
  _locationController =
      StreamController<Either<AppError, LocationCoordinates>>.broadcast();

  /// Subscription to the location stream from Geolocator
  StreamSubscription<geo.Position>? _positionStreamSubscription;

  /// List of popular St. Lucia locations for suggestions
  final List<Map<String, dynamic>> _stLuciaPopularLocations = [
    {
      'name': 'Rodney Bay',
      'address': 'Rodney Bay, Gros Islet, St. Lucia',
      'latitude': 14.0783,
      'longitude': -60.9564,
    },
    {
      'name': 'Castries City Center',
      'address': 'Castries, St. Lucia',
      'latitude': 14.0101,
      'longitude': -60.9897,
    },
    {
      'name': 'Soufrière',
      'address': 'Soufrière, St. Lucia',
      'latitude': 13.8566,
      'longitude': -61.0566,
    },
    {
      'name': 'Pigeon Island National Park',
      'address': 'Pigeon Island, Gros Islet, St. Lucia',
      'latitude': 14.0950,
      'longitude': -60.9522,
    },
    {
      'name': 'Hewanorra International Airport',
      'address': 'Vieux Fort, St. Lucia',
      'latitude': 13.7332,
      'longitude': -60.9526,
    },
    {
      'name': 'Marigot Bay',
      'address': 'Marigot Bay, St. Lucia',
      'latitude': 13.9644,
      'longitude': -61.0242,
    },
    {
      'name': 'The Pitons',
      'address': 'Soufrière, St. Lucia',
      'latitude': 13.8296,
      'longitude': -61.0743,
    },
    {
      'name': 'Anse Chastanet Beach',
      'address': 'Anse Chastanet, Soufrière, St. Lucia',
      'latitude': 13.8644,
      'longitude': -61.0786,
    },
    {
      'name': 'Sugar Beach',
      'address': 'Val des Pitons, Soufrière, St. Lucia',
      'latitude': 13.8401,
      'longitude': -61.0686,
    },
    {
      'name': 'George F.L. Charles Airport',
      'address': 'Castries, St. Lucia',
      'latitude': 14.0196,
      'longitude': -60.9930,
    },
  ];

  /// Creates a new LocationServiceImpl
  LocationServiceImpl({required StorageService storageService})
    : _storageService = storageService;

  @override
  Future<Either<AppError, bool>> initialize() async {
    try {
      // Check if location services are enabled
      final bool serviceEnabled =
          await geo.Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        return const Left(
          AppError.validation(
            message: 'Location services are disabled',
            fieldErrors: {
              'location':
                  'Please enable location services in your device settings',
            },
          ),
        );
      }

      // Check location permission
      final permissionResult = await checkLocationPermission();

      return permissionResult;
    } catch (e) {
      return Left(
        AppError.unknown(
          message: 'Failed to initialize location service',
          exception: e,
        ),
      );
    }
  }

  @override
  Future<Either<AppError, bool>> checkLocationPermission() async {
    try {
      // Check location permission using permission_handler for more control
      PermissionStatus status;

      if (Platform.isIOS) {
        status = await Permission.locationWhenInUse.status;
      } else if (Platform.isAndroid) {
        status = await Permission.location.status;
      } else {
        // For web or other platforms, use Geolocator's permission check
        final geo.LocationPermission permission =
            await geo.Geolocator.checkPermission();
        return Right(
          permission == geo.LocationPermission.always ||
              permission == geo.LocationPermission.whileInUse,
        );
      }

      return Right(status.isGranted);
    } catch (e) {
      return Left(
        AppError.unknown(
          message: 'Failed to check location permission',
          exception: e,
        ),
      );
    }
  }

  @override
  Future<Either<AppError, bool>> requestLocationPermission() async {
    try {
      PermissionStatus status;

      if (Platform.isIOS) {
        status = await Permission.locationWhenInUse.request();
      } else if (Platform.isAndroid) {
        status = await Permission.location.request();
      } else {
        // For web or other platforms, use Geolocator's request permission
        final geo.LocationPermission permission =
            await geo.Geolocator.requestPermission();
        return Right(
          permission == geo.LocationPermission.always ||
              permission == geo.LocationPermission.whileInUse,
        );
      }

      return Right(status.isGranted);
    } catch (e) {
      return Left(
        AppError.unknown(
          message: 'Failed to request location permission',
          exception: e,
        ),
      );
    }
  }

  @override
  Future<Either<AppError, LocationCoordinates>> getCurrentLocation({
    LocationAccuracy accuracy = LocationAccuracy.high,
  }) async {
    try {
      // Check if location services are enabled
      final bool serviceEnabled =
          await geo.Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        return const Left(
          AppError.validation(
            message: 'Location services are disabled',
            fieldErrors: {
              'location':
                  'Please enable location services in your device settings',
            },
          ),
        );
      }

      // Check if we have permission
      final permissionResult = await checkLocationPermission();
      final hasPermission = permissionResult.fold(
        (error) => false,
        (hasPermission) => hasPermission,
      );

      if (!hasPermission) {
        // Request permission if not granted
        final requestResult = await requestLocationPermission();
        final permissionGranted = requestResult.fold(
          (error) => false,
          (granted) => granted,
        );

        if (!permissionGranted) {
          return const Left(
            AppError.validation(
              message: 'Location permission denied',
              fieldErrors: {
                'location':
                    'Please grant location permission to use this feature',
              },
            ),
          );
        }
      }

      // Convert our accuracy enum to Geolocator's accuracy
      final geoAccuracy = _mapAccuracy(accuracy);

      // Get current position
      final geo.Position position = await geo.Geolocator.getCurrentPosition(
        desiredAccuracy: geoAccuracy,
        timeLimit: const Duration(seconds: 10),
      );

      // Convert to our model
      final coordinates = LocationCoordinates(
        latitude: position.latitude,
        longitude: position.longitude,
        accuracy: position.accuracy,
        timestamp: DateTime.fromMillisecondsSinceEpoch(
          position.timestamp.millisecondsSinceEpoch,
        ),
      );

      return Right(coordinates);
    } catch (e) {
      if (e is TimeoutException) {
        return const Left(
          AppError.validation(
            message: 'Location request timed out',
            fieldErrors: {
              'location': 'Could not get your location. Please try again',
            },
          ),
        );
      } else if (e is geo.LocationServiceDisabledException) {
        return const Left(
          AppError.validation(
            message: 'Location services are disabled',
            fieldErrors: {
              'location':
                  'Please enable location services in your device settings',
            },
          ),
        );
      } else if (e is geo.PermissionDeniedException) {
        return const Left(
          AppError.validation(
            message: 'Location permission denied',
            fieldErrors: {
              'location':
                  'Please grant location permission to use this feature',
            },
          ),
        );
      } else {
        return Left(
          AppError.unknown(
            message: 'Failed to get current location',
            exception: e,
          ),
        );
      }
    }
  }

  @override
  Stream<Either<AppError, LocationCoordinates>> getLocationStream({
    LocationAccuracy accuracy = LocationAccuracy.high,
    double distanceFilter = 10.0,
  }) {
    // Cancel any existing subscription
    _positionStreamSubscription?.cancel();

    // Convert our accuracy enum to Geolocator's accuracy
    final geoAccuracy = _mapAccuracy(accuracy);

    // Start listening to location updates
    _positionStreamSubscription =
        geo.Geolocator.getPositionStream(
          locationSettings: geo.LocationSettings(
            accuracy: geoAccuracy,
            distanceFilter: distanceFilter.toInt(),
          ),
        ).listen(
          (geo.Position position) {
            // Convert to our model
            final coordinates = LocationCoordinates(
              latitude: position.latitude,
              longitude: position.longitude,
              accuracy: position.accuracy,
              timestamp: DateTime.fromMillisecondsSinceEpoch(
                position.timestamp.millisecondsSinceEpoch,
              ),
            );

            // Add to stream
            _locationController.add(Right(coordinates));
          },
          onError: (error) {
            _locationController.add(
              Left(
                AppError.unknown(
                  message: 'Location stream error',
                  exception: error,
                ),
              ),
            );
          },
        );

    return _locationController.stream;
  }

  @override
  Future<Either<AppError, LocationCoordinates>> getCoordinatesFromAddress(
    String address,
  ) async {
    try {
      // First check if this is a known popular location by name
      final popularLocation = _findPopularLocationByName(address);
      if (popularLocation != null) {
        final coordinates = LocationCoordinates(
          latitude: popularLocation['latitude'] as double,
          longitude: popularLocation['longitude'] as double,
        );

        // Store as a recent location for future use
        final recentLocation = RecentLocation(
          id: '${coordinates.latitude},${coordinates.longitude}',
          name: popularLocation['name'] as String,
          address: popularLocation['address'] as String,
          coordinates: coordinates,
          lastUsed: DateTime.now(),
          type: RecentLocationType.both, // Default to both until specified
        );

        // Store asynchronously without waiting for result
        storeRecentLocation(recentLocation);

        return Right(coordinates);
      }

      // Add St. Lucia to the address if it's not already included
      final String enhancedAddress =
          address.toLowerCase().contains('st. lucia') ||
              address.toLowerCase().contains('saint lucia')
          ? address
          : '$address, St. Lucia';

      // Use geocoding package to get coordinates from address
      final List<Location> locations = await locationFromAddress(
        enhancedAddress,
        localeIdentifier: 'en_LC', // St. Lucia locale
      );

      if (locations.isEmpty) {
        // Try again without locale identifier
        try {
          final List<Location> fallbackLocations = await locationFromAddress(
            enhancedAddress,
          );

          if (fallbackLocations.isNotEmpty) {
            locations.addAll(fallbackLocations);
          }
        } catch (e) {
          debugPrint('Fallback geocoding error: $e');
        }

        if (locations.isEmpty) {
          return const Left(
            AppError.validation(
              message: 'Address not found',
              fieldErrors: {
                'address':
                    'Could not find coordinates for this address in St. Lucia',
              },
            ),
          );
        }
      }

      // Filter locations to only include those within St. Lucia
      final stLuciaLocations = locations.where((location) {
        final coordinates = LocationCoordinates(
          latitude: location.latitude,
          longitude: location.longitude,
        );
        return coordinates.isValidStLuciaLocation();
      }).toList();

      if (stLuciaLocations.isEmpty) {
        return const Left(
          AppError.validation(
            message: 'Location outside service area',
            fieldErrors: {
              'address': 'This address appears to be outside of St. Lucia',
            },
          ),
        );
      }

      final location = stLuciaLocations.first;
      final coordinates = LocationCoordinates(
        latitude: location.latitude,
        longitude: location.longitude,
        accuracy: 0.0, // Default accuracy since geocoding doesn't provide it
        timestamp: DateTime.now(),
      );

      // Get a formatted address for this location
      final addressResult = await getAddressFromCoordinates(coordinates);
      final formattedAddress = addressResult.fold(
        (error) => address,
        (addr) => addr,
      );

      // Extract a meaningful name from the address
      String locationName = address.split(',').first.trim();
      if (locationName.isEmpty || locationName.length > 30) {
        // If name is empty or too long, use first part of formatted address
        locationName = formattedAddress.split(',').first.trim();
      }

      final recentLocation = RecentLocation(
        id: '${coordinates.latitude},${coordinates.longitude}',
        name: locationName,
        address: formattedAddress,
        coordinates: coordinates,
        lastUsed: DateTime.now(),
        type: RecentLocationType.both, // Default to both until specified
      );

      // Store asynchronously without waiting for result
      storeRecentLocation(recentLocation);

      return Right(coordinates);
    } catch (e) {
      debugPrint('Geocoding error: $e');
      return Left(
        AppError.unknown(
          message: 'Failed to get coordinates from address',
          exception: e,
        ),
      );
    }
  }

  @override
  Future<Either<AppError, String>> getAddressFromCoordinates(
    LocationCoordinates coordinates,
  ) async {
    try {
      // First check if this is a known popular location
      final popularLocation = _findPopularLocationByCoordinates(coordinates);
      if (popularLocation != null) {
        return Right(popularLocation['address'] as String);
      }

      // Validate that the coordinates are within St. Lucia
      if (!coordinates.isValidStLuciaLocation()) {
        return const Left(
          AppError.validation(
            message: 'Location outside service area',
            fieldErrors: {
              'coordinates': 'These coordinates are outside of St. Lucia',
            },
          ),
        );
      }

      // Use geocoding package to get address from coordinates
      final List<Placemark> placemarks = await placemarkFromCoordinates(
        coordinates.latitude,
        coordinates.longitude,
        localeIdentifier: 'en_LC', // St. Lucia locale
      );

      // If no results with St. Lucia locale, try without locale
      if (placemarks.isEmpty) {
        try {
          final List<Placemark> fallbackPlacemarks =
              await placemarkFromCoordinates(
                coordinates.latitude,
                coordinates.longitude,
              );

          if (fallbackPlacemarks.isNotEmpty) {
            return Right(_formatStLuciaAddress(fallbackPlacemarks.first));
          }
        } catch (e) {
          debugPrint('Fallback reverse geocoding error: $e');
        }

        return const Left(
          AppError.validation(
            message: 'Location not found',
            fieldErrors: {
              'coordinates': 'Could not find address for these coordinates',
            },
          ),
        );
      }

      final placemark = placemarks.first;
      return Right(_formatStLuciaAddress(placemark));
    } catch (e) {
      debugPrint('Reverse geocoding error: $e');
      return Left(
        AppError.unknown(
          message: 'Failed to get address from coordinates',
          exception: e,
        ),
      );
    }
  }

  /// Format address with St. Lucia context
  String _formatStLuciaAddress(Placemark placemark) {
    // Format the address
    final String address = _formatAddress(placemark);

    // Verify that the address contains St. Lucia
    final bool isStLuciaAddress =
        address.toLowerCase().contains('st. lucia') ||
        address.toLowerCase().contains('saint lucia') ||
        placemark.isoCountryCode == AppConstants.stLuciaCountryCode;

    if (!isStLuciaAddress) {
      // If the address doesn't mention St. Lucia, append it
      return '$address, St. Lucia';
    }

    return address;
  }

  /// Find a popular location by coordinates
  Map<String, dynamic>? _findPopularLocationByCoordinates(
    LocationCoordinates coordinates,
  ) {
    const double maxDistanceInDegrees = 0.001;

    final match = _stLuciaPopularLocations.firstWhere((location) {
      final lat = location['latitude'] as double;
      final lon = location['longitude'] as double;
      return (lat - coordinates.latitude).abs() < maxDistanceInDegrees &&
          (lon - coordinates.longitude).abs() < maxDistanceInDegrees;
    }, orElse: () => <String, dynamic>{});

    return match.isNotEmpty ? match : null;
  }

  @override
  Future<Either<AppError, List<LocationSuggestion>>> getLocationSuggestions(
    String query, {
    int limit = 5,
  }) async {
    try {
      if (query.isEmpty) {
        // When query is empty, return a mix of recent and popular locations
        return Right(await _getMixedSuggestions(limit));
      }

      final List<LocationSuggestion> suggestions = [];

      // First check recent locations for matches
      final recentLocationsResult = await getRecentLocations(limit: limit);
      final recentLocations = recentLocationsResult.fold(
        (error) => <RecentLocation>[],
        (locations) => locations,
      );

      // Filter recent locations that match the query
      final matchingRecentLocations = recentLocations
          .where((location) {
            final lowercaseQuery = query.toLowerCase();
            return location.name.toLowerCase().contains(lowercaseQuery) ||
                location.address.toLowerCase().contains(lowercaseQuery);
          })
          .take(limit ~/ 2); // Take half of the limit for recent locations

      // Convert matching recent locations to suggestions
      for (final location in matchingRecentLocations) {
        final suggestion = LocationSuggestion(
          id: location.id,
          mainText: location.name,
          secondaryText: 'Recent location',
          fullAddress: location.address,
          coordinates: location.coordinates,
        );
        suggestions.add(suggestion);
      }

      // Then check popular locations for matches
      final popularSuggestions = _getMatchingPopularLocations(
        query,
        limit - suggestions.length,
      );
      suggestions.addAll(popularSuggestions);

      // If we don't have enough suggestions from popular and recent locations, try geocoding
      if (suggestions.length < limit) {
        try {
          // Add "St. Lucia" to the query to focus on St. Lucia locations
          final String enhancedQuery = '$query, St. Lucia';

          // Try with locale identifier first
          List<Location> locations = [];
          try {
            locations = await locationFromAddress(
              enhancedQuery,
              localeIdentifier: 'en_LC', // St. Lucia locale
            );
          } catch (e) {
            debugPrint('Geocoding with locale error: $e');
          }

          // If no results, try without locale identifier
          if (locations.isEmpty) {
            try {
              locations = await locationFromAddress(enhancedQuery);
            } catch (e) {
              debugPrint('Geocoding without locale error: $e');
            }
          }

          // Filter locations to only include those within St. Lucia
          final stLuciaLocations = locations.where((location) {
            final coordinates = LocationCoordinates(
              latitude: location.latitude,
              longitude: location.longitude,
            );
            return coordinates.isValidStLuciaLocation();
          }).toList();

          // Process each location
          for (final location in stLuciaLocations) {
            if (suggestions.length >= limit) break;

            // Get address details for each location
            final List<Placemark> placemarks = await placemarkFromCoordinates(
              location.latitude,
              location.longitude,
            );

            if (placemarks.isNotEmpty) {
              final placemark = placemarks.first;
              final String address = _formatStLuciaAddress(placemark);

              // Check if this location is already in our suggestions
              final bool isDuplicate = suggestions.any(
                (s) =>
                    s.coordinates != null &&
                    (s.coordinates!.latitude - location.latitude).abs() <
                        0.001 &&
                    (s.coordinates!.longitude - location.longitude).abs() <
                        0.001,
              );

              if (!isDuplicate) {
                // Create a suggestion
                final suggestion = LocationSuggestion(
                  id: '${location.latitude},${location.longitude}',
                  mainText: _extractMainText(placemark, query),
                  secondaryText: _formatSecondaryText(placemark),
                  fullAddress: address,
                  coordinates: LocationCoordinates(
                    latitude: location.latitude,
                    longitude: location.longitude,
                  ),
                );

                suggestions.add(suggestion);
              }
            }
          }
        } catch (geocodingError) {
          debugPrint('Geocoding error: $geocodingError');
          // Continue with what we have if geocoding fails
        }
      }

      // If we still don't have enough suggestions, add more popular locations
      if (suggestions.length < limit) {
        final remainingPopular = _getPopularLocationSuggestions(
          limit - suggestions.length,
        );

        // Add only those that aren't already in the suggestions
        for (final popular in remainingPopular) {
          final bool isDuplicate = suggestions.any((s) => s.id == popular.id);
          if (!isDuplicate) {
            suggestions.add(popular);
            if (suggestions.length >= limit) break;
          }
        }
      }

      return Right(suggestions);
    } catch (e) {
      debugPrint('Error getting location suggestions: $e');
      // Return popular locations on error to avoid breaking the UI
      return Right(await _getMixedSuggestions(limit));
    }
  }

  /// Get a mix of recent and popular locations
  Future<List<LocationSuggestion>> _getMixedSuggestions(int limit) async {
    final List<LocationSuggestion> suggestions = [];

    // First get recent locations (up to half of the limit)
    final recentLocationsResult = await getRecentLocations(limit: limit ~/ 2);
    final recentLocations = recentLocationsResult.fold(
      (error) => <RecentLocation>[],
      (locations) => locations,
    );

    // Convert recent locations to suggestions
    for (final location in recentLocations) {
      final suggestion = LocationSuggestion(
        id: location.id,
        mainText: location.name,
        secondaryText: 'Recent location',
        fullAddress: location.address,
        coordinates: location.coordinates,
      );
      suggestions.add(suggestion);
    }

    // Fill the rest with popular locations
    final popularSuggestions = _getPopularLocationSuggestions(
      limit - suggestions.length,
    );

    // Add only those that aren't already in the suggestions
    for (final popular in popularSuggestions) {
      final bool isDuplicate = suggestions.any((s) => s.id == popular.id);
      if (!isDuplicate) {
        suggestions.add(popular);
        if (suggestions.length >= limit) break;
      }
    }

    return suggestions;
  }

  /// Get suggestions from popular St. Lucia locations
  List<LocationSuggestion> _getPopularLocationSuggestions(int limit) {
    final suggestions = _stLuciaPopularLocations.take(limit).map((location) {
      return LocationSuggestion(
        id: '${location['latitude']},${location['longitude']}',
        mainText: location['name'] as String,
        secondaryText: (location['address'] as String)
            .replaceAll(location['name'] as String, '')
            .trim(),
        fullAddress: location['address'] as String,
        coordinates: LocationCoordinates(
          latitude: location['latitude'] as double,
          longitude: location['longitude'] as double,
        ),
      );
    }).toList();

    return suggestions;
  }

  /// Get matching popular locations based on query
  List<LocationSuggestion> _getMatchingPopularLocations(
    String query,
    int limit,
  ) {
    final lowercaseQuery = query.toLowerCase();

    // Filter popular locations that match the query
    final matchingLocations = _stLuciaPopularLocations
        .where((location) {
          final name = (location['name'] as String).toLowerCase();
          final address = (location['address'] as String).toLowerCase();
          return name.contains(lowercaseQuery) ||
              address.contains(lowercaseQuery);
        })
        .take(limit)
        .map((location) {
          return LocationSuggestion(
            id: '${location['latitude']},${location['longitude']}',
            mainText: location['name'] as String,
            secondaryText: (location['address'] as String)
                .replaceAll(location['name'] as String, '')
                .trim(),
            fullAddress: location['address'] as String,
            coordinates: LocationCoordinates(
              latitude: location['latitude'] as double,
              longitude: location['longitude'] as double,
            ),
          );
        })
        .toList();

    return matchingLocations;
  }

  /// Find a popular location by name
  Map<String, dynamic>? _findPopularLocationByName(String query) {
    final lowercaseQuery = query.toLowerCase();

    return _stLuciaPopularLocations
            .firstWhere(
              (location) => (location['name'] as String).toLowerCase().contains(
                lowercaseQuery,
              ),
              orElse: () => <String, dynamic>{},
            )
            .isNotEmpty
        ? _stLuciaPopularLocations.firstWhere(
            (location) => (location['name'] as String).toLowerCase().contains(
              lowercaseQuery,
            ),
          )
        : null;
  }

  /// Extract main text from placemark
  String _extractMainText(Placemark placemark, String query) {
    // Try to use the most specific part of the address as the main text
    if (placemark.name != null && placemark.name!.isNotEmpty) {
      return placemark.name!;
    } else if (placemark.street != null && placemark.street!.isNotEmpty) {
      return placemark.street!;
    } else if (placemark.subLocality != null &&
        placemark.subLocality!.isNotEmpty) {
      return placemark.subLocality!;
    } else if (placemark.locality != null && placemark.locality!.isNotEmpty) {
      return placemark.locality!;
    } else {
      return query;
    }
  }

  /// Format secondary text from placemark
  String _formatSecondaryText(Placemark placemark) {
    final List<String?> parts = [
      placemark.locality,
      placemark.administrativeArea,
      placemark.country,
    ];

    final List<String> addressParts = parts
        .where((part) => part != null && part.isNotEmpty)
        .map((part) => part!)
        .toList();

    return addressParts.join(', ');
  }

  @override
  Future<Either<AppError, void>> storeRecentLocation(
    RecentLocation location,
  ) async {
    try {
      // Get existing recent locations
      final recentLocationsResult = await getRecentLocations(
        limit: AppConstants.maxRecentLocations,
      );

      final List<RecentLocation> recentLocations = recentLocationsResult.fold(
        (error) => <RecentLocation>[],
        (locations) => locations,
      );

      // Check if this location already exists (using approximate coordinate matching)
      final existingIndex = recentLocations.indexWhere(
        (loc) => _areCoordinatesEqual(loc.coordinates, location.coordinates),
      );

      if (existingIndex != -1) {
        // Update existing location with new timestamp and possibly new type
        final existingLocation = recentLocations[existingIndex];

        // If the existing location has a different type, update to "both"
        RecentLocationType updatedType = location.type;
        if (existingLocation.type != location.type &&
            existingLocation.type != RecentLocationType.both) {
          updatedType = RecentLocationType.both;
        }

        recentLocations[existingIndex] = RecentLocation(
          id: existingLocation.id,
          name: location.name, // Use the new name if provided
          address: location.address, // Use the new address if provided
          coordinates: location.coordinates,
          lastUsed: DateTime.now(), // Update timestamp
          type: updatedType,
        );
      } else {
        // Add new location
        recentLocations.add(location);
      }

      // Sort by last used (most recent first)
      recentLocations.sort((a, b) => b.lastUsed.compareTo(a.lastUsed));

      // Limit to max number of locations
      final limitedLocations = recentLocations
          .take(AppConstants.maxRecentLocations)
          .toList();

      // Convert to JSON
      final List<Map<String, dynamic>> locationsJson = limitedLocations
          .map((loc) => loc.toJson())
          .toList();

      // Store in secure storage
      await _storageService.storeCachedData(AppConstants.recentLocationsKey, {
        'locations': locationsJson,
      });

      return const Right(null);
    } catch (e) {
      debugPrint('Failed to store recent location: $e');
      return Left(
        AppError.unknown(
          message: 'Failed to store recent location',
          exception: e,
        ),
      );
    }
  }

  /// Check if two sets of coordinates are approximately equal
  bool _areCoordinatesEqual(LocationCoordinates a, LocationCoordinates b) {
    const double tolerance = 0.0001; // About 11 meters at the equator
    return (a.latitude - b.latitude).abs() < tolerance &&
        (a.longitude - b.longitude).abs() < tolerance;
  }

  @override
  Future<Either<AppError, List<RecentLocation>>> getRecentLocations({
    RecentLocationType? type,
    int limit = 5,
  }) async {
    try {
      // Get from secure storage
      final cachedData = await _storageService.getCachedData(
        AppConstants.recentLocationsKey,
      );

      if (cachedData == null) {
        // If no cached data, return empty list
        return const Right([]);
      }

      // Check if the cached data has the expected format
      if (!cachedData.containsKey('locations') ||
          cachedData['locations'] is! List<dynamic>) {
        debugPrint('Invalid cached locations data format');
        return const Right([]);
      }

      final List<dynamic> locationsJson =
          cachedData['locations'] as List<dynamic>;

      // Convert to RecentLocation objects
      final List<RecentLocation> locations = [];
      for (final json in locationsJson) {
        try {
          if (json is Map<String, dynamic>) {
            final location = RecentLocation.fromJson(json);

            // Validate that the location is within St. Lucia
            if (location.coordinates.isValidStLuciaLocation()) {
              locations.add(location);
            }
          }
        } catch (e) {
          debugPrint('Error parsing location: $e');
          // Skip invalid locations
        }
      }

      // Filter by type if specified
      final List<RecentLocation> filteredLocations = type != null
          ? locations
                .where(
                  (loc) =>
                      loc.type == type || loc.type == RecentLocationType.both,
                )
                .toList()
          : locations;

      // Sort by last used (most recent first)
      filteredLocations.sort((a, b) => b.lastUsed.compareTo(a.lastUsed));

      // Limit the number of results
      final limitedLocations = filteredLocations.take(limit).toList();

      return Right(limitedLocations);
    } catch (e) {
      debugPrint('Error getting recent locations: $e');
      // Return empty list on error to avoid breaking the UI
      return const Right([]);
    }
  }

  @override
  Future<Either<AppError, void>> clearRecentLocations() async {
    try {
      // Clear from secure storage
      await _storageService.clearCachedData(AppConstants.recentLocationsKey);

      return const Right(null);
    } catch (e) {
      return Left(
        AppError.unknown(
          message: 'Failed to clear recent locations',
          exception: e,
        ),
      );
    }
  }

  @override
  LocationCoordinates rideLocationToCoordinates(RideLocation location) {
    return LocationCoordinates(
      latitude: location.latitude,
      longitude: location.longitude,
    );
  }

  @override
  Future<Either<AppError, RideLocation>> coordinatesToRideLocation(
    LocationCoordinates coordinates, {
    String? name,
    String? instructions,
  }) async {
    try {
      // Get address from coordinates
      final addressResult = await getAddressFromCoordinates(coordinates);

      final String address = addressResult.fold(
        (error) => '',
        (address) => address,
      );

      // Create RideLocation
      final rideLocation = RideLocation(
        name: name ?? address.split(',').first,
        address: address,
        latitude: coordinates.latitude,
        longitude: coordinates.longitude,
        instructions: instructions,
      );

      return Right(rideLocation);
    } catch (e) {
      return Left(
        AppError.unknown(
          message: 'Failed to convert coordinates to ride location',
          exception: e,
        ),
      );
    }
  }

  @override
  bool isLocationInServiceArea(LocationCoordinates coordinates) {
    return coordinates.isValidStLuciaLocation();
  }

  @override
  void dispose() {
    _positionStreamSubscription?.cancel();
    _locationController.close();
  }

  // Helper methods

  /// Map our accuracy enum to Geolocator's accuracy
  geo.LocationAccuracy _mapAccuracy(LocationAccuracy accuracy) {
    switch (accuracy) {
      case LocationAccuracy.lowest:
        return geo.LocationAccuracy.lowest;
      case LocationAccuracy.low:
        return geo.LocationAccuracy.low;
      case LocationAccuracy.medium:
        return geo.LocationAccuracy.medium;
      case LocationAccuracy.high:
        return geo.LocationAccuracy.high;
      case LocationAccuracy.highest:
        return geo.LocationAccuracy.best;
    }
  }

  /// Format address from placemark
  String _formatAddress(Placemark placemark) {
    final List<String?> parts = [
      placemark.name,
      placemark.street,
      placemark.subLocality,
      placemark.locality,
      placemark.administrativeArea,
      placemark.country,
    ];

    final List<String> addressParts = parts
        .where((part) => part != null && part.isNotEmpty)
        .map((part) => part!)
        .toList();

    return addressParts.join(', ');
  }
}
