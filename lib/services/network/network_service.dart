import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart' show debugPrint;

/// Abstract interface for network connectivity operations
abstract class NetworkService {
  /// Get current connectivity status
  Future<ConnectivityResult> getCurrentConnectivity();

  /// Stream of connectivity changes
  Stream<ConnectivityResult> get connectivityStream;

  /// Check if device is currently online
  Future<bool> isOnline();

  /// Check if device is currently offline
  Future<bool> isOffline();

  /// Initialize network monitoring
  Future<void> initialize();

  /// Dispose resources
  void dispose();
}

/// Implementation of NetworkService using connectivity_plus
class NetworkServiceImpl implements NetworkService {
  final Connectivity _connectivity;
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;
  final StreamController<ConnectivityResult> _connectivityController =
      StreamController<ConnectivityResult>.broadcast();

  ConnectivityResult _currentConnectivity = ConnectivityResult.none;

  NetworkServiceImpl({Connectivity? connectivity})
    : _connectivity = connectivity ?? Connectivity();

  @override
  Future<ConnectivityResult> getCurrentConnectivity() async {
    try {
      final ConnectivityResult result = await _connectivity.checkConnectivity();
      // Store the connectivity result
      _currentConnectivity = result;
      return _currentConnectivity;
    } catch (e) {
      // If connectivity check fails, assume offline
      _currentConnectivity = ConnectivityResult.none;
      return ConnectivityResult.none;
    }
  }

  @override
  Stream<ConnectivityResult> get connectivityStream =>
      _connectivityController.stream;

  @override
  Future<bool> isOnline() async {
    final connectivity = await getCurrentConnectivity();
    return connectivity != ConnectivityResult.none;
  }

  @override
  Future<bool> isOffline() async {
    return !(await isOnline());
  }

  @override
  Future<void> initialize() async {
    // Get initial connectivity status
    await getCurrentConnectivity();

    // Listen for connectivity changes
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      (ConnectivityResult connectivity) {
        // Store the connectivity result
        _currentConnectivity = connectivity;
        _connectivityController.add(connectivity);
      },
      onError: (error) {
        // Handle connectivity stream errors
        debugPrint('Connectivity stream error: $error');
        _currentConnectivity = ConnectivityResult.none;
        _connectivityController.add(ConnectivityResult.none);
      },
    );
  }
  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    _connectivityController.close();
  }

  /// Get a human-readable description of the connectivity status
  String getConnectivityDescription(ConnectivityResult connectivity) {
    switch (connectivity) {
      case ConnectivityResult.wifi:
        return 'Connected via WiFi';
      case ConnectivityResult.mobile:
        return 'Connected via Mobile Data';
      case ConnectivityResult.ethernet:
        return 'Connected via Ethernet';
      case ConnectivityResult.bluetooth:
        return 'Connected via Bluetooth';
      case ConnectivityResult.vpn:
        return 'Connected via VPN';
      case ConnectivityResult.other:
        return 'Connected via Other';
      case ConnectivityResult.none:
        return 'No Internet Connection';
    }
  }

  /// Check if the current connectivity allows for data usage
  bool isDataConnectionAvailable() {
    switch (_currentConnectivity) {
      case ConnectivityResult.wifi:
      case ConnectivityResult.mobile:
      case ConnectivityResult.ethernet:
        return true;
      case ConnectivityResult.bluetooth:
      case ConnectivityResult.vpn:
      case ConnectivityResult.other:
        // These might allow data but are less reliable
        return true;
      case ConnectivityResult.none:
        return false;
    }
  }
}
