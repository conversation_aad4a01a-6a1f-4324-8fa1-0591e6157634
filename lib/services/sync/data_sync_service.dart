import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../../data/datasources/storage_service.dart';
import '../../data/datasources/api_client.dart';
import '../../core/constants/app_constants.dart';
import '../../core/errors/app_error.dart';
import '../../domain/repositories/ride_repository.dart';

import '../network/network_service.dart';
import 'offline_data_service.dart';

/// Abstract interface for data synchronization operations
abstract class DataSyncService {
  /// Initialize the sync service
  Future<void> initialize();

  /// Sync user profile data
  Future<void> syncUserProfile();

  /// Sync rider profile data
  Future<void> syncRiderProfile();

  /// Sync ride data specifically
  Future<SyncResult> syncRideData();

  /// Sync pending offline operations
  Future<SyncResult> syncPendingOperations();

  /// Cache API response data for offline access
  Future<void> cacheApiResponse(String endpoint, Map<String, dynamic> data);

  /// Get cached API response data
  Future<Map<String, dynamic>?> getCachedApiResponse(String endpoint);

  /// Sync all cached data with server
  Future<void> syncAllData();

  /// Perform a full data synchronization
  Future<SyncResult> performFullSync();

  /// Check if sync is needed
  Future<bool> needsSync();

  /// Force sync all data
  Future<void> forceSyncAll();

  /// Force sync regardless of last sync time
  Future<SyncResult> forceSync();

  /// Clear all cached data
  Future<void> clearAllCache();

  /// Schedule automatic sync when connectivity is restored
  void scheduleAutoSync();

  /// Cancel scheduled sync operations
  void cancelAutoSync();

  /// Get last sync timestamp
  Future<DateTime?> getLastSyncTime();

  /// Get sync status
  SyncStatus getSyncStatus();

  /// Dispose resources
  void dispose();
}

/// Implementation of DataSyncService for handling offline data synchronization
class DataSyncServiceImpl implements DataSyncService {
  final StorageService _storageService;
  final ApiClient _apiClient;
  final NetworkService _networkService;
  final RideRepository _rideRepository;
  final OfflineDataService _offlineDataService;

  Timer? _autoSyncTimer;
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;
  bool _isInitialized = false;
  bool _isSyncing = false;
  SyncStatus _syncStatus = SyncStatus.idle;

  DataSyncServiceImpl({
    required StorageService storageService,
    required ApiClient apiClient,
    required NetworkService networkService,
    required RideRepository rideRepository,
    required OfflineDataService offlineDataService,
  }) : _storageService = storageService,
       _apiClient = apiClient,
       _networkService = networkService,
       _rideRepository = rideRepository,
       _offlineDataService = offlineDataService;

  @override
  Future<void> syncUserProfile() async {
    try {
      // Fetch fresh user profile data from API
      final response = await _apiClient.get(AppConstants.authProfile);

      if (response.statusCode == 200 && response.data != null) {
        // Cache the user profile data
        await cacheApiResponse(
          'user_profile',
          response.data as Map<String, dynamic>,
        );

        // Update last sync timestamp
        await _storageService.storeLastSync(DateTime.now());
      }
    } on AppError catch (e) {
      // Log error but don't throw - sync should be resilient
      debugPrint('Failed to sync user profile: ${e.message}');
    } catch (e) {
      debugPrint('Unexpected error during user profile sync: $e');
    }
  }

  @override
  Future<void> syncRiderProfile() async {
    try {
      // Fetch fresh rider profile data from API
      final response = await _apiClient.get(AppConstants.ridersProfile);

      if (response.statusCode == 200 && response.data != null) {
        // Cache the rider profile data
        await cacheApiResponse(
          'rider_profile',
          response.data as Map<String, dynamic>,
        );

        // Update last sync timestamp
        await _storageService.storeLastSync(DateTime.now());
      }
    } on AppError catch (e) {
      // Log error but don't throw - sync should be resilient
      debugPrint('Failed to sync rider profile: ${e.message}');
    } catch (e) {
      debugPrint('Unexpected error during rider profile sync: $e');
    }
  }

  @override
  Future<void> cacheApiResponse(
    String endpoint,
    Map<String, dynamic> data,
  ) async {
    try {
      // Store the API response data with timestamp for offline access
      await _storageService.storeCachedData(endpoint, data);
    } catch (e) {
      debugPrint('Failed to cache API response for $endpoint: $e');
    }
  }

  @override
  Future<Map<String, dynamic>?> getCachedApiResponse(String endpoint) async {
    try {
      // Retrieve cached API response data
      return await _storageService.getCachedData(endpoint);
    } catch (e) {
      debugPrint('Failed to get cached API response for $endpoint: $e');
      return null;
    }
  }

  @override
  Future<void> syncAllData() async {
    try {
      // Check if sync is actually needed
      if (!await needsSync()) {
        return;
      }

      // Sync user profile data
      await syncUserProfile();

      // Sync rider profile data
      await syncRiderProfile();

      // Update last sync timestamp
      await _storageService.storeLastSync(DateTime.now());
    } catch (e) {
      debugPrint('Failed to sync all data: $e');
    }
  }

  @override
  Future<bool> needsSync() async {
    try {
      return await _storageService.needsSync();
    } catch (e) {
      // If we can't determine sync status, assume sync is needed
      return true;
    }
  }

  @override
  Future<void> forceSyncAll() async {
    try {
      // Force sync regardless of last sync time
      await syncUserProfile();
      await syncRiderProfile();

      // Update last sync timestamp
      await _storageService.storeLastSync(DateTime.now());
    } catch (e) {
      debugPrint('Failed to force sync all data: $e');
    }
  }

  @override
  Future<void> clearAllCache() async {
    try {
      // Clear all cached data
      await _storageService.clearAllCachedData();
      await _offlineDataService.clearAllCache();
    } catch (e) {
      debugPrint('Failed to clear all cache: $e');
    }
  }

  @override
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _networkService.initialize();
      await _offlineDataService.initialize();
      scheduleAutoSync();
      _isInitialized = true;
      debugPrint('DataSyncService initialized successfully');
    } catch (e) {
      debugPrint('Failed to initialize DataSyncService: $e');
    }
  }

  @override
  Future<SyncResult> performFullSync() async {
    if (_isSyncing) {
      return const SyncResult(
        success: false,
        message: 'Sync already in progress',
        syncedItems: 0,
      );
    }

    return _performSync(false);
  }

  @override
  Future<SyncResult> forceSync() async {
    if (_isSyncing) {
      return const SyncResult(
        success: false,
        message: 'Sync already in progress',
        syncedItems: 0,
      );
    }

    return _performSync(true);
  }

  Future<SyncResult> _performSync(bool force) async {
    _isSyncing = true;
    _syncStatus = SyncStatus.syncing;
    debugPrint('Starting ${force ? 'forced' : 'full'} data synchronization');

    try {
      final isOnline = await _networkService.isOnline();
      if (!isOnline) {
        _syncStatus = SyncStatus.failed;
        return const SyncResult(
          success: false,
          message: 'No internet connection',
          syncedItems: 0,
        );
      }

      if (!force) {
        final needsSync = await this.needsSync();
        if (!needsSync) {
          _syncStatus = SyncStatus.upToDate;
          return const SyncResult(
            success: true,
            message: 'Data is already up to date',
            syncedItems: 0,
          );
        }
      }

      int totalSyncedItems = 0;
      final List<String> errors = [];

      // Sync pending offline operations first
      final pendingSyncResult = await syncPendingOperations();
      if (pendingSyncResult.success) {
        totalSyncedItems += pendingSyncResult.syncedItems;
      } else {
        errors.add(
          'Pending operations sync failed: ${pendingSyncResult.message}',
        );
      }

      // Sync user profile
      try {
        await syncUserProfile();
        totalSyncedItems++;
      } catch (e) {
        errors.add('Profile sync failed: $e');
      }

      // Sync rider profile
      try {
        await syncRiderProfile();
        totalSyncedItems++;
      } catch (e) {
        errors.add('Rider profile sync failed: $e');
      }

      // Sync ride data
      final rideSyncResult = await syncRideData();
      if (rideSyncResult.success) {
        totalSyncedItems += rideSyncResult.syncedItems;
      } else {
        errors.add('Ride sync failed: ${rideSyncResult.message}');
      }

      // Update last sync timestamp
      await _offlineDataService.updateLastSync();

      final success = errors.isEmpty;
      final message = success
          ? 'Sync completed successfully'
          : 'Sync completed with errors: ${errors.join(', ')}';

      _syncStatus = success ? SyncStatus.completed : SyncStatus.failed;
      debugPrint('Full sync completed: $message');

      return SyncResult(
        success: success,
        message: message,
        syncedItems: totalSyncedItems,
      );
    } catch (e) {
      debugPrint('Full sync failed: $e');
      _syncStatus = SyncStatus.failed;
      return SyncResult(
        success: false,
        message: 'Sync failed: $e',
        syncedItems: 0,
      );
    } finally {
      _isSyncing = false;
      // Reset status to idle after a delay
      Timer(const Duration(seconds: 2), () {
        if (_syncStatus != SyncStatus.syncing) {
          _syncStatus = SyncStatus.idle;
        }
      });
    }
  }

  @override
  Future<SyncResult> syncRideData() async {
    try {
      debugPrint('Syncing ride data');
      int syncedItems = 0;

      // Sync active ride
      final activeRideResult = await _rideRepository.getActiveRide();
      await activeRideResult.fold(
        (error) {
          debugPrint('Failed to sync active ride: ${error.message}');
          return Future.value();
        },
        (ride) async {
          await _offlineDataService.cacheActiveRide(ride);
          if (ride != null) {
            syncedItems++;
            debugPrint('Synced active ride: ${ride.id}');
          }
        },
      );

      // Sync ride history (first page)
      final historyResult = await _rideRepository.getRideHistory(limit: 20);
      await historyResult.fold(
        (error) {
          debugPrint('Failed to sync ride history: ${error.message}');
          return Future.value();
        },
        (history) async {
          await _offlineDataService.cacheRideHistory(history);
          syncedItems += history.length;
          debugPrint('Synced ${history.length} ride history items');
        },
      );

      return SyncResult(
        success: true,
        message: 'Ride data synced successfully',
        syncedItems: syncedItems,
      );
    } catch (e) {
      debugPrint('Ride data sync failed: $e');
      return SyncResult(
        success: false,
        message: 'Ride data sync failed: $e',
        syncedItems: 0,
      );
    }
  }

  @override
  Future<SyncResult> syncPendingOperations() async {
    try {
      debugPrint('Syncing pending offline operations');

      final pendingData = await _offlineDataService.getPendingSyncData();
      int syncedItems = 0;
      final List<String> errors = [];

      for (final entry in pendingData.entries) {
        try {
          final key = entry.key;
          final data = entry.value;

          // Process pending operation based on key type
          await _processPendingOperation(key, data);
          await _offlineDataService.clearPendingSyncData(key);
          syncedItems++;
        } catch (e) {
          errors.add('Failed to sync pending operation ${entry.key}: $e');
        }
      }

      final success = errors.isEmpty;
      final message = success
          ? 'Pending operations synced successfully'
          : 'Some pending operations failed: ${errors.join(', ')}';

      return SyncResult(
        success: success,
        message: message,
        syncedItems: syncedItems,
      );
    } catch (e) {
      debugPrint('Pending operations sync failed: $e');
      return SyncResult(
        success: false,
        message: 'Pending operations sync failed: $e',
        syncedItems: 0,
      );
    }
  }

  @override
  void scheduleAutoSync() {
    // Cancel existing timer
    cancelAutoSync();

    // Listen for connectivity changes
    _connectivitySubscription = _networkService.connectivityStream.listen((
      connectivity,
    ) {
      if (connectivity != ConnectivityResult.none) {
        // Connection restored, schedule sync
        _scheduleDelayedSync();
      }
    });

    // Schedule periodic sync
    _autoSyncTimer = Timer.periodic(
      const Duration(minutes: 30), // Sync every 30 minutes
      (_) => _performAutoSync(),
    );

    debugPrint('Auto sync scheduled');
  }

  @override
  void cancelAutoSync() {
    _autoSyncTimer?.cancel();
    _autoSyncTimer = null;
    _connectivitySubscription?.cancel();
    _connectivitySubscription = null;
    debugPrint('Auto sync cancelled');
  }

  @override
  Future<DateTime?> getLastSyncTime() async {
    return await _offlineDataService.getLastSync();
  }

  @override
  SyncStatus getSyncStatus() {
    return _syncStatus;
  }

  @override
  void dispose() {
    cancelAutoSync();
    _offlineDataService.dispose();
    _isInitialized = false;
    debugPrint('DataSyncService disposed');
  }

  // Private helper methods

  void _scheduleDelayedSync() {
    // Wait a bit before syncing to avoid immediate sync on connection restore
    Timer(const Duration(seconds: 5), () {
      _performAutoSync();
    });
  }

  Future<void> _performAutoSync() async {
    try {
      final needsSync = await this.needsSync();
      if (needsSync) {
        debugPrint('Performing automatic sync');
        await performFullSync();
      }
    } catch (e) {
      debugPrint('Auto sync failed: $e');
    }
  }

  Future<void> _processPendingOperation(
    String key,
    Map<String, dynamic> data,
  ) async {
    // Process different types of pending operations
    // This is a simplified implementation
    // In a real app, you'd handle different operation types
    debugPrint('Processing pending operation: $key');

    // Example: Handle ride cancellation, profile updates, etc.
    // The actual implementation would depend on the operation type
  }
}

/// Status of synchronization
enum SyncStatus { idle, syncing, completed, failed, upToDate }

/// Result of a synchronization operation
@immutable
class SyncResult {
  final bool success;
  final String message;
  final int syncedItems;

  const SyncResult({
    required this.success,
    required this.message,
    required this.syncedItems,
  });

  @override
  String toString() {
    return 'SyncResult(success: $success, message: $message, syncedItems: $syncedItems)';
  }
}
