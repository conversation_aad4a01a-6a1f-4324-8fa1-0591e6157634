import 'dart:async';

import 'package:flutter/foundation.dart';

import '../../data/datasources/storage_service.dart';
import '../../domain/entities/ride.dart';
import '../network/network_service.dart';

/// Service for managing offline data caching and synchronization
abstract class OfflineDataService {
  /// Initialize the offline data service
  Future<void> initialize();

  /// Cache ride data for offline access
  Future<void> cacheRideData(String key, Map<String, dynamic> data);

  /// Get cached ride data
  Future<Map<String, dynamic>?> getCachedRideData(String key);

  /// Cache ride history for offline access
  Future<void> cacheRideHistory(List<RideHistory> history);

  /// Get cached ride history
  Future<List<RideHistory>?> getCachedRideHistory();

  /// Cache active ride for offline access
  Future<void> cacheActiveRide(Ride? ride);

  /// Get cached active ride
  Future<Ride?> getCachedActiveRide();

  /// Cache pricing information for offline access
  Future<void> cachePricingInfo(String routeKey, PricingInfo pricing);

  /// Get cached pricing information
  Future<PricingInfo?> getCachedPricingInfo(String routeKey);

  /// Cache recent locations for offline access
  Future<void> cacheRecentLocations(List<RideLocation> locations);

  /// Get cached recent locations
  Future<List<RideLocation>?> getCachedRecentLocations();

  /// Mark data for synchronization when online
  Future<void> markForSync(String key, Map<String, dynamic> data);

  /// Get pending sync data
  Future<Map<String, Map<String, dynamic>>> getPendingSyncData();

  /// Clear pending sync data after successful sync
  Future<void> clearPendingSyncData(String key);

  /// Clear all cached data
  Future<void> clearAllCache();

  /// Get cache statistics
  Future<CacheStatistics> getCacheStatistics();

  /// Check if data needs refresh
  Future<bool> needsRefresh(String key);

  /// Update last sync timestamp
  Future<void> updateLastSync();

  /// Get last sync timestamp
  Future<DateTime?> getLastSync();

  /// Dispose resources
  void dispose();
}

/// Implementation of offline data service
class OfflineDataServiceImpl implements OfflineDataService {
  final StorageService _storageService;
  final NetworkService _networkService;

  /// Cache keys for different data types
  static const String _rideHistoryCacheKey = 'offline_ride_history';
  static const String _activeRideCacheKey = 'offline_active_ride';
  static const String _pricingCachePrefix = 'offline_pricing_';
  static const String _recentLocationsCacheKey = 'offline_recent_locations';
  static const String _pendingSyncPrefix = 'pending_sync_';
  static const String _cacheMetadataPrefix = 'cache_metadata_';

  /// Cache expiry durations for different data types
  static const Duration _rideHistoryCacheExpiry = Duration(hours: 6);
  static const Duration _activeRideCacheExpiry = Duration(minutes: 5);
  static const Duration _pricingCacheExpiry = Duration(hours: 1);
  static const Duration _recentLocationsCacheExpiry = Duration(days: 7);

  OfflineDataServiceImpl({
    required StorageService storageService,
    required NetworkService networkService,
  }) : _storageService = storageService,
       _networkService = networkService;

  @override
  Future<void> initialize() async {
    try {
      // Initialize network service if not already initialized
      await _networkService.initialize();

      // Clean up expired cache entries
      await _cleanupExpiredCache();

      debugPrint('OfflineDataService initialized successfully');
    } catch (e) {
      debugPrint('Failed to initialize OfflineDataService: $e');
    }
  }

  @override
  Future<void> cacheRideData(String key, Map<String, dynamic> data) async {
    try {
      await _storeCachedDataWithMetadata(key, data, Duration(hours: 1));
    } catch (e) {
      debugPrint('Failed to cache ride data for key $key: $e');
    }
  }

  @override
  Future<Map<String, dynamic>?> getCachedRideData(String key) async {
    try {
      return await _getCachedDataWithExpiry(key);
    } catch (e) {
      debugPrint('Failed to get cached ride data for key $key: $e');
      return null;
    }
  }

  @override
  Future<void> cacheRideHistory(List<RideHistory> history) async {
    try {
      final historyJson = history.map((ride) => ride.toJson()).toList();
      await _storeCachedDataWithMetadata(_rideHistoryCacheKey, {
        'history': historyJson,
      }, _rideHistoryCacheExpiry);
      debugPrint('Cached ${history.length} ride history items');
    } catch (e) {
      debugPrint('Failed to cache ride history: $e');
    }
  }

  @override
  Future<List<RideHistory>?> getCachedRideHistory() async {
    try {
      final cachedData = await _getCachedDataWithExpiry(_rideHistoryCacheKey);
      if (cachedData != null) {
        final historyList = cachedData['history'] as List<dynamic>;
        final history = historyList
            .map((item) => RideHistory.fromJson(item as Map<String, dynamic>))
            .toList();
        debugPrint('Retrieved ${history.length} cached ride history items');
        return history;
      }
    } catch (e) {
      debugPrint('Failed to get cached ride history: $e');
      // Clear corrupted cache
      await _clearCachedData(_rideHistoryCacheKey);
    }
    return null;
  }

  @override
  Future<void> cacheActiveRide(Ride? ride) async {
    try {
      if (ride != null) {
        await _storeCachedDataWithMetadata(
          _activeRideCacheKey,
          ride.toJson(),
          _activeRideCacheExpiry,
        );
        debugPrint('Cached active ride: ${ride.id}');
      } else {
        await _clearCachedData(_activeRideCacheKey);
        debugPrint('Cleared active ride cache');
      }
    } catch (e) {
      debugPrint('Failed to cache active ride: $e');
    }
  }

  @override
  Future<Ride?> getCachedActiveRide() async {
    try {
      final cachedData = await _getCachedDataWithExpiry(_activeRideCacheKey);
      if (cachedData != null) {
        final ride = Ride.fromJson(cachedData);
        debugPrint('Retrieved cached active ride: ${ride.id}');
        return ride;
      }
    } catch (e) {
      debugPrint('Failed to get cached active ride: $e');
      // Clear corrupted cache
      await _clearCachedData(_activeRideCacheKey);
    }
    return null;
  }

  @override
  Future<void> cachePricingInfo(String routeKey, PricingInfo pricing) async {
    try {
      final cacheKey = '$_pricingCachePrefix$routeKey';
      await _storeCachedDataWithMetadata(
        cacheKey,
        pricing.toJson(),
        _pricingCacheExpiry,
      );
      debugPrint('Cached pricing info for route: $routeKey');
    } catch (e) {
      debugPrint('Failed to cache pricing info for route $routeKey: $e');
    }
  }

  @override
  Future<PricingInfo?> getCachedPricingInfo(String routeKey) async {
    try {
      final cacheKey = '$_pricingCachePrefix$routeKey';
      final cachedData = await _getCachedDataWithExpiry(cacheKey);
      if (cachedData != null) {
        final pricing = PricingInfo.fromJson(cachedData);
        debugPrint('Retrieved cached pricing info for route: $routeKey');
        return pricing;
      }
    } catch (e) {
      debugPrint('Failed to get cached pricing info for route $routeKey: $e');
      // Clear corrupted cache
      final cacheKey = '$_pricingCachePrefix$routeKey';
      await _clearCachedData(cacheKey);
    }
    return null;
  }

  @override
  Future<void> cacheRecentLocations(List<RideLocation> locations) async {
    try {
      final locationsJson = locations
          .map((location) => location.toJson())
          .toList();
      await _storeCachedDataWithMetadata(_recentLocationsCacheKey, {
        'locations': locationsJson,
      }, _recentLocationsCacheExpiry);
      debugPrint('Cached ${locations.length} recent locations');
    } catch (e) {
      debugPrint('Failed to cache recent locations: $e');
    }
  }

  @override
  Future<List<RideLocation>?> getCachedRecentLocations() async {
    try {
      final cachedData = await _getCachedDataWithExpiry(
        _recentLocationsCacheKey,
      );
      if (cachedData != null) {
        final locationsList = cachedData['locations'] as List<dynamic>;
        final locations = locationsList
            .map((item) => RideLocation.fromJson(item as Map<String, dynamic>))
            .toList();
        debugPrint('Retrieved ${locations.length} cached recent locations');
        return locations;
      }
    } catch (e) {
      debugPrint('Failed to get cached recent locations: $e');
      // Clear corrupted cache
      await _clearCachedData(_recentLocationsCacheKey);
    }
    return null;
  }

  @override
  Future<void> markForSync(String key, Map<String, dynamic> data) async {
    try {
      final syncKey = '$_pendingSyncPrefix$key';
      final syncData = {
        'data': data,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'retryCount': 0,
      };
      await _storageService.storeCachedData(syncKey, syncData);
      debugPrint('Marked data for sync: $key');
    } catch (e) {
      debugPrint('Failed to mark data for sync: $e');
    }
  }

  @override
  Future<Map<String, Map<String, dynamic>>> getPendingSyncData() async {
    try {
      final pendingData = <String, Map<String, dynamic>>{};

      // This is a simplified implementation
      // In a real app, you'd iterate through all pending sync keys
      // For now, we'll return an empty map as the storage service
      // doesn't provide a way to list all keys

      return pendingData;
    } catch (e) {
      debugPrint('Failed to get pending sync data: $e');
      return {};
    }
  }

  @override
  Future<void> clearPendingSyncData(String key) async {
    try {
      final syncKey = '$_pendingSyncPrefix$key';
      await _storageService.clearCachedData(syncKey);
      debugPrint('Cleared pending sync data: $key');
    } catch (e) {
      debugPrint('Failed to clear pending sync data: $e');
    }
  }

  @override
  Future<void> clearAllCache() async {
    try {
      await _storageService.clearAllCachedData();
      debugPrint('Cleared all cached data');
    } catch (e) {
      debugPrint('Failed to clear all cache: $e');
    }
  }

  @override
  Future<CacheStatistics> getCacheStatistics() async {
    try {
      // Get cache statistics
      final rideHistory = await getCachedRideHistory();
      final activeRide = await getCachedActiveRide();
      final recentLocations = await getCachedRecentLocations();
      final lastSync = await getLastSync();

      return CacheStatistics(
        rideHistoryCount: rideHistory?.length ?? 0,
        hasActiveRide: activeRide != null,
        recentLocationsCount: recentLocations?.length ?? 0,
        lastSyncTime: lastSync,
        cacheSize: 0, // Would need to calculate actual size
      );
    } catch (e) {
      debugPrint('Failed to get cache statistics: $e');
      return const CacheStatistics(
        rideHistoryCount: 0,
        hasActiveRide: false,
        recentLocationsCount: 0,
        lastSyncTime: null,
        cacheSize: 0,
      );
    }
  }

  @override
  Future<bool> needsRefresh(String key) async {
    try {
      final metadataKey = '$_cacheMetadataPrefix$key';
      final metadata = await _storageService.getCachedData(metadataKey);

      if (metadata == null) return true;

      final timestamp = metadata['timestamp'] as int?;
      final expiry = metadata['expiry'] as int?;

      if (timestamp == null || expiry == null) return true;

      final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
      final expiryDuration = Duration(milliseconds: expiry);
      final expiryTime = cacheTime.add(expiryDuration);

      return DateTime.now().isAfter(expiryTime);
    } catch (e) {
      debugPrint('Failed to check if data needs refresh: $e');
      return true;
    }
  }

  @override
  Future<void> updateLastSync() async {
    try {
      await _storageService.storeLastSync(DateTime.now());
      debugPrint('Updated last sync timestamp');
    } catch (e) {
      debugPrint('Failed to update last sync: $e');
    }
  }

  @override
  Future<DateTime?> getLastSync() async {
    try {
      return await _storageService.getLastSync();
    } catch (e) {
      debugPrint('Failed to get last sync: $e');
      return null;
    }
  }

  @override
  void dispose() {
    // Clean up any resources if needed
    debugPrint('OfflineDataService disposed');
  }

  // Private helper methods

  /// Store cached data with metadata for expiry tracking
  Future<void> _storeCachedDataWithMetadata(
    String key,
    Map<String, dynamic> data,
    Duration expiry,
  ) async {
    // Store the actual data
    await _storageService.storeCachedData(key, data);

    // Store metadata for expiry tracking
    final metadataKey = '$_cacheMetadataPrefix$key';
    final metadata = {
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'expiry': expiry.inMilliseconds,
    };
    await _storageService.storeCachedData(metadataKey, metadata);
  }

  /// Get cached data with expiry check
  Future<Map<String, dynamic>?> _getCachedDataWithExpiry(String key) async {
    // Check if data has expired
    final hasExpired = await needsRefresh(key);
    if (hasExpired) {
      await _clearCachedData(key);
      return null;
    }

    return await _storageService.getCachedData(key);
  }

  /// Clear cached data and its metadata
  Future<void> _clearCachedData(String key) async {
    await _storageService.clearCachedData(key);
    final metadataKey = '$_cacheMetadataPrefix$key';
    await _storageService.clearCachedData(metadataKey);
  }

  /// Clean up expired cache entries
  Future<void> _cleanupExpiredCache() async {
    try {
      // This is a simplified cleanup
      // In a real implementation, you'd iterate through all cache keys
      // and remove expired ones
      debugPrint('Cache cleanup completed');
    } catch (e) {
      debugPrint('Failed to cleanup expired cache: $e');
    }
  }
}

/// Statistics about cached data
@immutable
class CacheStatistics {
  final int rideHistoryCount;
  final bool hasActiveRide;
  final int recentLocationsCount;
  final DateTime? lastSyncTime;
  final int cacheSize;

  const CacheStatistics({
    required this.rideHistoryCount,
    required this.hasActiveRide,
    required this.recentLocationsCount,
    required this.lastSyncTime,
    required this.cacheSize,
  });

  @override
  String toString() {
    return 'CacheStatistics('
        'rideHistoryCount: $rideHistoryCount, '
        'hasActiveRide: $hasActiveRide, '
        'recentLocationsCount: $recentLocationsCount, '
        'lastSyncTime: $lastSyncTime, '
        'cacheSize: $cacheSize'
        ')';
  }
}
