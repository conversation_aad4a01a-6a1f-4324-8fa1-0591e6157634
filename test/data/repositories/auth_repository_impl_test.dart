import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:dio/dio.dart';

import 'package:lucian_rides_app/data/repositories/auth_repository_impl.dart';
import 'package:lucian_rides_app/data/datasources/api_client.dart';
import 'package:lucian_rides_app/data/datasources/storage_service.dart';
import 'package:lucian_rides_app/domain/entities/user.dart';
import 'package:lucian_rides_app/core/errors/app_error.dart';
import 'package:lucian_rides_app/core/constants/app_constants.dart';

import 'auth_repository_impl_test.mocks.dart';

@GenerateMocks([ApiClient, StorageService])
void main() {
  group('AuthRepositoryImpl', () {
    late MockApiClient mockApiClient;
    late MockStorageService mockStorageService;
    late AuthRepositoryImpl authRepository;

    setUp(() {
      mockApiClient = MockApiClient();
      mockStorageService = MockStorageService();
      authRepository = AuthRepositoryImpl(
        apiClient: mockApiClient,
        storageService: mockStorageService,
      );
    });

    group('login', () {
      test('should return token on successful login', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';
        const token = 'test-access-token';

        final responseData = {
          'access_token': token,
          'refresh_token': 'test-refresh-token',
          'user': {
            'id': '1',
            'email': email,
            'name': 'Test User',
            'user_type': 'rider',
            'created_at': '2024-01-01T00:00:00Z',
            'updated_at': '2024-01-01T00:00:00Z',
            'is_active': true,
            'language': 'en',
          },
        };

        final response = Response<Map<String, dynamic>>(
          data: responseData,
          statusCode: 200,
          requestOptions: RequestOptions(path: ''),
        );

        when(
          mockApiClient.post(AppConstants.authLogin, data: anyNamed('data')),
        ).thenAnswer((_) async => response);
        when(
          mockStorageService.storeToken(any, expiresAt: anyNamed('expiresAt')),
        ).thenAnswer((_) async {});
        when(
          mockStorageService.storeRefreshToken(any),
        ).thenAnswer((_) async {});
        when(mockStorageService.storeUserData(any)).thenAnswer((_) async {});

        // Act
        final result = await authRepository.login(email, password);

        // Assert
        expect(result, equals(token));

        // Verify API call was made with correct data
        final capturedData =
            verify(
                  mockApiClient.post(
                    AppConstants.authLogin,
                    data: captureAnyNamed('data'),
                  ),
                ).captured.single
                as Map<String, dynamic>;

        expect(capturedData['email'], equals(email));
        expect(capturedData['password'], equals(password));
        expect(capturedData['user_type'], equals('rider'));

        // Verify storage operations
        verify(
          mockStorageService.storeToken(
            token,
            expiresAt: anyNamed('expiresAt'),
          ),
        ).called(1);
        verify(
          mockStorageService.storeRefreshToken('test-refresh-token'),
        ).called(1);
        verify(mockStorageService.storeUserData(any)).called(1);
      });

      test('should throw authentication error when token is missing', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';

        final responseData = <String, dynamic>{}; // Missing access_token

        final response = Response<Map<String, dynamic>>(
          data: responseData,
          statusCode: 200,
          requestOptions: RequestOptions(path: ''),
        );

        when(
          mockApiClient.post(AppConstants.authLogin, data: anyNamed('data')),
        ).thenAnswer((_) async => response);

        // Act & Assert
        expect(
          () => authRepository.login(email, password),
          throwsA(
            isA<AppError>().having(
              (e) => e.when(
                network: (message, details) => false,
                authentication: (message, errorCode) =>
                    message == 'Invalid response: missing access token',
                validation: (message, fieldErrors) => false,
                server: (message, statusCode) => false,
                unknown: (message, exception) => false,
              ),
              'error type',
              isTrue,
            ),
          ),
        );
      });

      test('should throw authentication error on API client error', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'wrongpassword';

        when(
          mockApiClient.post(AppConstants.authLogin, data: anyNamed('data')),
        ).thenThrow(
          DioException(
            requestOptions: RequestOptions(path: ''),
            response: Response(
              statusCode: 401,
              requestOptions: RequestOptions(path: ''),
            ),
          ),
        );

        // Act & Assert
        expect(
          () => authRepository.login(email, password),
          throwsA(
            isA<AppError>().having(
              (e) => e.when(
                network: (message, details) => false,
                authentication: (message, errorCode) =>
                    message.contains('Login failed'),
                validation: (message, fieldErrors) => false,
                server: (message, statusCode) => false,
                unknown: (message, exception) => false,
              ),
              'error type',
              isTrue,
            ),
          ),
        );
      });

      test('should rethrow AppError when thrown by API client', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';
        const appError = AppError.network(message: 'Network connection failed');

        when(
          mockApiClient.post(AppConstants.authLogin, data: anyNamed('data')),
        ).thenThrow(appError);

        // Act & Assert
        expect(
          () => authRepository.login(email, password),
          throwsA(equals(appError)),
        );
      });
    });

    group('register', () {
      test('should return token on successful registration', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';
        const name = 'Test User';
        const userType = UserType.rider;
        const token = 'test-access-token';

        final responseData = {
          'access_token': token,
          'refresh_token': 'test-refresh-token',
          'user': {
            'id': '1',
            'email': email,
            'name': name,
            'user_type': 'rider',
            'created_at': '2024-01-01T00:00:00Z',
            'updated_at': '2024-01-01T00:00:00Z',
            'is_active': true,
            'language': 'en',
          },
        };

        final response = Response<Map<String, dynamic>>(
          data: responseData,
          statusCode: 201,
          requestOptions: RequestOptions(path: ''),
        );

        when(
          mockApiClient.post(AppConstants.authRegister, data: anyNamed('data')),
        ).thenAnswer((_) async => response);
        when(
          mockStorageService.storeToken(any, expiresAt: anyNamed('expiresAt')),
        ).thenAnswer((_) async {});
        when(
          mockStorageService.storeRefreshToken(any),
        ).thenAnswer((_) async {});
        when(mockStorageService.storeUserData(any)).thenAnswer((_) async {});

        // Act
        final result = await authRepository.register(
          email: email,
          password: password,
          name: name,
          userType: userType,
        );

        // Assert
        expect(result, equals(token));

        // Verify API call was made with correct data
        final capturedData =
            verify(
                  mockApiClient.post(
                    AppConstants.authRegister,
                    data: captureAnyNamed('data'),
                  ),
                ).captured.single
                as Map<String, dynamic>;

        expect(capturedData['email'], equals(email));
        expect(capturedData['password'], equals(password));
        expect(capturedData['confirm_password'], equals(password));
        expect(capturedData['name'], equals(name));
        expect(capturedData['user_type'], equals('rider'));

        // Verify storage operations
        verify(
          mockStorageService.storeToken(
            token,
            expiresAt: anyNamed('expiresAt'),
          ),
        ).called(1);
        verify(
          mockStorageService.storeRefreshToken('test-refresh-token'),
        ).called(1);
        verify(mockStorageService.storeUserData(any)).called(1);
      });

      test('should include phone number when provided', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';
        const name = 'Test User';
        const userType = UserType.rider;
        const phone = '+1234567890';
        const token = 'test-access-token';

        final responseData = {'access_token': token};

        final response = Response<Map<String, dynamic>>(
          data: responseData,
          statusCode: 201,
          requestOptions: RequestOptions(path: ''),
        );

        when(
          mockApiClient.post(AppConstants.authRegister, data: anyNamed('data')),
        ).thenAnswer((_) async => response);
        when(
          mockStorageService.storeToken(any, expiresAt: anyNamed('expiresAt')),
        ).thenAnswer((_) async {});

        // Act
        await authRepository.register(
          email: email,
          password: password,
          name: name,
          userType: userType,
          phone: phone,
        );

        // Assert
        final capturedData =
            verify(
                  mockApiClient.post(
                    AppConstants.authRegister,
                    data: captureAnyNamed('data'),
                  ),
                ).captured.single
                as Map<String, dynamic>;

        expect(capturedData['phone'], equals(phone));
      });

      test('should throw authentication error when token is missing', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';
        const name = 'Test User';
        const userType = UserType.rider;

        final responseData = <String, dynamic>{}; // Missing access_token

        final response = Response<Map<String, dynamic>>(
          data: responseData,
          statusCode: 201,
          requestOptions: RequestOptions(path: ''),
        );

        when(
          mockApiClient.post(AppConstants.authRegister, data: anyNamed('data')),
        ).thenAnswer((_) async => response);

        // Act & Assert
        expect(
          () => authRepository.register(
            email: email,
            password: password,
            name: name,
            userType: userType,
          ),
          throwsA(
            isA<AppError>().having(
              (e) => e.when(
                network: (message, details) => false,
                authentication: (message, errorCode) =>
                    message == 'Invalid response: missing access token',
                validation: (message, fieldErrors) => false,
                server: (message, statusCode) => false,
                unknown: (message, exception) => false,
              ),
              'error type',
              isTrue,
            ),
          ),
        );
      });
    });

    group('logout', () {
      test('should call logout endpoint and clear storage', () async {
        // Arrange
        final response = Response<Map<String, dynamic>>(
          data: {'message': 'Logged out successfully'},
          statusCode: 200,
          requestOptions: RequestOptions(path: ''),
        );

        when(
          mockApiClient.post(AppConstants.authLogout),
        ).thenAnswer((_) async => response);
        when(mockStorageService.clearToken()).thenAnswer((_) async {});
        when(mockStorageService.clearRefreshToken()).thenAnswer((_) async {});
        when(mockStorageService.clearUserData()).thenAnswer((_) async {});
        when(mockStorageService.clearAllCachedData()).thenAnswer((_) async {});

        // Act
        await authRepository.logout();

        // Assert
        verify(mockApiClient.post(AppConstants.authLogout)).called(1);
        verify(mockStorageService.clearToken()).called(1);
        verify(mockStorageService.clearRefreshToken()).called(1);
        verify(mockStorageService.clearUserData()).called(1);
        verify(mockStorageService.clearAllCachedData()).called(1);
      });

      test('should clear storage even when API call fails', () async {
        // Arrange
        when(
          mockApiClient.post(AppConstants.authLogout),
        ).thenThrow(DioException(requestOptions: RequestOptions(path: '')));
        when(mockStorageService.clearToken()).thenAnswer((_) async {});
        when(mockStorageService.clearRefreshToken()).thenAnswer((_) async {});
        when(mockStorageService.clearUserData()).thenAnswer((_) async {});
        when(mockStorageService.clearAllCachedData()).thenAnswer((_) async {});

        // Act
        await authRepository.logout();

        // Assert
        verify(mockApiClient.post(AppConstants.authLogout)).called(1);
        verify(mockStorageService.clearToken()).called(1);
        verify(mockStorageService.clearRefreshToken()).called(1);
        verify(mockStorageService.clearUserData()).called(1);
        verify(mockStorageService.clearAllCachedData()).called(1);
      });
    });

    group('getCurrentUser', () {
      test('should return cached user when available', () async {
        // Arrange
        final testUser = User(
          id: '1',
          email: '<EMAIL>',
          name: 'Test User',
          userType: UserType.rider,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          isActive: true,
          language: 'en',
        );

        when(
          mockStorageService.getUserData(),
        ).thenAnswer((_) async => testUser);

        // Act
        final result = await authRepository.getCurrentUser();

        // Assert
        expect(result, equals(testUser));
        verify(mockStorageService.getUserData()).called(1);
        verifyNever(mockApiClient.get(any));
      });

      test('should fetch from server when not cached', () async {
        // Arrange
        final userData = {
          'id': '1',
          'email': '<EMAIL>',
          'name': 'Test User',
          'user_type': 'rider',
          'created_at': '2024-01-01T00:00:00Z',
          'updated_at': '2024-01-01T00:00:00Z',
          'is_active': true,
          'language': 'en',
        };

        final response = Response<Map<String, dynamic>>(
          data: userData,
          statusCode: 200,
          requestOptions: RequestOptions(path: ''),
        );

        when(mockStorageService.getUserData()).thenAnswer((_) async => null);
        when(
          mockApiClient.get(AppConstants.authProfile),
        ).thenAnswer((_) async => response);
        when(mockStorageService.storeUserData(any)).thenAnswer((_) async {});

        // Act
        final result = await authRepository.getCurrentUser();

        // Assert
        expect(result.id, equals('1'));
        expect(result.email, equals('<EMAIL>'));
        expect(result.name, equals('Test User'));
        expect(result.userType, equals(UserType.rider));

        verify(mockStorageService.getUserData()).called(1);
        verify(mockApiClient.get(AppConstants.authProfile)).called(1);
        verify(mockStorageService.storeUserData(any)).called(1);
      });

      test('should throw authentication error on API failure', () async {
        // Arrange
        when(mockStorageService.getUserData()).thenAnswer((_) async => null);
        when(mockApiClient.get(AppConstants.authProfile)).thenThrow(
          DioException(
            requestOptions: RequestOptions(path: ''),
            response: Response(
              statusCode: 401,
              requestOptions: RequestOptions(path: ''),
            ),
          ),
        );

        // Act & Assert
        expect(
          () => authRepository.getCurrentUser(),
          throwsA(
            isA<AppError>().having(
              (e) => e.when(
                network: (message, details) => false,
                authentication: (message, errorCode) =>
                    message.contains('Failed to get current user'),
                validation: (message, fieldErrors) => false,
                server: (message, statusCode) => false,
                unknown: (message, exception) => false,
              ),
              'error type',
              isTrue,
            ),
          ),
        );
      });
    });

    group('verifyToken', () {
      test('should return false when no token exists', () async {
        // Arrange
        when(mockStorageService.hasToken()).thenAnswer((_) async => false);

        // Act
        final result = await authRepository.verifyToken();

        // Assert
        expect(result, isFalse);
        verify(mockStorageService.hasToken()).called(1);
        verifyNever(mockApiClient.get(any));
      });

      test('should return true when token is valid', () async {
        // Arrange
        final response = Response<Map<String, dynamic>>(
          data: {'valid': true},
          statusCode: 200,
          requestOptions: RequestOptions(path: ''),
        );

        when(mockStorageService.hasToken()).thenAnswer((_) async => true);
        when(
          mockApiClient.get(AppConstants.authVerifyToken),
        ).thenAnswer((_) async => response);

        // Act
        final result = await authRepository.verifyToken();

        // Assert
        expect(result, isTrue);
        verify(mockStorageService.hasToken()).called(1);
        verify(mockApiClient.get(AppConstants.authVerifyToken)).called(1);
      });

      test(
        'should return false and clear storage when verification fails',
        () async {
          // Arrange
          when(mockStorageService.hasToken()).thenAnswer((_) async => true);
          when(mockApiClient.get(AppConstants.authVerifyToken)).thenThrow(
            DioException(
              requestOptions: RequestOptions(path: ''),
              response: Response(
                statusCode: 401,
                requestOptions: RequestOptions(path: ''),
              ),
            ),
          );
          when(mockStorageService.clearToken()).thenAnswer((_) async {});
          when(mockStorageService.clearUserData()).thenAnswer((_) async {});

          // Act
          final result = await authRepository.verifyToken();

          // Assert
          expect(result, isFalse);
          verify(mockStorageService.hasToken()).called(1);
          verify(mockApiClient.get(AppConstants.authVerifyToken)).called(1);
          verify(mockStorageService.clearToken()).called(1);
          verify(mockStorageService.clearUserData()).called(1);
        },
      );
    });
  });
}
