// Mocks generated by Mockito 5.4.5 from annotations
// in lucian_rides_app/test/data/repositories/auth_repository_impl_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:dio/dio.dart' as _i2;
import 'package:lucian_rides_app/data/datasources/api_client.dart' as _i3;
import 'package:lucian_rides_app/data/datasources/storage_service.dart' as _i5;
import 'package:lucian_rides_app/domain/entities/user.dart' as _i6;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeResponse_0<T1> extends _i1.SmartFake implements _i2.Response<T1> {
  _FakeResponse_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [ApiClient].
///
/// See the documentation for Mockito's code generation for more information.
class MockApiClient extends _i1.Mock implements _i3.ApiClient {
  MockApiClient() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.Response<T>> get<T>(
    String? path, {
    Map<String, dynamic>? queryParams,
    bool? allowCached = true,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #get,
          [path],
          {
            #queryParams: queryParams,
            #allowCached: allowCached,
          },
        ),
        returnValue: _i4.Future<_i2.Response<T>>.value(_FakeResponse_0<T>(
          this,
          Invocation.method(
            #get,
            [path],
            {
              #queryParams: queryParams,
              #allowCached: allowCached,
            },
          ),
        )),
      ) as _i4.Future<_i2.Response<T>>);

  @override
  _i4.Future<_i2.Response<T>> post<T>(
    String? path, {
    dynamic data,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #post,
          [path],
          {#data: data},
        ),
        returnValue: _i4.Future<_i2.Response<T>>.value(_FakeResponse_0<T>(
          this,
          Invocation.method(
            #post,
            [path],
            {#data: data},
          ),
        )),
      ) as _i4.Future<_i2.Response<T>>);

  @override
  _i4.Future<_i2.Response<T>> put<T>(
    String? path, {
    dynamic data,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #put,
          [path],
          {#data: data},
        ),
        returnValue: _i4.Future<_i2.Response<T>>.value(_FakeResponse_0<T>(
          this,
          Invocation.method(
            #put,
            [path],
            {#data: data},
          ),
        )),
      ) as _i4.Future<_i2.Response<T>>);

  @override
  _i4.Future<_i2.Response<T>> delete<T>(String? path) => (super.noSuchMethod(
        Invocation.method(
          #delete,
          [path],
        ),
        returnValue: _i4.Future<_i2.Response<T>>.value(_FakeResponse_0<T>(
          this,
          Invocation.method(
            #delete,
            [path],
          ),
        )),
      ) as _i4.Future<_i2.Response<T>>);

  @override
  _i4.Future<_i2.Response<T>> patch<T>(
    String? path, {
    dynamic data,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #patch,
          [path],
          {#data: data},
        ),
        returnValue: _i4.Future<_i2.Response<T>>.value(_FakeResponse_0<T>(
          this,
          Invocation.method(
            #patch,
            [path],
            {#data: data},
          ),
        )),
      ) as _i4.Future<_i2.Response<T>>);

  @override
  void updateAuthToken(String? token) => super.noSuchMethod(
        Invocation.method(
          #updateAuthToken,
          [token],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i4.Future<bool> isOnline() => (super.noSuchMethod(
        Invocation.method(
          #isOnline,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);
}

/// A class which mocks [StorageService].
///
/// See the documentation for Mockito's code generation for more information.
class MockStorageService extends _i1.Mock implements _i5.StorageService {
  MockStorageService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<void> storeToken(
    String? token, {
    DateTime? expiresAt,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #storeToken,
          [token],
          {#expiresAt: expiresAt},
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> storeRefreshToken(String? refreshToken) =>
      (super.noSuchMethod(
        Invocation.method(
          #storeRefreshToken,
          [refreshToken],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<String?> getToken() => (super.noSuchMethod(
        Invocation.method(
          #getToken,
          [],
        ),
        returnValue: _i4.Future<String?>.value(),
      ) as _i4.Future<String?>);

  @override
  _i4.Future<String?> getRefreshToken() => (super.noSuchMethod(
        Invocation.method(
          #getRefreshToken,
          [],
        ),
        returnValue: _i4.Future<String?>.value(),
      ) as _i4.Future<String?>);

  @override
  _i4.Future<DateTime?> getTokenExpiry() => (super.noSuchMethod(
        Invocation.method(
          #getTokenExpiry,
          [],
        ),
        returnValue: _i4.Future<DateTime?>.value(),
      ) as _i4.Future<DateTime?>);

  @override
  _i4.Future<bool> isTokenExpired() => (super.noSuchMethod(
        Invocation.method(
          #isTokenExpired,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> shouldRefreshToken() => (super.noSuchMethod(
        Invocation.method(
          #shouldRefreshToken,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<void> clearToken() => (super.noSuchMethod(
        Invocation.method(
          #clearToken,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> clearRefreshToken() => (super.noSuchMethod(
        Invocation.method(
          #clearRefreshToken,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> storeUserData(_i6.User? user) => (super.noSuchMethod(
        Invocation.method(
          #storeUserData,
          [user],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<_i6.User?> getUserData() => (super.noSuchMethod(
        Invocation.method(
          #getUserData,
          [],
        ),
        returnValue: _i4.Future<_i6.User?>.value(),
      ) as _i4.Future<_i6.User?>);

  @override
  _i4.Future<void> clearUserData() => (super.noSuchMethod(
        Invocation.method(
          #clearUserData,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> storeCachedData(
    String? key,
    Map<String, dynamic>? data,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #storeCachedData,
          [
            key,
            data,
          ],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<Map<String, dynamic>?> getCachedData(String? key) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCachedData,
          [key],
        ),
        returnValue: _i4.Future<Map<String, dynamic>?>.value(),
      ) as _i4.Future<Map<String, dynamic>?>);

  @override
  _i4.Future<bool> isCachedDataValid(String? key) => (super.noSuchMethod(
        Invocation.method(
          #isCachedDataValid,
          [key],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<void> clearCachedData(String? key) => (super.noSuchMethod(
        Invocation.method(
          #clearCachedData,
          [key],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> clearAllCachedData() => (super.noSuchMethod(
        Invocation.method(
          #clearAllCachedData,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> storeLastSync(DateTime? timestamp) => (super.noSuchMethod(
        Invocation.method(
          #storeLastSync,
          [timestamp],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<DateTime?> getLastSync() => (super.noSuchMethod(
        Invocation.method(
          #getLastSync,
          [],
        ),
        returnValue: _i4.Future<DateTime?>.value(),
      ) as _i4.Future<DateTime?>);

  @override
  _i4.Future<bool> needsSync() => (super.noSuchMethod(
        Invocation.method(
          #needsSync,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<void> storeLanguage(String? language) => (super.noSuchMethod(
        Invocation.method(
          #storeLanguage,
          [language],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<String?> getLanguage() => (super.noSuchMethod(
        Invocation.method(
          #getLanguage,
          [],
        ),
        returnValue: _i4.Future<String?>.value(),
      ) as _i4.Future<String?>);

  @override
  _i4.Future<void> storeThemeMode(String? themeMode) => (super.noSuchMethod(
        Invocation.method(
          #storeThemeMode,
          [themeMode],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<String?> getThemeMode() => (super.noSuchMethod(
        Invocation.method(
          #getThemeMode,
          [],
        ),
        returnValue: _i4.Future<String?>.value(),
      ) as _i4.Future<String?>);

  @override
  _i4.Future<void> storeOnboardingCompleted(bool? completed) =>
      (super.noSuchMethod(
        Invocation.method(
          #storeOnboardingCompleted,
          [completed],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<bool> isOnboardingCompleted() => (super.noSuchMethod(
        Invocation.method(
          #isOnboardingCompleted,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<void> clearAll() => (super.noSuchMethod(
        Invocation.method(
          #clearAll,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<bool> hasToken() => (super.noSuchMethod(
        Invocation.method(
          #hasToken,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> hasUserData() => (super.noSuchMethod(
        Invocation.method(
          #hasUserData,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);
}
