import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:lucian_rides_app/presentation/providers/auth_notifier.dart';
import 'package:lucian_rides_app/presentation/providers/auth_state.dart';
import 'package:lucian_rides_app/services/auth/auth_service.dart';
import 'package:lucian_rides_app/domain/entities/user.dart';
import 'package:lucian_rides_app/data/models/auth_result.dart';

import 'auth_notifier_test.mocks.dart';

@GenerateMocks([AuthService])
void main() {
  group('AuthNotifier', () {
    late MockAuthService mockAuthService;
    late AuthNotifier authNotifier;
    late ProviderContainer container;

    setUp(() {
      mockAuthService = MockAuthService();
      authNotifier = AuthNotifier(mockAuthService);

      container = ProviderContainer(
        overrides: [authServiceProvider.overrideWithValue(mockAuthService)],
      );
    });

    tearDown(() {
      container.dispose();
    });

    group('initialize', () {
      test(
        'should set authenticated state when user has valid session',
        () async {
          // Arrange
          final testUser = User(
            id: '1',
            email: '<EMAIL>',
            name: 'Test User',
            userType: UserType.rider,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            isActive: true,
            language: 'en',
          );

          when(mockAuthService.hasValidSession()).thenAnswer((_) async => true);
          when(mockAuthService.verifyToken()).thenAnswer((_) async => true);
          when(
            mockAuthService.getCurrentUser(),
          ).thenAnswer((_) async => testUser);

          // Act
          await authNotifier.initialize();

          // Assert
          expect(authNotifier.state, isA<AuthAuthenticated>());
          expect(authNotifier.state.user, equals(testUser));
          verify(mockAuthService.hasValidSession()).called(1);
          verify(mockAuthService.verifyToken()).called(1);
          verify(mockAuthService.getCurrentUser()).called(1);
        },
      );

      test(
        'should set unauthenticated state when user has no valid session',
        () async {
          // Arrange
          when(
            mockAuthService.hasValidSession(),
          ).thenAnswer((_) async => false);

          // Act
          await authNotifier.initialize();

          // Assert
          expect(authNotifier.state, isA<AuthUnauthenticated>());
          verify(mockAuthService.hasValidSession()).called(1);
          verifyNever(mockAuthService.verifyToken());
          verifyNever(mockAuthService.getCurrentUser());
        },
      );

      test(
        'should set unauthenticated state when token verification fails',
        () async {
          // Arrange
          when(mockAuthService.hasValidSession()).thenAnswer((_) async => true);
          when(mockAuthService.verifyToken()).thenAnswer((_) async => false);

          // Act
          await authNotifier.initialize();

          // Assert
          expect(authNotifier.state, isA<AuthUnauthenticated>());
          verify(mockAuthService.hasValidSession()).called(1);
          verify(mockAuthService.verifyToken()).called(1);
          verifyNever(mockAuthService.getCurrentUser());
        },
      );

      test(
        'should set error state when initialization throws exception',
        () async {
          // Arrange
          when(
            mockAuthService.hasValidSession(),
          ).thenThrow(Exception('Network error'));

          // Act
          await authNotifier.initialize();

          // Assert
          expect(authNotifier.state, isA<AuthError>());
          expect(
            authNotifier.state.errorMessage,
            contains('Failed to initialize authentication'),
          );
          expect(authNotifier.state.errorCode, equals('INIT_ERROR'));
        },
      );
    });

    group('login', () {
      test('should set authenticated state on successful login', () async {
        // Arrange
        final testUser = User(
          id: '1',
          email: '<EMAIL>',
          name: 'Test User',
          userType: UserType.rider,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          isActive: true,
          language: 'en',
        );

        final authResult = AuthResult.success(
          token: 'test-token',
          user: testUser,
        );
        when(
          mockAuthService.login('<EMAIL>', 'password123'),
        ).thenAnswer((_) async => authResult);

        // Act
        await authNotifier.login('<EMAIL>', 'password123');

        // Assert
        expect(authNotifier.state, isA<AuthAuthenticated>());
        expect(authNotifier.state.user, equals(testUser));
        verify(
          mockAuthService.login('<EMAIL>', 'password123'),
        ).called(1);
      });

      test('should set error state on login failure', () async {
        // Arrange
        const authResult = AuthResult.failure(
          message: 'Invalid credentials',
          errorCode: 'INVALID_CREDENTIALS',
        );
        when(
          mockAuthService.login('<EMAIL>', 'wrongpassword'),
        ).thenAnswer((_) async => authResult);

        // Act
        await authNotifier.login('<EMAIL>', 'wrongpassword');

        // Assert
        expect(authNotifier.state, isA<AuthError>());
        expect(authNotifier.state.errorMessage, equals('Invalid credentials'));
        expect(authNotifier.state.errorCode, equals('INVALID_CREDENTIALS'));
      });

      test('should set error state when login throws exception', () async {
        // Arrange
        when(
          mockAuthService.login('<EMAIL>', 'password123'),
        ).thenThrow(Exception('Network error'));

        // Act
        await authNotifier.login('<EMAIL>', 'password123');

        // Assert
        expect(authNotifier.state, isA<AuthError>());
        expect(authNotifier.state.errorMessage, contains('Login failed'));
        expect(authNotifier.state.errorCode, equals('LOGIN_ERROR'));
      });

      test('should set loading state during login', () async {
        // Arrange
        final testUser = User(
          id: '1',
          email: '<EMAIL>',
          name: 'Test User',
          userType: UserType.rider,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          isActive: true,
          language: 'en',
        );

        final authResult = AuthResult.success(
          token: 'test-token',
          user: testUser,
        );
        when(
          mockAuthService.login('<EMAIL>', 'password123'),
        ).thenAnswer((_) async {
          // Verify loading state is set
          expect(authNotifier.state, isA<AuthLoading>());
          return authResult;
        });

        // Act
        await authNotifier.login('<EMAIL>', 'password123');

        // Assert - final state should be authenticated
        expect(authNotifier.state, isA<AuthAuthenticated>());
      });
    });

    group('register', () {
      test(
        'should set authenticated state on successful registration',
        () async {
          // Arrange
          final testUser = User(
            id: '1',
            email: '<EMAIL>',
            name: 'Test User',
            userType: UserType.rider,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            isActive: true,
            language: 'en',
          );

          final authResult = AuthResult.success(
            token: 'test-token',
            user: testUser,
          );
          when(
            mockAuthService.register(
              email: '<EMAIL>',
              password: 'password123',
              name: 'Test User',
              userType: UserType.rider,
              phone: null,
            ),
          ).thenAnswer((_) async => authResult);

          // Act
          await authNotifier.register(
            email: '<EMAIL>',
            password: 'password123',
            name: 'Test User',
            userType: UserType.rider,
          );

          // Assert
          expect(authNotifier.state, isA<AuthAuthenticated>());
          expect(authNotifier.state.user, equals(testUser));
        },
      );

      test('should set error state on registration failure', () async {
        // Arrange
        const authResult = AuthResult.failure(
          message: 'Email already exists',
          errorCode: 'EMAIL_EXISTS',
        );
        when(
          mockAuthService.register(
            email: '<EMAIL>',
            password: 'password123',
            name: 'Test User',
            userType: UserType.rider,
            phone: null,
          ),
        ).thenAnswer((_) async => authResult);

        // Act
        await authNotifier.register(
          email: '<EMAIL>',
          password: 'password123',
          name: 'Test User',
          userType: UserType.rider,
        );

        // Assert
        expect(authNotifier.state, isA<AuthError>());
        expect(authNotifier.state.errorMessage, equals('Email already exists'));
        expect(authNotifier.state.errorCode, equals('EMAIL_EXISTS'));
      });
    });

    group('logout', () {
      test('should set unauthenticated state on successful logout', () async {
        // Arrange
        when(mockAuthService.logout()).thenAnswer((_) async {});

        // Act
        await authNotifier.logout();

        // Assert
        expect(authNotifier.state, isA<AuthUnauthenticated>());
        verify(mockAuthService.logout()).called(1);
      });

      test(
        'should set unauthenticated state even when logout throws exception',
        () async {
          // Arrange
          when(mockAuthService.logout()).thenThrow(Exception('Network error'));

          // Act
          await authNotifier.logout();

          // Assert
          expect(authNotifier.state, isA<AuthUnauthenticated>());
          verify(mockAuthService.logout()).called(1);
        },
      );
    });

    group('refreshUser', () {
      test('should update user data when authenticated', () async {
        // Arrange
        final initialUser = User(
          id: '1',
          email: '<EMAIL>',
          name: 'Test User',
          userType: UserType.rider,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          isActive: true,
          language: 'en',
        );

        final updatedUser = initialUser.copyWith(name: 'Updated User');

        // Set initial authenticated state
        authNotifier.state = AuthState.authenticated(user: initialUser);

        when(
          mockAuthService.getCurrentUser(),
        ).thenAnswer((_) async => updatedUser);

        // Act
        await authNotifier.refreshUser();

        // Assert
        expect(authNotifier.state, isA<AuthAuthenticated>());
        expect(authNotifier.state.user?.name, equals('Updated User'));
      });

      test('should not refresh when not authenticated', () async {
        // Arrange
        authNotifier.state = const AuthState.unauthenticated();

        // Act
        await authNotifier.refreshUser();

        // Assert
        expect(authNotifier.state, isA<AuthUnauthenticated>());
        verifyNever(mockAuthService.getCurrentUser());
      });
    });

    group('verifyToken', () {
      test(
        'should set unauthenticated when token is invalid and currently authenticated',
        () async {
          // Arrange
          final testUser = User(
            id: '1',
            email: '<EMAIL>',
            name: 'Test User',
            userType: UserType.rider,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            isActive: true,
            language: 'en',
          );

          authNotifier.state = AuthState.authenticated(user: testUser);
          when(mockAuthService.verifyToken()).thenAnswer((_) async => false);

          // Act
          await authNotifier.verifyToken();

          // Assert
          expect(authNotifier.state, isA<AuthUnauthenticated>());
        },
      );

      test('should maintain state when token is valid', () async {
        // Arrange
        final testUser = User(
          id: '1',
          email: '<EMAIL>',
          name: 'Test User',
          userType: UserType.rider,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          isActive: true,
          language: 'en',
        );

        authNotifier.state = AuthState.authenticated(user: testUser);
        when(mockAuthService.verifyToken()).thenAnswer((_) async => true);

        // Act
        await authNotifier.verifyToken();

        // Assert
        expect(authNotifier.state, isA<AuthAuthenticated>());
        expect(authNotifier.state.user, equals(testUser));
      });
    });

    group('clearError', () {
      test('should set unauthenticated state when in error state', () {
        // Arrange
        authNotifier.state = const AuthState.error(
          message: 'Test error',
          errorCode: 'TEST_ERROR',
        );

        // Act
        authNotifier.clearError();

        // Assert
        expect(authNotifier.state, isA<AuthUnauthenticated>());
      });

      test('should not change state when not in error state', () {
        // Arrange
        authNotifier.state = const AuthState.unauthenticated();

        // Act
        authNotifier.clearError();

        // Assert
        expect(authNotifier.state, isA<AuthUnauthenticated>());
      });
    });
  });

  group('AuthState Extensions', () {
    test('isAuthenticated should return true only for authenticated state', () {
      expect(
        AuthState.authenticated(
          user: User(
            id: '1',
            email: '<EMAIL>',
            name: 'Test User',
            userType: UserType.rider,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            isActive: true,
            language: 'en',
          ),
        ).isAuthenticated,
        isTrue,
      );

      expect(const AuthState.initial().isAuthenticated, isFalse);
      expect(const AuthState.loading().isAuthenticated, isFalse);
      expect(const AuthState.unauthenticated().isAuthenticated, isFalse);
      expect(const AuthState.error(message: 'error').isAuthenticated, isFalse);
    });

    test('isLoading should return true only for loading state', () {
      expect(const AuthState.loading().isLoading, isTrue);

      expect(const AuthState.initial().isLoading, isFalse);
      expect(
        AuthState.authenticated(
          user: User(
            id: '1',
            email: '<EMAIL>',
            name: 'Test User',
            userType: UserType.rider,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            isActive: true,
            language: 'en',
          ),
        ).isLoading,
        isFalse,
      );
      expect(const AuthState.unauthenticated().isLoading, isFalse);
      expect(const AuthState.error(message: 'error').isLoading, isFalse);
    });

    test('hasError should return true only for error state', () {
      expect(const AuthState.error(message: 'error').hasError, isTrue);

      expect(const AuthState.initial().hasError, isFalse);
      expect(const AuthState.loading().hasError, isFalse);
      expect(
        AuthState.authenticated(
          user: User(
            id: '1',
            email: '<EMAIL>',
            name: 'Test User',
            userType: UserType.rider,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            isActive: true,
            language: 'en',
          ),
        ).hasError,
        isFalse,
      );
      expect(const AuthState.unauthenticated().hasError, isFalse);
    });
  });
}
