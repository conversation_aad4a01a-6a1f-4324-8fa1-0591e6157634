import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:lucian_rides_app/presentation/providers/form_providers.dart';
import 'package:lucian_rides_app/domain/entities/user.dart';

void main() {
  group('LoginFormNotifier', () {
    late ProviderContainer container;
    late LoginFormNotifier notifier;

    setUp(() {
      container = ProviderContainer();
      notifier = container.read(loginFormProvider.notifier);
    });

    tearDown(() {
      container.dispose();
    });

    test('should have initial state', () {
      final state = container.read(loginFormProvider);
      expect(state.email, isEmpty);
      expect(state.password, isEmpty);
      expect(state.isLoading, isFalse);
      expect(state.isFormValid, isFalse);
      expect(state.obscurePassword, isFalse);
      expect(state.hasAttemptedSubmit, isFalse);
      expect(state.errorMessage, isNull);
      expect(state.fieldErrors, isEmpty);
    });

    group('updateEmail', () {
      test('should update email and validate', () {
        notifier.updateEmail('<EMAIL>');

        final state = container.read(loginFormProvider);
        expect(state.email, equals('<EMAIL>'));
        expect(state.fieldErrors, isEmpty);
      });

      test('should add validation error for invalid email', () {
        notifier.updateEmail('invalid-email');

        final state = container.read(loginFormProvider);
        expect(state.email, equals('invalid-email'));
        expect(
          state.fieldErrors['email'],
          equals('Please enter a valid email address'),
        );
      });

      test('should remove validation error when email becomes valid', () {
        // First set invalid email
        notifier.updateEmail('invalid-email');
        expect(
          container.read(loginFormProvider).fieldErrors['email'],
          isNotNull,
        );

        // Then set valid email
        notifier.updateEmail('<EMAIL>');
        final state = container.read(loginFormProvider);
        expect(state.fieldErrors['email'], isNull);
      });
    });

    group('updatePassword', () {
      test('should update password and validate', () {
        notifier.updatePassword('password123');

        final state = container.read(loginFormProvider);
        expect(state.password, equals('password123'));
        expect(state.fieldErrors, isEmpty);
      });

      test('should add validation error for invalid password', () {
        notifier.updatePassword('123');

        final state = container.read(loginFormProvider);
        expect(state.password, equals('123'));
        expect(
          state.fieldErrors['password'],
          equals('Password must be at least 8 characters'),
        );
      });
    });

    group('form validity', () {
      test('should update form validity when both fields are valid', () {
        notifier.updateEmail('<EMAIL>');
        notifier.updatePassword('password123');

        final state = container.read(loginFormProvider);
        expect(state.isFormValid, isTrue);
      });

      test('should be invalid when email is invalid', () {
        notifier.updateEmail('invalid-email');
        notifier.updatePassword('password123');

        final state = container.read(loginFormProvider);
        expect(state.isFormValid, isFalse);
      });

      test('should be invalid when password is invalid', () {
        notifier.updateEmail('<EMAIL>');
        notifier.updatePassword('123');

        final state = container.read(loginFormProvider);
        expect(state.isFormValid, isFalse);
      });
    });

    test('should toggle password visibility', () {
      expect(container.read(loginFormProvider).obscurePassword, isFalse);

      notifier.togglePasswordVisibility();
      expect(container.read(loginFormProvider).obscurePassword, isTrue);

      notifier.togglePasswordVisibility();
      expect(container.read(loginFormProvider).obscurePassword, isFalse);
    });

    test('should set loading state', () {
      notifier.setLoading(true);
      expect(container.read(loginFormProvider).isLoading, isTrue);

      notifier.setLoading(false);
      expect(container.read(loginFormProvider).isLoading, isFalse);
    });

    test('should set and clear error message', () {
      notifier.setError('Login failed');
      expect(
        container.read(loginFormProvider).errorMessage,
        equals('Login failed'),
      );

      notifier.clearError();
      expect(container.read(loginFormProvider).errorMessage, isNull);
    });

    test('should mark attempted submit and validate all fields', () {
      notifier.updateEmail('invalid-email');
      notifier.updatePassword('123');

      notifier.markAttemptedSubmit();

      final state = container.read(loginFormProvider);
      expect(state.hasAttemptedSubmit, isTrue);
      expect(
        state.fieldErrors['email'],
        equals('Please enter a valid email address'),
      );
      expect(
        state.fieldErrors['password'],
        equals('Password must be at least 8 characters'),
      );
      expect(state.isFormValid, isFalse);
    });

    test('should reset form to initial state', () {
      // Set some values
      notifier.updateEmail('<EMAIL>');
      notifier.updatePassword('password123');
      notifier.setLoading(true);
      notifier.setError('Some error');
      notifier.markAttemptedSubmit();

      // Reset
      notifier.reset();

      final state = container.read(loginFormProvider);
      expect(state.email, isEmpty);
      expect(state.password, isEmpty);
      expect(state.isLoading, isFalse);
      expect(state.isFormValid, isFalse);
      expect(state.obscurePassword, isFalse);
      expect(state.hasAttemptedSubmit, isFalse);
      expect(state.errorMessage, isNull);
      expect(state.fieldErrors, isEmpty);
    });
  });

  group('RegisterFormNotifier', () {
    late ProviderContainer container;
    late RegisterFormNotifier notifier;

    setUp(() {
      container = ProviderContainer();
      notifier = container.read(registerFormProvider.notifier);
    });

    tearDown(() {
      container.dispose();
    });

    test('should have initial state', () {
      final state = container.read(registerFormProvider);
      expect(state.name, isEmpty);
      expect(state.email, isEmpty);
      expect(state.password, isEmpty);
      expect(state.confirmPassword, isEmpty);
      expect(state.phone, isEmpty);
      expect(state.userType, equals(UserType.rider));
      expect(state.isLoading, isFalse);
      expect(state.isFormValid, isFalse);
      expect(state.obscurePassword, isFalse);
      expect(state.obscureConfirmPassword, isFalse);
      expect(state.hasAttemptedSubmit, isFalse);
      expect(state.errorMessage, isNull);
      expect(state.fieldErrors, isEmpty);
    });

    group('field updates', () {
      test('should update name and validate', () {
        notifier.updateName('John Doe');

        final state = container.read(registerFormProvider);
        expect(state.name, equals('John Doe'));
        expect(state.fieldErrors['name'], isNull);
      });

      test('should update email and validate', () {
        notifier.updateEmail('<EMAIL>');

        final state = container.read(registerFormProvider);
        expect(state.email, equals('<EMAIL>'));
        expect(state.fieldErrors['email'], isNull);
      });

      test('should update password and re-validate confirm password', () {
        // First set confirm password
        notifier.updateConfirmPassword('password123');

        // Then update password to match
        notifier.updatePassword('password123');

        final state = container.read(registerFormProvider);
        expect(state.password, equals('password123'));
        expect(state.fieldErrors['password'], isNull);
        expect(state.fieldErrors['confirmPassword'], isNull);
      });

      test('should update confirm password and validate against password', () {
        notifier.updatePassword('password123');
        notifier.updateConfirmPassword('password123');

        final state = container.read(registerFormProvider);
        expect(state.confirmPassword, equals('password123'));
        expect(state.fieldErrors['confirmPassword'], isNull);
      });

      test('should show error when passwords do not match', () {
        notifier.updatePassword('password123');
        notifier.updateConfirmPassword('different123');

        final state = container.read(registerFormProvider);
        expect(
          state.fieldErrors['confirmPassword'],
          equals('Passwords do not match'),
        );
      });

      test('should update phone and validate', () {
        notifier.updatePhone('+**********');

        final state = container.read(registerFormProvider);
        expect(state.phone, equals('+**********'));
        expect(state.fieldErrors['phone'], isNull);
      });

      test('should update user type', () {
        notifier.updateUserType(UserType.rider);

        final state = container.read(registerFormProvider);
        expect(state.userType, equals(UserType.rider));
      });
    });

    group('form validity', () {
      test('should be valid when all required fields are valid', () {
        notifier.updateName('John Doe');
        notifier.updateEmail('<EMAIL>');
        notifier.updatePassword('password123');
        notifier.updateConfirmPassword('password123');

        final state = container.read(registerFormProvider);
        expect(state.isFormValid, isTrue);
      });

      test('should be invalid when required field is missing', () {
        notifier.updateEmail('<EMAIL>');
        notifier.updatePassword('password123');
        notifier.updateConfirmPassword('password123');
        // Missing name

        final state = container.read(registerFormProvider);
        expect(state.isFormValid, isFalse);
      });

      test('should be invalid when field validation fails', () {
        notifier.updateName('John Doe');
        notifier.updateEmail('invalid-email');
        notifier.updatePassword('password123');
        notifier.updateConfirmPassword('password123');

        final state = container.read(registerFormProvider);
        expect(state.isFormValid, isFalse);
      });
    });

    test('should toggle password visibility', () {
      expect(container.read(registerFormProvider).obscurePassword, isFalse);

      notifier.togglePasswordVisibility();
      expect(container.read(registerFormProvider).obscurePassword, isTrue);
    });

    test('should toggle confirm password visibility', () {
      expect(
        container.read(registerFormProvider).obscureConfirmPassword,
        isFalse,
      );

      notifier.toggleConfirmPasswordVisibility();
      expect(
        container.read(registerFormProvider).obscureConfirmPassword,
        isTrue,
      );
    });

    test('should validate all fields on attempted submit', () {
      notifier.updateName('A'); // Too short
      notifier.updateEmail('invalid-email');
      notifier.updatePassword('123'); // Too short
      notifier.updateConfirmPassword('different');
      notifier.updatePhone('invalid-phone');

      notifier.markAttemptedSubmit();

      final state = container.read(registerFormProvider);
      expect(state.hasAttemptedSubmit, isTrue);
      expect(
        state.fieldErrors['name'],
        equals('Name must be at least 2 characters'),
      );
      expect(
        state.fieldErrors['email'],
        equals('Please enter a valid email address'),
      );
      expect(
        state.fieldErrors['password'],
        equals('Password must be at least 8 characters'),
      );
      expect(
        state.fieldErrors['confirmPassword'],
        equals('Passwords do not match'),
      );
      expect(
        state.fieldErrors['phone'],
        equals('Please enter a valid phone number'),
      );
      expect(state.isFormValid, isFalse);
    });
  });

  group('ProfileFormNotifier', () {
    late ProviderContainer container;
    late ProfileFormNotifier notifier;

    setUp(() {
      container = ProviderContainer();
      notifier = container.read(profileFormProvider.notifier);
    });

    tearDown(() {
      container.dispose();
    });

    test('should have initial state', () {
      final state = container.read(profileFormProvider);
      expect(state.name, isEmpty);
      expect(state.email, isEmpty);
      expect(state.phone, isEmpty);
      expect(state.isLoading, isFalse);
      expect(state.isFormValid, isFalse);
      expect(state.hasChanges, isFalse);
      expect(state.hasAttemptedSubmit, isFalse);
      expect(state.errorMessage, isNull);
      expect(state.fieldErrors, isEmpty);
    });

    test('should initialize with user data', () {
      notifier.initialize(
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+**********',
      );

      final state = container.read(profileFormProvider);
      expect(state.name, equals('John Doe'));
      expect(state.email, equals('<EMAIL>'));
      expect(state.phone, equals('+**********'));
      expect(state.isFormValid, isTrue);
      expect(state.hasChanges, isFalse);
    });

    group('field updates and change tracking', () {
      setUp(() {
        notifier.initialize(
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '+**********',
        );
      });

      test('should track changes when name is updated', () {
        notifier.updateName('Jane Doe');

        final state = container.read(profileFormProvider);
        expect(state.name, equals('Jane Doe'));
        expect(state.hasChanges, isTrue);
      });

      test('should track changes when email is updated', () {
        notifier.updateEmail('<EMAIL>');

        final state = container.read(profileFormProvider);
        expect(state.email, equals('<EMAIL>'));
        expect(state.hasChanges, isTrue);
      });

      test('should track changes when phone is updated', () {
        notifier.updatePhone('+**********');

        final state = container.read(profileFormProvider);
        expect(state.phone, equals('+**********'));
        expect(state.hasChanges, isTrue);
      });

      test('should not show changes when value is same as original', () {
        notifier.updateName('John Doe'); // Same as original

        final state = container.read(profileFormProvider);
        expect(state.hasChanges, isFalse);
      });

      test('should show changes when value differs from original', () {
        notifier.updateName('Different Name');
        expect(container.read(profileFormProvider).hasChanges, isTrue);

        // Change back to original
        notifier.updateName('John Doe');
        expect(container.read(profileFormProvider).hasChanges, isFalse);
      });
    });

    test('should validate fields and update form validity', () {
      notifier.initialize(name: 'John Doe', email: '<EMAIL>');

      // Update to invalid email
      notifier.updateEmail('invalid-email');

      final state = container.read(profileFormProvider);
      expect(
        state.fieldErrors['email'],
        equals('Please enter a valid email address'),
      );
      expect(state.isFormValid, isFalse);
    });

    test('should reset to original values', () {
      notifier.initialize(
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+**********',
      );

      // Make changes
      notifier.updateName('Jane Doe');
      notifier.updateEmail('<EMAIL>');
      notifier.setError('Some error');

      // Reset
      notifier.reset();

      final state = container.read(profileFormProvider);
      expect(state.name, equals('John Doe'));
      expect(state.email, equals('<EMAIL>'));
      expect(state.phone, equals('+**********'));
      expect(state.hasChanges, isFalse);
      expect(state.isFormValid, isTrue);
      expect(state.errorMessage, isNull);
    });

    test('should validate all fields on attempted submit', () {
      notifier.initialize(name: 'John Doe', email: '<EMAIL>');

      // Update to invalid values
      notifier.updateName(''); // Empty name
      notifier.updateEmail('invalid-email');
      notifier.updatePhone('invalid-phone');

      notifier.markAttemptedSubmit();

      final state = container.read(profileFormProvider);
      expect(state.hasAttemptedSubmit, isTrue);
      expect(state.fieldErrors['name'], equals('Name is required'));
      expect(
        state.fieldErrors['email'],
        equals('Please enter a valid email address'),
      );
      expect(
        state.fieldErrors['phone'],
        equals('Please enter a valid phone number'),
      );
      expect(state.isFormValid, isFalse);
    });
  });
}
