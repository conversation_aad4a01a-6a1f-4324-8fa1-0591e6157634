import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:dartz/dartz.dart';

import 'package:lucian_rides_app/domain/repositories/ride_repository.dart';
import 'package:lucian_rides_app/domain/entities/ride.dart';

import 'package:lucian_rides_app/presentation/providers/ride_tracking_notifier.dart';
import 'package:lucian_rides_app/presentation/providers/ride_tracking_state.dart';
import 'package:lucian_rides_app/core/errors/app_error.dart';

import 'ride_tracking_notifier_test.mocks.dart';

@GenerateMocks([RideRepository])
void main() {
  group('RideTrackingNotifier', () {
    late MockRideRepository mockRideRepository;
    late RideTrackingNotifier notifier;

    setUp(() {
      mockRideRepository = MockRideRepository();
      notifier = RideTrackingNotifier(mockRideRepository);
    });

    tearDown(() {
      notifier.dispose();
    });

    test('initial state should be empty', () {
      expect(notifier.state, equals(const RideTrackingState()));
      expect(notifier.state.hasActiveRide, isFalse);
      expect(notifier.state.hasDriverInfo, isFalse);
      expect(notifier.state.hasDriverLocation, isFalse);
      expect(notifier.state.hasETA, isFalse);
    });

    test('should initialize tracking successfully', () async {
      // Arrange
      const rideId = 'test-ride-id';
      final mockRide = Ride(
        id: rideId,
        riderId: 'rider-id',
        driverId: 'driver-id',
        status: RideStatus.accepted,
        pickupLocation: const RideLocation(
          name: 'Pickup Location',
          latitude: 14.0,
          longitude: -61.0,
        ),
        dropoffLocation: const RideLocation(
          name: 'Dropoff Location',
          latitude: 14.1,
          longitude: -61.1,
        ),
        fixedFare: 25.0,
        paymentStatus: PaymentStatus.pending,
        createdAt: DateTime.now(),
      );

      when(
        mockRideRepository.getRideStatus(rideId),
      ).thenAnswer((_) async => Right(mockRide));

      // Act
      await notifier.initializeTracking(rideId);

      // Assert
      expect(notifier.state.hasActiveRide, isTrue);
      expect(notifier.state.activeRide?.id, equals(rideId));
      expect(
        notifier.state.connectionStatus,
        equals(ConnectionStatus.connected),
      );
      expect(notifier.state.hasDriverInfo, isTrue);
      expect(notifier.state.isLoading, isFalse);
    });

    test('should handle initialization error', () async {
      // Arrange
      const rideId = 'test-ride-id';
      const error = AppError.network(message: 'Failed to load ride');

      when(
        mockRideRepository.getRideStatus(rideId),
      ).thenAnswer((_) async => const Left(error));

      // Act
      await notifier.initializeTracking(rideId);

      // Assert
      expect(notifier.state.hasActiveRide, isFalse);
      expect(notifier.state.errorMessage, equals('Failed to load ride'));
      expect(notifier.state.connectionStatus, equals(ConnectionStatus.failed));
      expect(notifier.state.isLoading, isFalse);
    });

    test('should cancel ride successfully', () async {
      // Arrange
      const rideId = 'test-ride-id';
      final mockRide = Ride(
        id: rideId,
        riderId: 'rider-id',
        driverId: 'driver-id',
        status: RideStatus.accepted,
        pickupLocation: const RideLocation(
          name: 'Pickup Location',
          latitude: 14.0,
          longitude: -61.0,
        ),
        dropoffLocation: const RideLocation(
          name: 'Dropoff Location',
          latitude: 14.1,
          longitude: -61.1,
        ),
        fixedFare: 25.0,
        paymentStatus: PaymentStatus.pending,
        createdAt: DateTime.now(),
      );

      // Set up initial state
      when(
        mockRideRepository.getRideStatus(rideId),
      ).thenAnswer((_) async => Right(mockRide));
      await notifier.initializeTracking(rideId);

      // Mock cancellation
      when(
        mockRideRepository.cancelRide(
          rideId,
          CancellationReason.riderChangedMind,
        ),
      ).thenAnswer((_) async => const Right(null));

      // Act
      await notifier.cancelRide(CancellationReason.riderChangedMind);

      // Assert
      expect(notifier.state.activeRide?.status, equals(RideStatus.cancelled));
      expect(
        notifier.state.activeRide?.cancellationReason,
        equals(CancellationReason.riderChangedMind),
      );
      expect(
        notifier.state.connectionStatus,
        equals(ConnectionStatus.disconnected),
      );
    });

    test('should handle cancellation error', () async {
      // Arrange
      const rideId = 'test-ride-id';
      final mockRide = Ride(
        id: rideId,
        riderId: 'rider-id',
        driverId: 'driver-id',
        status: RideStatus.accepted,
        pickupLocation: const RideLocation(
          name: 'Pickup Location',
          latitude: 14.0,
          longitude: -61.0,
        ),
        dropoffLocation: const RideLocation(
          name: 'Dropoff Location',
          latitude: 14.1,
          longitude: -61.1,
        ),
        fixedFare: 25.0,
        paymentStatus: PaymentStatus.pending,
        createdAt: DateTime.now(),
      );

      // Set up initial state
      when(
        mockRideRepository.getRideStatus(rideId),
      ).thenAnswer((_) async => Right(mockRide));
      await notifier.initializeTracking(rideId);

      // Mock cancellation error
      const error = AppError.network(message: 'Failed to cancel ride');
      when(
        mockRideRepository.cancelRide(
          rideId,
          CancellationReason.riderChangedMind,
        ),
      ).thenAnswer((_) async => const Left(error));

      // Act
      await notifier.cancelRide(CancellationReason.riderChangedMind);

      // Assert
      expect(notifier.state.activeRide?.status, equals(RideStatus.accepted));
      expect(notifier.state.errorMessage, equals('Failed to cancel ride'));
    });

    test('should clear tracking state', () async {
      // Arrange
      const rideId = 'test-ride-id';
      final mockRide = Ride(
        id: rideId,
        riderId: 'rider-id',
        driverId: 'driver-id',
        status: RideStatus.accepted,
        pickupLocation: const RideLocation(
          name: 'Pickup Location',
          latitude: 14.0,
          longitude: -61.0,
        ),
        dropoffLocation: const RideLocation(
          name: 'Dropoff Location',
          latitude: 14.1,
          longitude: -61.1,
        ),
        fixedFare: 25.0,
        paymentStatus: PaymentStatus.pending,
        createdAt: DateTime.now(),
      );

      when(
        mockRideRepository.getRideStatus(rideId),
      ).thenAnswer((_) async => Right(mockRide));
      await notifier.initializeTracking(rideId);

      // Act
      notifier.clearTracking();

      // Assert
      expect(notifier.state, equals(const RideTrackingState()));
      expect(notifier.state.hasActiveRide, isFalse);
      expect(
        notifier.state.connectionStatus,
        equals(ConnectionStatus.disconnected),
      );
    });

    test('should clear errors', () {
      // Arrange
      notifier.state = notifier.state.copyWith(
        errorMessage: 'Test error',
        fieldErrors: {'field': 'Field error'},
      );

      // Act
      notifier.clearErrors();

      // Assert
      expect(notifier.state.errorMessage, isNull);
      expect(notifier.state.fieldErrors, isEmpty);
    });

    group('State getters', () {
      test('should return correct status display text', () {
        // Test different ride statuses
        final testCases = [
          (RideStatus.requested, 'Finding driver...'),
          (RideStatus.accepted, 'Driver on the way'),
          (RideStatus.inProgress, 'Trip in progress'),
          (RideStatus.completed, 'Trip completed'),
          (RideStatus.cancelled, 'Trip cancelled'),
        ];

        for (final (status, expectedText) in testCases) {
          final ride = Ride(
            id: 'test-id',
            riderId: 'rider-id',
            status: status,
            pickupLocation: const RideLocation(
              name: 'Pickup',
              latitude: 14.0,
              longitude: -61.0,
            ),
            dropoffLocation: const RideLocation(
              name: 'Dropoff',
              latitude: 14.1,
              longitude: -61.1,
            ),
            fixedFare: 25.0,
            paymentStatus: PaymentStatus.pending,
            createdAt: DateTime.now(),
          );

          notifier.state = notifier.state.copyWith(activeRide: ride);
          expect(notifier.state.statusDisplayText, equals(expectedText));
        }
      });

      test('should return correct formatted ETA', () {
        // Test different ETA values
        final testCases = [
          (null, null),
          (0, 'Arriving now'),
          (1, '1 minute'),
          (5, '5 minutes'),
          (30, '30 minutes'),
        ];

        for (final (eta, expectedText) in testCases) {
          notifier.state = notifier.state.copyWith(
            estimatedArrivalMinutes: eta,
          );
          expect(notifier.state.formattedETA, equals(expectedText));
        }
      });
    });
  });
}
