// Mocks generated by Mockito 5.4.5 from annotations
// in lucian_rides_app/test/presentation/providers/ride_tracking_notifier_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:dartz/dartz.dart' as _i2;
import 'package:lucian_rides_app/core/errors/app_error.dart' as _i5;
import 'package:lucian_rides_app/domain/entities/ride.dart' as _i6;
import 'package:lucian_rides_app/domain/repositories/ride_repository.dart'
    as _i3;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeEither_0<L, R> extends _i1.SmartFake implements _i2.Either<L, R> {
  _FakeEither_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [RideRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockRideRepository extends _i1.Mock implements _i3.RideRepository {
  MockRideRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.Either<_i5.AppError, _i6.Ride>> requestRide(
          _i6.RideRequestCreate? request) =>
      (super.noSuchMethod(
        Invocation.method(
          #requestRide,
          [request],
        ),
        returnValue: _i4.Future<_i2.Either<_i5.AppError, _i6.Ride>>.value(
            _FakeEither_0<_i5.AppError, _i6.Ride>(
          this,
          Invocation.method(
            #requestRide,
            [request],
          ),
        )),
      ) as _i4.Future<_i2.Either<_i5.AppError, _i6.Ride>>);

  @override
  _i4.Future<_i2.Either<_i5.AppError, _i6.Ride>> getRideStatus(
          String? rideId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getRideStatus,
          [rideId],
        ),
        returnValue: _i4.Future<_i2.Either<_i5.AppError, _i6.Ride>>.value(
            _FakeEither_0<_i5.AppError, _i6.Ride>(
          this,
          Invocation.method(
            #getRideStatus,
            [rideId],
          ),
        )),
      ) as _i4.Future<_i2.Either<_i5.AppError, _i6.Ride>>);

  @override
  _i4.Future<_i2.Either<_i5.AppError, void>> cancelRide(
    String? rideId,
    _i6.CancellationReason? reason,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #cancelRide,
          [
            rideId,
            reason,
          ],
        ),
        returnValue: _i4.Future<_i2.Either<_i5.AppError, void>>.value(
            _FakeEither_0<_i5.AppError, void>(
          this,
          Invocation.method(
            #cancelRide,
            [
              rideId,
              reason,
            ],
          ),
        )),
      ) as _i4.Future<_i2.Either<_i5.AppError, void>>);

  @override
  _i4.Future<_i2.Either<_i5.AppError, List<_i6.RideHistory>>> getRideHistory({
    int? limit = 20,
    int? offset = 0,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getRideHistory,
          [],
          {
            #limit: limit,
            #offset: offset,
          },
        ),
        returnValue:
            _i4.Future<_i2.Either<_i5.AppError, List<_i6.RideHistory>>>.value(
                _FakeEither_0<_i5.AppError, List<_i6.RideHistory>>(
          this,
          Invocation.method(
            #getRideHistory,
            [],
            {
              #limit: limit,
              #offset: offset,
            },
          ),
        )),
      ) as _i4.Future<_i2.Either<_i5.AppError, List<_i6.RideHistory>>>);

  @override
  _i4.Future<_i2.Either<_i5.AppError, _i6.Ride?>> getActiveRide() =>
      (super.noSuchMethod(
        Invocation.method(
          #getActiveRide,
          [],
        ),
        returnValue: _i4.Future<_i2.Either<_i5.AppError, _i6.Ride?>>.value(
            _FakeEither_0<_i5.AppError, _i6.Ride?>(
          this,
          Invocation.method(
            #getActiveRide,
            [],
          ),
        )),
      ) as _i4.Future<_i2.Either<_i5.AppError, _i6.Ride?>>);

  @override
  _i4.Future<_i2.Either<_i5.AppError, _i6.PricingInfo>> calculatePrice(
    _i6.RideLocation? pickup,
    _i6.RideLocation? dropoff,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #calculatePrice,
          [
            pickup,
            dropoff,
          ],
        ),
        returnValue:
            _i4.Future<_i2.Either<_i5.AppError, _i6.PricingInfo>>.value(
                _FakeEither_0<_i5.AppError, _i6.PricingInfo>(
          this,
          Invocation.method(
            #calculatePrice,
            [
              pickup,
              dropoff,
            ],
          ),
        )),
      ) as _i4.Future<_i2.Either<_i5.AppError, _i6.PricingInfo>>);

  @override
  _i4.Future<_i2.Either<_i5.AppError, _i6.RideReceipt>> getRideReceipt(
          String? rideId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getRideReceipt,
          [rideId],
        ),
        returnValue:
            _i4.Future<_i2.Either<_i5.AppError, _i6.RideReceipt>>.value(
                _FakeEither_0<_i5.AppError, _i6.RideReceipt>(
          this,
          Invocation.method(
            #getRideReceipt,
            [rideId],
          ),
        )),
      ) as _i4.Future<_i2.Either<_i5.AppError, _i6.RideReceipt>>);
}
