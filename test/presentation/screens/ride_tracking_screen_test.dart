import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:lucian_rides_app/presentation/screens/ride_tracking_screen.dart';
import 'package:lucian_rides_app/presentation/providers/ride_tracking_notifier.dart';
import 'package:lucian_rides_app/presentation/providers/ride_tracking_state.dart';
import 'package:lucian_rides_app/domain/entities/ride.dart';

import '../providers/ride_tracking_notifier_test.mocks.dart';

void main() {
  group('RideTrackingScreen', () {
    late MockRideRepository mockRideRepository;

    setUp(() {
      mockRideRepository = MockRideRepository();
    });

    Widget createTestWidget(String rideId) {
      return ProviderScope(
        overrides: [
          rideTrackingNotifierProvider.overrideWith(
            (ref) => RideTrackingNotifier(mockRideRepository),
          ),
        ],
        child: MaterialApp(home: RideTrackingScreen(rideId: rideId)),
      );
    }

    testWidgets('should display loading indicator initially', (tester) async {
      await tester.pumpWidget(createTestWidget('test-ride-id'));

      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Track Your Ride'), findsOneWidget);
    });

    testWidgets('should display error message when ride loading fails', (
      tester,
    ) async {
      const error = 'Failed to load ride';

      await tester.pumpWidget(createTestWidget('test-ride-id'));
      await tester.pump();

      // Simulate error state
      final container = ProviderScope.containerOf(
        tester.element(find.byType(RideTrackingScreen)),
      );

      container.read(rideTrackingNotifierProvider.notifier).state =
          const RideTrackingState(errorMessage: error, isLoadingRide: false);

      await tester.pump();

      expect(find.text(error), findsOneWidget);
    });

    testWidgets('should display ride information when loaded', (tester) async {
      final mockRide = Ride(
        id: 'test-ride-id',
        riderId: 'rider-id',
        driverId: 'driver-id',
        status: RideStatus.accepted,
        pickupLocation: const RideLocation(
          name: 'Pickup Location',
          latitude: 14.0,
          longitude: -61.0,
        ),
        dropoffLocation: const RideLocation(
          name: 'Dropoff Location',
          latitude: 14.1,
          longitude: -61.1,
        ),
        fixedFare: 25.0,
        paymentStatus: PaymentStatus.pending,
        createdAt: DateTime.now(),
      );

      await tester.pumpWidget(createTestWidget('test-ride-id'));
      await tester.pump();

      // Simulate loaded state
      final container = ProviderScope.containerOf(
        tester.element(find.byType(RideTrackingScreen)),
      );

      container
          .read(rideTrackingNotifierProvider.notifier)
          .state = RideTrackingState(
        activeRide: mockRide,
        isLoadingRide: false,
        connectionStatus: ConnectionStatus.connected,
        driverInfo: const DriverInfo(
          id: 'driver-id',
          name: 'John Doe',
          rating: 4.8,
          vehicleInfo: VehicleInfo(
            make: 'Toyota',
            model: 'Camry',
            color: 'Silver',
            licensePlate: 'SLU-1234',
          ),
        ),
      );

      await tester.pump();

      expect(find.text('Driver on the way'), findsOneWidget);
      expect(find.text('Pickup Location'), findsOneWidget);
      expect(find.text('Dropoff Location'), findsOneWidget);
      expect(find.text('\$25.00'), findsOneWidget);
      expect(find.text('John Doe'), findsOneWidget);
      expect(find.text('Silver Toyota Camry'), findsOneWidget);
    });

    testWidgets('should show cancel button when ride can be cancelled', (
      tester,
    ) async {
      final mockRide = Ride(
        id: 'test-ride-id',
        riderId: 'rider-id',
        driverId: 'driver-id',
        status: RideStatus.accepted,
        pickupLocation: const RideLocation(
          name: 'Pickup Location',
          latitude: 14.0,
          longitude: -61.0,
        ),
        dropoffLocation: const RideLocation(
          name: 'Dropoff Location',
          latitude: 14.1,
          longitude: -61.1,
        ),
        fixedFare: 25.0,
        paymentStatus: PaymentStatus.pending,
        createdAt: DateTime.now(),
      );

      await tester.pumpWidget(createTestWidget('test-ride-id'));
      await tester.pump();

      // Simulate loaded state with cancellable ride
      final container = ProviderScope.containerOf(
        tester.element(find.byType(RideTrackingScreen)),
      );

      container
          .read(rideTrackingNotifierProvider.notifier)
          .state = RideTrackingState(
        activeRide: mockRide,
        isLoadingRide: false,
        connectionStatus: ConnectionStatus.connected,
      );

      await tester.pump();

      expect(find.text('Cancel Ride'), findsOneWidget);
      expect(find.text('Get Help'), findsOneWidget);
    });

    testWidgets('should display connection status', (tester) async {
      final mockRide = Ride(
        id: 'test-ride-id',
        riderId: 'rider-id',
        status: RideStatus.requested,
        pickupLocation: const RideLocation(
          name: 'Pickup Location',
          latitude: 14.0,
          longitude: -61.0,
        ),
        dropoffLocation: const RideLocation(
          name: 'Dropoff Location',
          latitude: 14.1,
          longitude: -61.1,
        ),
        fixedFare: 25.0,
        paymentStatus: PaymentStatus.pending,
        createdAt: DateTime.now(),
      );

      await tester.pumpWidget(createTestWidget('test-ride-id'));
      await tester.pump();

      // Test different connection statuses
      final container = ProviderScope.containerOf(
        tester.element(find.byType(RideTrackingScreen)),
      );

      final notifier = container.read(rideTrackingNotifierProvider.notifier);

      // Test connected status
      notifier.state = RideTrackingState(
        activeRide: mockRide,
        connectionStatus: ConnectionStatus.connected,
      );
      await tester.pump();
      expect(find.text('Connected'), findsOneWidget);

      // Test disconnected status
      notifier.state = RideTrackingState(
        activeRide: mockRide,
        connectionStatus: ConnectionStatus.disconnected,
      );
      await tester.pump();
      expect(find.text('Disconnected'), findsOneWidget);

      // Test connecting status
      notifier.state = RideTrackingState(
        activeRide: mockRide,
        connectionStatus: ConnectionStatus.connecting,
      );
      await tester.pump();
      expect(find.text('Connecting...'), findsOneWidget);
    });

    testWidgets('should display ETA when available', (tester) async {
      final mockRide = Ride(
        id: 'test-ride-id',
        riderId: 'rider-id',
        driverId: 'driver-id',
        status: RideStatus.accepted,
        pickupLocation: const RideLocation(
          name: 'Pickup Location',
          latitude: 14.0,
          longitude: -61.0,
        ),
        dropoffLocation: const RideLocation(
          name: 'Dropoff Location',
          latitude: 14.1,
          longitude: -61.1,
        ),
        fixedFare: 25.0,
        paymentStatus: PaymentStatus.pending,
        createdAt: DateTime.now(),
      );

      await tester.pumpWidget(createTestWidget('test-ride-id'));
      await tester.pump();

      // Simulate state with ETA
      final container = ProviderScope.containerOf(
        tester.element(find.byType(RideTrackingScreen)),
      );

      container
          .read(rideTrackingNotifierProvider.notifier)
          .state = RideTrackingState(
        activeRide: mockRide,
        estimatedArrivalMinutes: 5,
        distanceKm: 2.5,
        connectionStatus: ConnectionStatus.connected,
      );

      await tester.pump();

      expect(find.text('ETA: 5 minutes'), findsOneWidget);
      expect(find.text('Estimated Arrival'), findsOneWidget);
      expect(find.text('2.5 km'), findsOneWidget);
    });
  });
}
