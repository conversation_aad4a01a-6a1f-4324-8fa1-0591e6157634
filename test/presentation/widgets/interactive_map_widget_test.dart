import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:lucian_rides_app/presentation/widgets/interactive_map_widget.dart';
import 'package:lucian_rides_app/domain/entities/location_models.dart';
import 'package:lucian_rides_app/domain/entities/ride.dart';

void main() {
  group('InteractiveMapWidget', () {
    testWidgets('should render without crashing', (WidgetTester tester) async {
      // Arrange
      const currentLocation = LocationCoordinates(
        latitude: 13.9094,
        longitude: -60.9789,
      );

      // Act
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: InteractiveMapWidget(
                currentLocation: currentLocation,
                height: 300,
              ),
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(InteractiveMapWidget), findsOneWidget);
    });

    testWidgets(
      'should show pickup and dropoff markers when locations provided',
      (WidgetTester tester) async {
        // Arrange
        const currentLocation = LocationCoordinates(
          latitude: 13.9094,
          longitude: -60.9789,
        );

        const pickupLocation = RideLocation(
          name: 'Pickup Point',
          latitude: 13.9100,
          longitude: -60.9800,
        );

        const dropoffLocation = RideLocation(
          name: 'Dropoff Point',
          latitude: 13.9200,
          longitude: -60.9700,
        );

        // Act
        await tester.pumpWidget(
          ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: InteractiveMapWidget(
                  currentLocation: currentLocation,
                  pickupLocation: pickupLocation,
                  dropoffLocation: dropoffLocation,
                  showRoute: true,
                  height: 300,
                ),
              ),
            ),
          ),
        );

        // Assert
        expect(find.byType(InteractiveMapWidget), findsOneWidget);
      },
    );

    testWidgets('should show map controls when enabled', (
      WidgetTester tester,
    ) async {
      // Arrange
      const currentLocation = LocationCoordinates(
        latitude: 13.9094,
        longitude: -60.9789,
      );

      // Act
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: InteractiveMapWidget(
                currentLocation: currentLocation,
                showControls: true,
                height: 300,
              ),
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(InteractiveMapWidget), findsOneWidget);
    });

    testWidgets('should handle location selection callbacks', (
      WidgetTester tester,
    ) async {
      // Arrange
      const currentLocation = LocationCoordinates(
        latitude: 13.9094,
        longitude: -60.9789,
      );

      // Act
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: InteractiveMapWidget(
                currentLocation: currentLocation,
                onPickupLocationChanged: (location) {
                  // Handle pickup location change
                },
                onDropoffLocationChanged: (location) {
                  // Handle dropoff location change
                },
                height: 300,
              ),
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(InteractiveMapWidget), findsOneWidget);
      // Note: Actual callback testing would require more complex interaction simulation
    });

    testWidgets(
      'should expand to fill available space when expandToFill is true',
      (WidgetTester tester) async {
        // Arrange
        const currentLocation = LocationCoordinates(
          latitude: 13.9094,
          longitude: -60.9789,
        );

        // Act
        await tester.pumpWidget(
          ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: InteractiveMapWidget(
                  currentLocation: currentLocation,
                  expandToFill: true,
                ),
              ),
            ),
          ),
        );

        // Assert
        expect(find.byType(InteractiveMapWidget), findsOneWidget);
      },
    );
  });
}
