import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:lucian_rides_app/services/auth/auth_service_impl.dart';
import 'package:lucian_rides_app/domain/repositories/auth_repository.dart';
import 'package:lucian_rides_app/data/datasources/storage_service.dart';
import 'package:lucian_rides_app/domain/entities/user.dart';
import 'package:lucian_rides_app/data/models/auth_result.dart';
import 'package:lucian_rides_app/core/errors/app_error.dart';

import 'auth_service_impl_test.mocks.dart';

@GenerateMocks([AuthRepository, StorageService])
void main() {
  group('AuthServiceImpl', () {
    late MockAuthRepository mockAuthRepository;
    late MockStorageService mockStorageService;
    late AuthServiceImpl authService;

    setUp(() {
      mockAuthRepository = MockAuthRepository();
      mockStorageService = MockStorageService();
      authService = AuthServiceImpl(
        authRepository: mockAuthRepository,
        storageService: mockStorageService,
      );
    });

    group('login', () {
      test('should return success result on successful login', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';
        const token = 'test-token';

        final testUser = User(
          id: '1',
          email: email,
          name: 'Test User',
          userType: UserType.rider,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          isActive: true,
          language: 'en',
        );

        when(
          mockAuthRepository.login(email, password),
        ).thenAnswer((_) async => token);
        when(
          mockAuthRepository.getCurrentUser(),
        ).thenAnswer((_) async => testUser);
        when(
          mockStorageService.storeToken(any, expiresAt: anyNamed('expiresAt')),
        ).thenAnswer((_) async {});
        when(mockStorageService.storeUserData(any)).thenAnswer((_) async {});

        // Act
        final result = await authService.login(email, password);

        // Assert
        expect(result, isA<AuthSuccess>());
        result.when(
          success: (token, user) {
            expect(token, equals('test-token'));
            expect(user, equals(testUser));
          },
          failure: (message, errorCode) =>
              fail('Expected success but got failure'),
        );

        verify(mockAuthRepository.login(email, password)).called(1);
        verify(mockAuthRepository.getCurrentUser()).called(1);
        verify(
          mockStorageService.storeToken(
            token,
            expiresAt: anyNamed('expiresAt'),
          ),
        ).called(1);
        verify(mockStorageService.storeUserData(testUser)).called(1);
      });

      test('should return failure result with invalid input', () async {
        // Act
        final result = await authService.login('', 'password123');

        // Assert
        expect(result, isA<AuthFailure>());
        result.when(
          success: (token, user) => fail('Expected failure but got success'),
          failure: (message, errorCode) {
            expect(message, equals('Email and password are required'));
            expect(errorCode, equals('INVALID_INPUT'));
          },
        );

        verifyNever(mockAuthRepository.login(any, any));
      });

      test(
        'should return failure result on repository authentication error',
        () async {
          // Arrange
          const email = '<EMAIL>';
          const password = 'wrongpassword';

          when(mockAuthRepository.login(email, password)).thenThrow(
            const AppError.authentication(
              message: 'Invalid credentials',
              errorCode: 'INVALID_CREDENTIALS',
            ),
          );

          // Act
          final result = await authService.login(email, password);

          // Assert
          expect(result, isA<AuthFailure>());
          result.when(
            success: (token, user) => fail('Expected failure but got success'),
            failure: (message, errorCode) {
              expect(message, equals('Invalid credentials'));
              expect(errorCode, equals('INVALID_CREDENTIALS'));
            },
          );
        },
      );

      test('should return failure result on network error', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';

        when(mockAuthRepository.login(email, password)).thenThrow(
          const AppError.network(message: 'Network connection failed'),
        );

        // Act
        final result = await authService.login(email, password);

        // Assert
        expect(result, isA<AuthFailure>());
        result.when(
          success: (token, user) => fail('Expected failure but got success'),
          failure: (message, errorCode) {
            expect(message, equals('Network connection failed'));
            expect(errorCode, equals('NETWORK_ERROR'));
          },
        );
      });

      test('should return failure result on unexpected error', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';

        when(
          mockAuthRepository.login(email, password),
        ).thenThrow(Exception('Unexpected error'));

        // Act
        final result = await authService.login(email, password);

        // Assert
        expect(result, isA<AuthFailure>());
        result.when(
          success: (token, user) => fail('Expected failure but got success'),
          failure: (message, errorCode) {
            expect(message, contains('Login failed'));
            expect(errorCode, equals('UNKNOWN_ERROR'));
          },
        );
      });
    });

    group('register', () {
      test('should return success result on successful registration', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';
        const name = 'Test User';
        const userType = UserType.rider;
        const token = 'test-token';

        final testUser = User(
          id: '1',
          email: email,
          name: name,
          userType: userType,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          isActive: true,
          language: 'en',
        );

        when(
          mockAuthRepository.register(
            email: email,
            password: password,
            name: name,
            userType: userType,
            phone: null,
          ),
        ).thenAnswer((_) async => token);
        when(
          mockAuthRepository.getCurrentUser(),
        ).thenAnswer((_) async => testUser);
        when(
          mockStorageService.storeToken(any, expiresAt: anyNamed('expiresAt')),
        ).thenAnswer((_) async {});
        when(mockStorageService.storeUserData(any)).thenAnswer((_) async {});

        // Act
        final result = await authService.register(
          email: email,
          password: password,
          name: name,
          userType: userType,
        );

        // Assert
        expect(result, isA<AuthSuccess>());
        result.when(
          success: (token, user) {
            expect(token, equals('test-token'));
            expect(user, equals(testUser));
          },
          failure: (message, errorCode) =>
              fail('Expected success but got failure'),
        );

        verify(
          mockAuthRepository.register(
            email: email,
            password: password,
            name: name,
            userType: userType,
            phone: null,
          ),
        ).called(1);
        verify(mockAuthRepository.getCurrentUser()).called(1);
        verify(
          mockStorageService.storeToken(
            token,
            expiresAt: anyNamed('expiresAt'),
          ),
        ).called(1);
        verify(mockStorageService.storeUserData(testUser)).called(1);
      });

      test('should return failure result with invalid input', () async {
        // Act
        final result = await authService.register(
          email: '',
          password: 'password123',
          name: 'Test User',
          userType: UserType.rider,
        );

        // Assert
        expect(result, isA<AuthFailure>());
        result.when(
          success: (token, user) => fail('Expected failure but got success'),
          failure: (message, errorCode) {
            expect(message, equals('Email, password, and name are required'));
            expect(errorCode, equals('INVALID_INPUT'));
          },
        );

        verifyNever(
          mockAuthRepository.register(
            email: anyNamed('email'),
            password: anyNamed('password'),
            name: anyNamed('name'),
            userType: anyNamed('userType'),
            phone: anyNamed('phone'),
          ),
        );
      });

      test('should return failure result on validation error', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';
        const name = 'Test User';
        const userType = UserType.rider;

        when(
          mockAuthRepository.register(
            email: email,
            password: password,
            name: name,
            userType: userType,
            phone: null,
          ),
        ).thenThrow(
          const AppError.validation(
            message: 'Email already exists',
            fieldErrors: {'email': 'Email already exists'},
          ),
        );

        // Act
        final result = await authService.register(
          email: email,
          password: password,
          name: name,
          userType: userType,
        );

        // Assert
        expect(result, isA<AuthFailure>());
        result.when(
          success: (token, user) => fail('Expected failure but got success'),
          failure: (message, errorCode) {
            expect(message, equals('Email already exists'));
            expect(errorCode, equals('VALIDATION_ERROR'));
          },
        );
      });
    });

    group('logout', () {
      test('should call repository logout', () async {
        // Arrange
        when(mockAuthRepository.logout()).thenAnswer((_) async {});

        // Act
        await authService.logout();

        // Assert
        verify(mockAuthRepository.logout()).called(1);
      });

      test('should clear storage even when repository logout fails', () async {
        // Arrange
        when(mockAuthRepository.logout()).thenThrow(Exception('Network error'));
        when(mockStorageService.clearToken()).thenAnswer((_) async {});
        when(mockStorageService.clearRefreshToken()).thenAnswer((_) async {});
        when(mockStorageService.clearUserData()).thenAnswer((_) async {});
        when(mockStorageService.clearAllCachedData()).thenAnswer((_) async {});

        // Act & Assert
        expect(() => authService.logout(), throwsException);

        verify(mockAuthRepository.logout()).called(1);
        verify(mockStorageService.clearToken()).called(1);
        verify(mockStorageService.clearRefreshToken()).called(1);
        verify(mockStorageService.clearUserData()).called(1);
        verify(mockStorageService.clearAllCachedData()).called(1);
      });
    });

    group('getCurrentUser', () {
      test('should return user when authenticated', () async {
        // Arrange
        final testUser = User(
          id: '1',
          email: '<EMAIL>',
          name: 'Test User',
          userType: UserType.rider,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          isActive: true,
          language: 'en',
        );

        when(mockStorageService.hasToken()).thenAnswer((_) async => true);
        when(
          mockAuthRepository.getCurrentUser(),
        ).thenAnswer((_) async => testUser);

        // Act
        final result = await authService.getCurrentUser();

        // Assert
        expect(result, equals(testUser));
        verify(mockAuthRepository.getCurrentUser()).called(1);
      });

      test('should return null when not authenticated', () async {
        // Arrange
        when(mockStorageService.hasToken()).thenAnswer((_) async => false);

        // Act
        final result = await authService.getCurrentUser();

        // Assert
        expect(result, isNull);
        verifyNever(mockAuthRepository.getCurrentUser());
      });

      test('should return null and clear user data on error', () async {
        // Arrange
        when(mockStorageService.hasToken()).thenAnswer((_) async => true);
        when(mockAuthRepository.getCurrentUser()).thenThrow(Exception('Error'));
        when(mockStorageService.clearUserData()).thenAnswer((_) async {});

        // Act
        final result = await authService.getCurrentUser();

        // Assert
        expect(result, isNull);
        verify(mockStorageService.clearUserData()).called(1);
      });
    });

    group('verifyToken', () {
      test(
        'should return false and clear data when token is expired',
        () async {
          // Arrange
          when(
            mockStorageService.isTokenExpired(),
          ).thenAnswer((_) async => true);
          when(mockStorageService.clearToken()).thenAnswer((_) async {});
          when(mockStorageService.clearUserData()).thenAnswer((_) async {});

          // Act
          final result = await authService.verifyToken();

          // Assert
          expect(result, isFalse);
          verify(mockStorageService.isTokenExpired()).called(1);
          verify(mockStorageService.clearToken()).called(1);
          verify(mockStorageService.clearUserData()).called(1);
          verifyNever(mockAuthRepository.verifyToken());
        },
      );

      test('should return true when token is valid', () async {
        // Arrange
        when(
          mockStorageService.isTokenExpired(),
        ).thenAnswer((_) async => false);
        when(
          mockStorageService.shouldRefreshToken(),
        ).thenAnswer((_) async => false);
        when(mockAuthRepository.verifyToken()).thenAnswer((_) async => true);

        // Act
        final result = await authService.verifyToken();

        // Assert
        expect(result, isTrue);
        verify(mockStorageService.isTokenExpired()).called(1);
        verify(mockStorageService.shouldRefreshToken()).called(1);
        verify(mockAuthRepository.verifyToken()).called(1);
      });

      test(
        'should return false and clear data when verification fails',
        () async {
          // Arrange
          when(
            mockStorageService.isTokenExpired(),
          ).thenAnswer((_) async => false);
          when(
            mockStorageService.shouldRefreshToken(),
          ).thenAnswer((_) async => false);
          when(
            mockAuthRepository.verifyToken(),
          ).thenThrow(Exception('Network error'));
          when(mockStorageService.clearToken()).thenAnswer((_) async {});
          when(mockStorageService.clearUserData()).thenAnswer((_) async {});

          // Act
          final result = await authService.verifyToken();

          // Assert
          expect(result, isFalse);
          verify(mockStorageService.clearToken()).called(1);
          verify(mockStorageService.clearUserData()).called(1);
        },
      );
    });

    group('isAuthenticated', () {
      test('should return false when no token exists', () async {
        // Arrange
        when(mockStorageService.hasToken()).thenAnswer((_) async => false);

        // Act
        final result = await authService.isAuthenticated();

        // Assert
        expect(result, isFalse);
        verify(mockStorageService.hasToken()).called(1);
        verifyNever(mockStorageService.isTokenExpired());
      });

      test(
        'should return false and clear data when token is expired',
        () async {
          // Arrange
          when(mockStorageService.hasToken()).thenAnswer((_) async => true);
          when(
            mockStorageService.isTokenExpired(),
          ).thenAnswer((_) async => true);
          when(mockStorageService.clearToken()).thenAnswer((_) async {});
          when(mockStorageService.clearUserData()).thenAnswer((_) async {});

          // Act
          final result = await authService.isAuthenticated();

          // Assert
          expect(result, isFalse);
          verify(mockStorageService.hasToken()).called(1);
          verify(mockStorageService.isTokenExpired()).called(1);
          verify(mockStorageService.clearToken()).called(1);
          verify(mockStorageService.clearUserData()).called(1);
        },
      );

      test('should return true when token is valid', () async {
        // Arrange
        when(mockStorageService.hasToken()).thenAnswer((_) async => true);
        when(
          mockStorageService.isTokenExpired(),
        ).thenAnswer((_) async => false);
        when(
          mockStorageService.shouldRefreshToken(),
        ).thenAnswer((_) async => false);
        when(mockAuthRepository.verifyToken()).thenAnswer((_) async => true);

        // Act
        final result = await authService.isAuthenticated();

        // Assert
        expect(result, isTrue);
      });
    });

    group('hasValidSession', () {
      test('should return true when token exists', () async {
        // Arrange
        when(mockStorageService.hasToken()).thenAnswer((_) async => true);

        // Act
        final result = await authService.hasValidSession();

        // Assert
        expect(result, isTrue);
        verify(mockStorageService.hasToken()).called(1);
      });

      test('should return false when no token exists', () async {
        // Arrange
        when(mockStorageService.hasToken()).thenAnswer((_) async => false);

        // Act
        final result = await authService.hasValidSession();

        // Assert
        expect(result, isFalse);
        verify(mockStorageService.hasToken()).called(1);
      });

      test('should return false on storage error', () async {
        // Arrange
        when(
          mockStorageService.hasToken(),
        ).thenThrow(Exception('Storage error'));

        // Act
        final result = await authService.hasValidSession();

        // Assert
        expect(result, isFalse);
      });
    });

    group('getStoredToken', () {
      test('should return token when available', () async {
        // Arrange
        const token = 'test-token';
        when(mockStorageService.getToken()).thenAnswer((_) async => token);

        // Act
        final result = await authService.getStoredToken();

        // Assert
        expect(result, equals(token));
        verify(mockStorageService.getToken()).called(1);
      });

      test('should return null on storage error', () async {
        // Arrange
        when(
          mockStorageService.getToken(),
        ).thenThrow(Exception('Storage error'));

        // Act
        final result = await authService.getStoredToken();

        // Assert
        expect(result, isNull);
      });
    });
  });
}
