import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
// import 'package:dartz/dartz.dart'; // Unused import
// import 'package:lucian_rides_app/data/datasources/storage_service.dart'; // Unused import
import 'package:lucian_rides_app/services/location/location_service_impl.dart';
import 'package:lucian_rides_app/services/location/location_service.dart';
import 'package:lucian_rides_app/domain/entities/location_models.dart';
import 'package:lucian_rides_app/core/errors/app_error.dart';

// Use existing mocks
import 'location_service_impl_test.mocks.dart';

void main() {
  late LocationService locationService;
  late MockStorageService mockStorageService;

  setUp(() {
    mockStorageService = MockStorageService();
    locationService = LocationServiceImpl(storageService: mockStorageService);
  });

  group('Geocoding Services', () {
    test(
      'getCoordinatesFromAddress should handle popular St. Lucia locations',
      () async {
        // Arrange
        const String address = 'Rodney Bay';

        // Mock the storage service methods that will be called
        when(
          mockStorageService.getCachedData(any),
        ).thenAnswer((_) async => {'locations': []});

        when(
          mockStorageService.storeCachedData(any, any),
        ).thenAnswer((_) async => {});

        // Act
        final result = await locationService.getCoordinatesFromAddress(address);

        // Assert
        expect(result.isRight(), true);
        result.fold((error) => fail('Should not return error'), (coordinates) {
          expect(coordinates.latitude, 14.0783);
          expect(coordinates.longitude, -60.9564);
        });
      },
    );

    test(
      'getAddressFromCoordinates should return address for St. Lucia coordinates',
      () async {
        // Arrange
        const coordinates = LocationCoordinates(
          latitude: 14.0783,
          longitude: -60.9564,
        );

        // Act
        final result = await locationService.getAddressFromCoordinates(
          coordinates,
        );

        // Assert
        expect(result.isRight(), true);
        result.fold((error) => fail('Should not return error'), (address) {
          expect(address, contains('Rodney Bay'));
          expect(address, contains('St. Lucia'));
        });
      },
    );

    test(
      'getAddressFromCoordinates should reject coordinates outside St. Lucia',
      () async {
        // Arrange
        const coordinates = LocationCoordinates(
          latitude: 40.7128, // New York City
          longitude: -74.0060,
        );

        // Act
        final result = await locationService.getAddressFromCoordinates(
          coordinates,
        );

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (error) {
            expect(error, isA<AppError>());
            expect(error.message, contains('outside service area'));
          },
          (_) => fail('Should return error for coordinates outside St. Lucia'),
        );
      },
    );
  });

  group('Location Suggestions', () {
    test(
      'getLocationSuggestions should return mixed suggestions when query is empty',
      () async {
        // Arrange
        when(
          mockStorageService.getCachedData(any),
        ).thenAnswer((_) async => {'locations': []});

        // Act
        final result = await locationService.getLocationSuggestions('');

        // Assert
        expect(result.isRight(), true);
        result.fold((error) => fail('Should not return error'), (suggestions) {
          expect(suggestions.isNotEmpty, true);
          // Should return popular locations when no recent locations exist
          expect(suggestions.first.mainText, isNotEmpty);
          expect(suggestions.first.coordinates, isNotNull);
        });
      },
    );

    test('getLocationSuggestions should filter by query', () async {
      // Arrange
      const String query = 'Soufrière';

      when(
        mockStorageService.getCachedData(any),
      ).thenAnswer((_) async => {'locations': []});

      // Act
      final result = await locationService.getLocationSuggestions(query);

      // Assert
      expect(result.isRight(), true);
      result.fold((error) => fail('Should not return error'), (suggestions) {
        expect(suggestions.isNotEmpty, true);
        expect(
          suggestions.first.mainText.toLowerCase(),
          contains(query.toLowerCase()),
        );
      });
    });
  });

  group('Recent Locations', () {
    test('getRecentLocations should return locations from storage', () async {
      // Arrange
      final recentLocation = {
        'id': 'test-id',
        'name': 'Test Location',
        'address': 'Test Address, St. Lucia',
        'coordinates': {'latitude': 14.0, 'longitude': -61.0},
        'lastUsed': DateTime.now().toIso8601String(),
        'type': 'pickup',
      };

      when(mockStorageService.getCachedData(any)).thenAnswer(
        (_) async => {
          'locations': [recentLocation],
        },
      );

      // Act
      final result = await locationService.getRecentLocations();

      // Assert
      expect(result.isRight(), true);
      result.fold((error) => fail('Should not return error'), (locations) {
        expect(locations.length, 1);
        expect(locations.first.name, 'Test Location');
        expect(locations.first.type, RecentLocationType.pickup);
      });
    });

    test('getRecentLocations should filter by type', () async {
      // Arrange
      final pickupLocation = {
        'id': 'pickup-id',
        'name': 'Pickup Location',
        'address': 'Pickup Address, St. Lucia',
        'coordinates': {'latitude': 14.0, 'longitude': -61.0},
        'lastUsed': DateTime.now().toIso8601String(),
        'type': 'pickup',
      };

      final dropoffLocation = {
        'id': 'dropoff-id',
        'name': 'Dropoff Location',
        'address': 'Dropoff Address, St. Lucia',
        'coordinates': {'latitude': 14.1, 'longitude': -60.9},
        'lastUsed': DateTime.now().toIso8601String(),
        'type': 'dropoff',
      };

      when(mockStorageService.getCachedData(any)).thenAnswer(
        (_) async => {
          'locations': [pickupLocation, dropoffLocation],
        },
      );

      // Act
      final result = await locationService.getRecentLocations(
        type: RecentLocationType.pickup,
      );

      // Assert
      expect(result.isRight(), true);
      result.fold((error) => fail('Should not return error'), (locations) {
        expect(locations.length, 1);
        expect(locations.first.name, 'Pickup Location');
        expect(locations.first.type, RecentLocationType.pickup);
      });
    });

    test('clearRecentLocations should clear locations from storage', () async {
      // Arrange
      when(mockStorageService.clearCachedData(any)).thenAnswer((_) async => {});

      // Act
      final result = await locationService.clearRecentLocations();

      // Assert
      expect(result.isRight(), true);
      verify(mockStorageService.clearCachedData(any)).called(1);
    });
  });
}

