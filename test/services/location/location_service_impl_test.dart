import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:dartz/dartz.dart';
import 'package:lucian_rides_app/data/datasources/storage_service.dart';
import 'package:lucian_rides_app/services/location/location_service_impl.dart';
import 'package:lucian_rides_app/services/location/location_service.dart';
import 'package:lucian_rides_app/domain/entities/location_models.dart';
import 'package:lucian_rides_app/domain/entities/ride.dart';
import 'package:lucian_rides_app/core/errors/app_error.dart';

// Generate mocks
@GenerateMocks([StorageService])
import 'location_service_impl_test.mocks.dart';

void main() {
  late LocationService locationService;
  late MockStorageService mockStorageService;

  setUp(() {
    mockStorageService = MockStorageService();
    locationService = LocationServiceImpl(storageService: mockStorageService);
  });

  group('LocationService', () {
    test(
      'storeRecentLocation should store location in storage service',
      () async {
        // Arrange
        final recentLocation = RecentLocation(
          id: 'test-id',
          name: 'Test Location',
          address: 'Test Address, St. Lucia',
          coordinates: const LocationCoordinates(
            latitude: 14.0,
            longitude: -61.0,
          ),
          lastUsed: DateTime.now(),
          type: RecentLocationType.pickup,
        );

        when(
          mockStorageService.getCachedData(any),
        ).thenAnswer((_) async => {'locations': []});

        when(
          mockStorageService.storeCachedData(any, any),
        ).thenAnswer((_) async => {});

        // Act
        final result = await locationService.storeRecentLocation(
          recentLocation,
        );

        // Assert
        expect(result, isA<Right<AppError, void>>());
        verify(mockStorageService.storeCachedData(any, any)).called(1);
      },
    );

    test(
      'getRecentLocations should return locations from storage service',
      () async {
        // Arrange
        final recentLocation = {
          'id': 'test-id',
          'name': 'Test Location',
          'address': 'Test Address, St. Lucia',
          'coordinates': {'latitude': 14.0, 'longitude': -61.0},
          'lastUsed': DateTime.now().toIso8601String(),
          'type': 'pickup',
        };

        when(mockStorageService.getCachedData(any)).thenAnswer(
          (_) async => {
            'locations': [recentLocation],
          },
        );

        // Act
        final result = await locationService.getRecentLocations();

        // Assert
        expect(result, isA<Right<AppError, List<RecentLocation>>>());
        result.fold((error) => fail('Should not return error'), (locations) {
          expect(locations.length, 1);
          expect(locations.first.id, 'test-id');
          expect(locations.first.name, 'Test Location');
        });
      },
    );

    test(
      'clearRecentLocations should clear locations from storage service',
      () async {
        // Arrange
        when(
          mockStorageService.clearCachedData(any),
        ).thenAnswer((_) async => {});

        // Act
        final result = await locationService.clearRecentLocations();

        // Assert
        expect(result, isA<Right<AppError, void>>());
        verify(mockStorageService.clearCachedData(any)).called(1);
      },
    );

    test('isLocationInServiceArea should validate St. Lucia coordinates', () {
      // Arrange
      final validCoordinates = const LocationCoordinates(
        latitude: 13.9,
        longitude: -60.9,
      ); // Within St. Lucia

      final invalidCoordinates = const LocationCoordinates(
        latitude: 40.7,
        longitude: -74.0,
      ); // New York City

      // Act & Assert
      expect(locationService.isLocationInServiceArea(validCoordinates), true);
      expect(
        locationService.isLocationInServiceArea(invalidCoordinates),
        false,
      );
    });

    test(
      'rideLocationToCoordinates should convert RideLocation to LocationCoordinates',
      () {
        // Arrange
        final rideLocation = RideLocation(
          name: 'Test Location',
          address: 'Test Address',
          latitude: 13.9,
          longitude: -60.9,
        );

        // Act
        final result = locationService.rideLocationToCoordinates(rideLocation);

        // Assert
        expect(result.latitude, 13.9);
        expect(result.longitude, -60.9);
      },
    );
  });
}
