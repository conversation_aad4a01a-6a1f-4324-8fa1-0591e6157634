// Mocks generated by Mockito 5.4.5 from annotations
// in lucian_rides_app/test/services/location/location_service_impl_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:lucian_rides_app/data/datasources/storage_service.dart' as _i2;
import 'package:lucian_rides_app/domain/entities/user.dart' as _i4;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [StorageService].
///
/// See the documentation for Mockito's code generation for more information.
class MockStorageService extends _i1.Mock implements _i2.StorageService {
  MockStorageService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<void> storeToken(
    String? token, {
    DateTime? expiresAt,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #storeToken,
          [token],
          {#expiresAt: expiresAt},
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> storeRefreshToken(String? refreshToken) =>
      (super.noSuchMethod(
        Invocation.method(
          #storeRefreshToken,
          [refreshToken],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<String?> getToken() => (super.noSuchMethod(
        Invocation.method(
          #getToken,
          [],
        ),
        returnValue: _i3.Future<String?>.value(),
      ) as _i3.Future<String?>);

  @override
  _i3.Future<String?> getRefreshToken() => (super.noSuchMethod(
        Invocation.method(
          #getRefreshToken,
          [],
        ),
        returnValue: _i3.Future<String?>.value(),
      ) as _i3.Future<String?>);

  @override
  _i3.Future<DateTime?> getTokenExpiry() => (super.noSuchMethod(
        Invocation.method(
          #getTokenExpiry,
          [],
        ),
        returnValue: _i3.Future<DateTime?>.value(),
      ) as _i3.Future<DateTime?>);

  @override
  _i3.Future<bool> isTokenExpired() => (super.noSuchMethod(
        Invocation.method(
          #isTokenExpired,
          [],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<bool> shouldRefreshToken() => (super.noSuchMethod(
        Invocation.method(
          #shouldRefreshToken,
          [],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<void> clearToken() => (super.noSuchMethod(
        Invocation.method(
          #clearToken,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> clearRefreshToken() => (super.noSuchMethod(
        Invocation.method(
          #clearRefreshToken,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> storeUserData(_i4.User? user) => (super.noSuchMethod(
        Invocation.method(
          #storeUserData,
          [user],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<_i4.User?> getUserData() => (super.noSuchMethod(
        Invocation.method(
          #getUserData,
          [],
        ),
        returnValue: _i3.Future<_i4.User?>.value(),
      ) as _i3.Future<_i4.User?>);

  @override
  _i3.Future<void> clearUserData() => (super.noSuchMethod(
        Invocation.method(
          #clearUserData,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> storeCachedData(
    String? key,
    Map<String, dynamic>? data,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #storeCachedData,
          [
            key,
            data,
          ],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<Map<String, dynamic>?> getCachedData(String? key) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCachedData,
          [key],
        ),
        returnValue: _i3.Future<Map<String, dynamic>?>.value(),
      ) as _i3.Future<Map<String, dynamic>?>);

  @override
  _i3.Future<bool> isCachedDataValid(String? key) => (super.noSuchMethod(
        Invocation.method(
          #isCachedDataValid,
          [key],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<void> clearCachedData(String? key) => (super.noSuchMethod(
        Invocation.method(
          #clearCachedData,
          [key],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> clearAllCachedData() => (super.noSuchMethod(
        Invocation.method(
          #clearAllCachedData,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> storeLastSync(DateTime? timestamp) => (super.noSuchMethod(
        Invocation.method(
          #storeLastSync,
          [timestamp],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<DateTime?> getLastSync() => (super.noSuchMethod(
        Invocation.method(
          #getLastSync,
          [],
        ),
        returnValue: _i3.Future<DateTime?>.value(),
      ) as _i3.Future<DateTime?>);

  @override
  _i3.Future<bool> needsSync() => (super.noSuchMethod(
        Invocation.method(
          #needsSync,
          [],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<void> storeLanguage(String? language) => (super.noSuchMethod(
        Invocation.method(
          #storeLanguage,
          [language],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<String?> getLanguage() => (super.noSuchMethod(
        Invocation.method(
          #getLanguage,
          [],
        ),
        returnValue: _i3.Future<String?>.value(),
      ) as _i3.Future<String?>);

  @override
  _i3.Future<void> storeThemeMode(String? themeMode) => (super.noSuchMethod(
        Invocation.method(
          #storeThemeMode,
          [themeMode],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<String?> getThemeMode() => (super.noSuchMethod(
        Invocation.method(
          #getThemeMode,
          [],
        ),
        returnValue: _i3.Future<String?>.value(),
      ) as _i3.Future<String?>);

  @override
  _i3.Future<void> storeOnboardingCompleted(bool? completed) =>
      (super.noSuchMethod(
        Invocation.method(
          #storeOnboardingCompleted,
          [completed],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<bool> isOnboardingCompleted() => (super.noSuchMethod(
        Invocation.method(
          #isOnboardingCompleted,
          [],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<void> clearAll() => (super.noSuchMethod(
        Invocation.method(
          #clearAll,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<bool> hasToken() => (super.noSuchMethod(
        Invocation.method(
          #hasToken,
          [],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<bool> hasUserData() => (super.noSuchMethod(
        Invocation.method(
          #hasUserData,
          [],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);
}
