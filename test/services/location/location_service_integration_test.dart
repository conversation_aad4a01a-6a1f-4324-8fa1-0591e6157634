import 'package:flutter_test/flutter_test.dart';
import 'package:lucian_rides_app/core/di/service_locator.dart';
import 'package:lucian_rides_app/services/location/location_service.dart';

void main() {
  setUp(() async {
    // Initialize the service locator
    await setupServiceLocator();
  });

  tearDown(() {
    // Clean up the service locator
    getIt.reset();
  });

  test('LocationService should be registered in service locator', () {
    // Act
    final locationService = getIt<LocationService>();

    // Assert
    expect(locationService, isNotNull);
    expect(locationService, isA<LocationService>());
  });
}
